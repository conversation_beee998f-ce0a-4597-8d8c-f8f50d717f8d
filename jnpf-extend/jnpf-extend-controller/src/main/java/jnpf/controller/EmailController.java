package jnpf.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.Operation;
import jnpf.base.ActionResult;
import jnpf.base.model.MailAccount;
import jnpf.base.util.Pop3Util;
import jnpf.base.vo.PaginationVO;
import jnpf.base.service.SysconfigService;
import jnpf.base.entity.EmailConfigEntity;
import jnpf.base.entity.EmailReceiveEntity;
import jnpf.constant.MsgCode;
import jnpf.entity.EmailSendEntity;
import jnpf.exception.DataException;
import jnpf.model.email.*;
import jnpf.service.EmailReceiveService;
import jnpf.util.JsonUtil;
import jnpf.util.JsonUtilEx;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

/**
 * 邮件配置
 *
 * <AUTHOR>
 * @version V3.1.0
 * @copyright 引迈信息技术有限公司（https://www.jnpfsoft.com）
 * @date 2019年9月26日 上午9:18
 */
@Tag(name = "邮件收发", description = "Email")
@RestController
@RequestMapping("/api/extend/Email")
public class EmailController {

    @Autowired
    private EmailReceiveService emailReceiveService;
    @Autowired
    private Pop3Util pop3Util;
    @Autowired
    private SysconfigService sysconfigService;

    /**
     * 获取邮件列表(收件箱、标星件、草稿箱、已发送)
     *
     * @param paginationEmail 分页模型
     * @return
     */
    @Operation(summary = "获取邮件列表(收件箱、标星件、草稿箱、已发送)")
    @GetMapping
    @SaCheckPermission("extend.email")
    public ActionResult receiveList(PaginationEmail paginationEmail) {
        String type = paginationEmail.getType() != null ? paginationEmail.getType() : "inBox";
        switch (type) {
            case "inBox":
                List<EmailReceiveEntity> entity = emailReceiveService.getReceiveList(paginationEmail);
                PaginationVO paginationVO = JsonUtil.getJsonToBean(paginationEmail, PaginationVO.class);
                List<EmailReceiveListVO> listVO = JsonUtil.getJsonToList(entity, EmailReceiveListVO.class);
                return ActionResult.page(listVO,paginationVO);
            case "star":
                List<EmailReceiveEntity> entity1 = emailReceiveService.getStarredList(paginationEmail);
                PaginationVO paginationVo1 = JsonUtil.getJsonToBean(paginationEmail, PaginationVO.class);
                List<EmailStarredListVO> listVo1 = JsonUtil.getJsonToList(entity1, EmailStarredListVO.class);
                return ActionResult.page(listVo1,paginationVo1);
            case "draft":
                List<EmailSendEntity> entity2 = emailReceiveService.getDraftList(paginationEmail);
                PaginationVO paginationVo2 = JsonUtil.getJsonToBean(paginationEmail, PaginationVO.class);
                List<EmailDraftListVO> listVo2 = JsonUtil.getJsonToList(entity2, EmailDraftListVO.class);
                return ActionResult.page(listVo2,paginationVo2);
            case "sent":
                List<EmailSendEntity> entity3 = emailReceiveService.getSentList(paginationEmail);
                PaginationVO paginationVo3 = JsonUtil.getJsonToBean(paginationEmail, PaginationVO.class);
                List<EmailSentListVO> listVo3 = JsonUtil.getJsonToList(entity3, EmailSentListVO.class);
                return ActionResult.page(listVo3,paginationVo3);
            default:
                return ActionResult.fail(MsgCode.ETD106.get());
        }
    }

    /**
     * 获取邮箱配置
     *
     * @return
     */
    @Operation(summary = "获取邮箱配置")
    @GetMapping("/Config")
    @SaCheckPermission("extend.email")
    public ActionResult<EmailCofigInfoVO> configInfo() {
        EmailConfigEntity entity = emailReceiveService.getConfigInfo();
        EmailCofigInfoVO vo = JsonUtil.getJsonToBean(entity, EmailCofigInfoVO.class);
        if(vo==null){
            vo=new EmailCofigInfoVO();
        }
        return ActionResult.success(vo);
    }

    /**
     * 获取邮件信息
     *
     * @param id 主键
     * @return
     */
    @Operation(summary = "获取邮件信息")
    @GetMapping("/{id}")
    @Parameters({
            @Parameter(name = "id", description = "主键",required = true),
    })
    @SaCheckPermission("extend.email")
    public ActionResult<EmailInfoVO> info(@PathVariable("id") String id) throws DataException {
        Object entity = emailReceiveService.getInfo(id);
        EmailInfoVO vo = JsonUtil.getJsonToBeanEx(entity, EmailInfoVO.class);
        return ActionResult.success(vo);
    }

    /**
     * 删除
     *
     * @param id 主键
     * @return
     */
    @Operation(summary = "删除邮件")
    @DeleteMapping("/{id}")
    @Parameters({
            @Parameter(name = "id", description = "主键",required = true),
    })
    @SaCheckPermission("extend.email")
    public ActionResult delete(@PathVariable("id") String id) {
       boolean flag= emailReceiveService.delete(id);
        if(flag==false){
            return ActionResult.fail(MsgCode.FA003.get());
        }
        return ActionResult.success(MsgCode.SU003.get());
    }

    /**
     * 设置已读邮件
     *
     * @param id 主键
     * @return
     */
    @Operation(summary = "设置已读邮件")
    @PutMapping("/{id}/Actions/Read")
    @Parameters({
            @Parameter(name = "id", description = "主键",required = true),
    })
    @SaCheckPermission("extend.email")
    public ActionResult receiveRead(@PathVariable("id") String id) {
        boolean flag= emailReceiveService.receiveRead(id, 1);
        if(flag==false){
            return ActionResult.fail(MsgCode.FA007.get());
        }
        return ActionResult.success(MsgCode.SU005.get());
    }

    /**
     * 设置未读邮件
     *
     * @param id 主键
     * @return
     */
    @Operation(summary = "设置未读邮件")
    @PutMapping("/{id}/Actions/Unread")
    @Parameters({
            @Parameter(name = "id", description = "主键",required = true),
    })
    @SaCheckPermission("extend.email")
    public ActionResult receiveUnread(@PathVariable("id") String id) {
        boolean flag= emailReceiveService.receiveRead(id, 0);
        if(flag==false){
            return ActionResult.fail(MsgCode.FA007.get());
        }
        return ActionResult.success(MsgCode.SU005.get());
    }

    /**
     * 设置星标邮件
     *
     * @param id 主键
     * @return
     */
    @Operation(summary = "设置星标邮件")
    @PutMapping("/{id}/Actions/Star")
    @Parameters({
            @Parameter(name = "id", description = "主键",required = true),
    })
    @SaCheckPermission("extend.email")
    public ActionResult receiveYesStarred(@PathVariable("id") String id) {
        boolean flag= emailReceiveService.receiveStarred(id, 1);
        if(flag==false){
            return ActionResult.fail(MsgCode.FA007.get());
        }
        return ActionResult.success(MsgCode.SU005.get());
    }

    /**
     * 设置取消星标
     *
     * @param id 主键
     * @return
     */
    @Operation(summary = "设置取消星标")
    @PutMapping("/{id}/Actions/Unstar")
    @Parameters({
            @Parameter(name = "id", description = "主键",required = true),
    })
    @SaCheckPermission("extend.email")
    public ActionResult receiveNoStarred(@PathVariable("id") String id) {
        boolean flag= emailReceiveService.receiveStarred(id, 0);
        if(flag==false){
            return ActionResult.fail(MsgCode.FA007.get());
        }
        return ActionResult.success(MsgCode.SU005.get());
    }

    /**
     * 收邮件
     *
     * @return
     */
    @Operation(summary = "收邮件")
    @PostMapping("/Receive")
    @SaCheckPermission("extend.email")
    public ActionResult receive() {
        EmailConfigEntity configEntity = emailReceiveService.getConfigInfo();
        if (configEntity != null) {
            MailAccount mailAccount = new MailAccount();
            mailAccount.setAccount(configEntity.getAccount());
            mailAccount.setPassword(configEntity.getPassword());
            mailAccount.setPop3Host(configEntity.getPop3Host());
            mailAccount.setPop3Port(configEntity.getPop3Port());
            mailAccount.setSmtpHost(configEntity.getSmtpHost());
            mailAccount.setSmtpPort(configEntity.getSmtpPort());
            if ("1".equals(String.valueOf(configEntity.getEmailSsl()))) {
                mailAccount.setSsl(true);
            } else {
                mailAccount.setSsl(false);
            }
            String checkResult=pop3Util.checkConnected(mailAccount);
            if ("true".equals(checkResult)) {
                int mailCount = emailReceiveService.receive(configEntity);
                return ActionResult.success(MsgCode.SU005.get(), mailCount);
            } else {
                return ActionResult.fail(MsgCode.ETD107.get());
            }
        } else {
            return ActionResult.fail(MsgCode.ETD108.get());
        }
    }

    /**
     * 存草稿
     *
     * @param emailSendCrForm 邮件模型
     * @return
     */
    @Operation(summary = "存草稿")
    @PostMapping("/Actions/SaveDraft")
    @Parameters({
            @Parameter(name = "emailSendCrForm", description = "邮件模型",required = true),
    })
    @SaCheckPermission("extend.email")
    public ActionResult saveDraft(@RequestBody @Valid EmailSendCrForm emailSendCrForm) {
        EmailSendEntity entity = JsonUtil.getJsonToBean(emailSendCrForm, EmailSendEntity.class);
        emailReceiveService.saveDraft(entity);
        return ActionResult.success(MsgCode.SU002.get());
    }

    /**
     * 发邮件
     *
     * @param emailCrForm 发送邮件模型
     * @return
     */
    @Operation(summary = "发邮件")
    @PostMapping
    @Parameters({
            @Parameter(name = "emailCrForm", description = "发送邮件模型",required = true),
    })
    @SaCheckPermission("extend.email")
    public ActionResult saveSent(@RequestBody @Valid EmailCrForm emailCrForm) {
        EmailSendEntity entity = JsonUtil.getJsonToBean(emailCrForm, EmailSendEntity.class);
        EmailConfigEntity configEntity = emailReceiveService.getConfigInfo();
        if (configEntity != null) {
            MailAccount mailAccount = new MailAccount();
            mailAccount.setAccount(configEntity.getAccount());
            mailAccount.setPassword(configEntity.getPassword());
            mailAccount.setPop3Host(configEntity.getPop3Host());
            mailAccount.setPop3Port(configEntity.getPop3Port());
            mailAccount.setSmtpHost(configEntity.getSmtpHost());
            mailAccount.setSmtpPort(configEntity.getSmtpPort());
            if ("1".equals(String.valueOf(configEntity.getEmailSsl()))) {
                mailAccount.setSsl(true);
            } else {
                mailAccount.setSsl(false);
            }
            int flag = emailReceiveService.saveSent(entity, configEntity);
            if (flag == 0) {
                return ActionResult.success(MsgCode.SU012.get());
            } else {
                return ActionResult.fail(MsgCode.ETD107.get());
            }
        } else {
            return ActionResult.fail(MsgCode.ETD108.get());
        }
    }

    /**
     * 更新邮件配置
     *
     * @param emailCheckForm 邮件配置模型
     * @return
     */
    @Operation(summary = "更新邮件配置")
    @PutMapping("/Config")
    @Parameters({
            @Parameter(name = "emailCheckForm", description = "邮件配置模型",required = true),
    })
    @SaCheckPermission("extend.email")
    public ActionResult saveConfig(@RequestBody @Valid EmailCheckForm emailCheckForm) throws DataException {
        EmailConfigEntity entity = JsonUtil.getJsonToBean(emailCheckForm, EmailConfigEntity.class);
        emailReceiveService.saveConfig(entity);
        return ActionResult.success(MsgCode.SU002.get());
    }

    /**
     * 邮箱配置-测试连接
     *
     * @param emailCheckForm 邮件配置模型
     * @return
     */
    @Operation(summary = "邮箱配置-测试连接")
    @PostMapping("/Config/Actions/CheckMail")
    @Parameters({
            @Parameter(name = "emailCheckForm", description = "邮件配置模型",required = true),
    })
    @SaCheckPermission("extend.email")
    public ActionResult checkLogin(@RequestBody @Valid EmailCheckForm emailCheckForm) {
        EmailConfigEntity entity = JsonUtil.getJsonToBean(emailCheckForm, EmailConfigEntity.class);
        String result = sysconfigService.checkLogin(entity);
        if ("true".equals(result)) {
            return ActionResult.success(MsgCode.SU017.get());
        } else {
            return ActionResult.fail(MsgCode.ETD107.get());
        }
    }

}

#!/bin/bash

echo "==================================================="
echo "         冷库项目控制器详细测试执行脚本              "
echo "==================================================="

# 设置颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}开始执行详细测试...${NC}"

# 运行QuickMockTest
echo -e "\n${YELLOW}=== 执行快速Mock测试 ===${NC}"
mvn test -Dtest=QuickMockTest -Dmaven.test.failure.ignore=true -X | grep -E "(开始测试|测试数据|预期结果|Mock配置|响应状态码|响应内容|测试完成|PASSED|FAILED|ERROR)"

# 运行原始验证测试的前两个方法
echo -e "\n${YELLOW}=== 执行验证测试（部分方法） ===${NC}"
mvn test -Dtest=ColdStorageProjectControllerValidationTest#testCreateProject_ValidData_Success -Dmaven.test.failure.ignore=true -X | grep -E "(开始测试|测试数据|预期结果|Mock配置|响应状态码|响应内容|测试完成|PASSED|FAILED|ERROR)"

echo -e "\n${YELLOW}=== 执行空项目编号测试 ===${NC}"
mvn test -Dtest=ColdStorageProjectControllerValidationTest#testCreateProject_EmptyProjectCode_BusinessValidationError -Dmaven.test.failure.ignore=true -X | grep -E "(开始测试|测试数据|预期结果|Mock配置|响应状态码|响应内容|测试完成|PASSED|FAILED|ERROR)"

echo -e "\n${GREEN}=== 测试执行完成 ===${NC}"
echo "如需查看完整输出，请直接运行："
echo "mvn test -Dtest=QuickMockTest"
echo "或"  
echo "mvn test -Dtest=ColdStorageProjectControllerValidationTest"
AD101=æ¥å£æ æ³è®¿é®
AD102=ç³»ç»å¼å¸¸
AD103=æä½è¿äºé¢ç¹
AD104=æ²¡æè®¿é®æéï¼è¯·èç³»ç®¡çåææ
AD105=è®¤è¯å¤±è´¥ï¼æ æ³è®¿é®ç³»ç»èµæº
AD106=æ æåé¨è®¤è¯ï¼æ æ³è®¿é®ç³»ç»èµæº
COD001=éåæ¡ä»¶è¿æ»¤è·å¾ç®æ ä¸ºç©º
COPY001=å¤å¶åç§°é¿åº¦è¶è¿äºéå¶é¿åº¦
DB001=æ°æ®ç±»åç¼ç ä¸ç¬¦åæ åï¼è¯·æ³¨æå¤§å°åï¼ãMySQL , SQLServer , Oracle , DM , KingbaseES , PostgreSQL
DB002=è¯·æ£æ¥ 1ãè¿æ¥ä¿¡æ¯ 2ãç½ç»éä¿¡ 3ãæ°æ®åºæå¡å¯å¨ç¶æã è¯¦æï¼{0}
DB003=éè¿urlæ¾ä¸å°å¯¹åºæ°æ®åº
DB004=æ¥è¯¢ç»æéä¸ºç©ºã
DB005=æªæ¾å°å¯¹åºæ°æ®åºç±»å:{0}({1})
DB006=æªæ¾å°å¯¹åºæ°æ®ç±»åè½¬æ¢
DB007=å¯¼å¥è¡¨åå­å¨éå¤
DB008=å»ºè¡¨æ°æ®ä¸å½åæä½æ°æ®åºä¸å¹é: {0} -> {1}
DB009=æªæ¾å°è¡¨ä¿¡æ¯: {0}
DB010=æ°æ®åº{0}ï¼æªæ¾å°æ­¤è¡¨:{1}
DB011=èåä¸»é®ç±»ç¼ºå°â{0}âå­æ®µå¼
DB012=è¡¨ç¤ºå¯¹åºè·åæ°å¼å¤±è´¥
DB013=ç®åè¿æªæ¯æ{0}æ°æ®ç±»åï¼{1}
DB014=è¡¨ "{0}"ä¸­å­æ®µ "{1}" ä¸ºä¸»é®ï¼ä¸åè®¸æ°æ®ç±»å "{2}"
DB015=æªæ¾å°å­æ®µSQLè¯­å¥
DB016=æ²¡æåå§å­æ®µ
DB017=sqlå¼å¸¸ï¼{0}
DB018=è¯·å¨æ°æ®åºä¸­æ·»å å¯¹åºçæ°æ®è¡¨
DB019=æ·»å å¤±è´¥
DB101=ç³»ç»èªå¸¦è¡¨,ä¸åè®¸è¢«å é¤
DB102=ç³»ç»èªå¸¦è¡¨,ä¸åè®¸è¢«ç¼è¾
DB201=è¡¨å·²ç»è¢«ä½¿ç¨,ä¸åè®¸è¢«å é¤
DB202=è¡¨å·²ç»è¢«ä½¿ç¨,ä¸åè®¸è¢«ç¼è¾
DB301=æ°æ®åºè¿æ¥æå
DB302=æ°æ®åºè¿æ¥å¤±è´¥
ETD101=æä½å¤±è´¥ï¼åæä»¶ä¸å­å¨
ETD102=æ¾ä¸å°ç¶çº§
ETD103=ä¸è½ç§»å¨å°èªå·±çæä»¶å¤¹
ETD104=æªè½æ¾å°æ­¤è®¢å
ETD105=æ°å»ºæå10000æ¡æ°æ®
ETD106=è·åå¤±è´¥
ETD107=è´¦æ·è®¤è¯éè¯¯
ETD108=ä½ è¿æ²¡æè®¾ç½®é®ä»¶çå¸æ·
ETD109=æä»¶å¯¼åºå¤±è´¥
ETD110=æä»¶æ ¼å¼ä¸æ­£ç¡®
ETD111=æä»¶æ¾ä¸å°
ETD112=æ­¤è®°å½è¢«å³èå¼ç¨,ä¸åè®¸è¢«å é¤
ETD113=é²æ­¢æ¶æåå»ºè¿å¤æ°æ®
ETD114=ä¿å­å¤±è´¥ï¼è¯·éæ°ç»é
ETD115=è¯·è¾å¥é¢è§çurl
ETD116=è¯·éæ©æ­£ç¡®çé¢è§æ¹å¼
ETD117=æ°æ®è¶è¿1000æ¡
EXIST001=åç§°ä¸è½éå¤
EXIST002=ç¼ç ä¸è½éå¤
EXIST003=æ¨¡æ¿åå·²å­å¨
EXIST004=æä»¶å¤¹åç§°ä¸è½éå¤
EXIST005=æ¨¡æ¿åç§°è¶è¿äºéå¶é¿åº¦
EXIST101=åç§°éå¤ï¼è¯·éæ°è¾å¥
EXIST102=ç¼ç éå¤ï¼è¯·éæ°è¾å¥
EXIST103=ä¸è½éå¤
FA001=æ°æ®ä¸å­å¨
FA002=æ´æ°å¤±è´¥ï¼æ°æ®ä¸å­å¨
FA003=å é¤å¤±è´¥ï¼æ°æ®ä¸å­å¨
FA004=å¤å¶å¤±è´¥ï¼æ°æ®ä¸å­å¨
FA005=åéå¤±è´¥ï¼æ°æ®ä¸å­å¨
FA006=ä¸è½½å¤±è´¥ï¼æ°æ®ä¸å­å¨
FA007=æä½å¤±è´¥ï¼æ°æ®ä¸å­å¨
FA008=åæ­¢å¤±è´¥ï¼æ°æ®ä¸å­å¨
FA009=ç»æ­¢å¤±è´¥ï¼æ°æ®ä¸å­å¨
FA010=è¿åå¤±è´¥ï¼æ°æ®ä¸å­å¨
FA011=åå¸å¤±è´¥ï¼æ°æ®ä¸å­å¨
FA012=è·åå¤±è´¥ï¼æ°æ®ä¸å­å¨
FA013=æ¥å£ä¿®æ¹å¤±è´¥ï¼æ°æ®ä¸å­å¨
FA014=æ´æ°æ¥å£ç¶æå¤±è´¥ï¼æ°æ®ä¸å­å¨
FA015=é¢è§å¤±è´¥ï¼æ°æ®ä¸å­å¨
FA016=å é¤å¤±è´¥ï¼è¯¥æä»¶å¤¹å­å¨æ°æ®
FA017=æä»¶æ ¼å¼ä¸æ­£ç¡®
FA018=æä»¶ä¸å­å¨
FA019=å·²å¤±æ
FA020=æªæ¥å°ä¿¡æ¯
FA021=æä½å¤±è´¥ï¼æ¨æ²¡ææéæä½
FA022=æ´æ°å¤±è´¥ï¼æ¨æ²¡ææéæä½ (è§è²åªæè¶çº§ç®¡çåæè½å¤æä½)
FA023=æ´æ°å¤±è´¥ï¼å·²ç»å®ç¨æ·ï¼æ æ³åæ¢ç»ç»
FA024=å é¤å¤±è´¥ï¼å·²ç»å®ç¨æ·
FA025=è¯¥ç»ç»åæéä¸ºç©ºï¼ç»ç»åæ¢å¤±è´¥ï¼
FA026=æ´æ°å¤±è´¥ï¼å³èç»ç»ä¸å­å¨ï¼è¯·éæ°ç»å½ï¼æèå·æ°é¡µé¢
FA027=è¯¥ç³»ç»ä¸èåä¸ºç©ºï¼ç³»ç»åæ¢å¤±è´¥
FA028=æ°å¢æ°æ®å¤±è´¥
FA029=ä¿®æ¹æ°æ®å¤±è´¥
FA030=æ´æ°å¤±è´¥ï¼å·²ç»å®ç¨æ·ï¼æ æ³ä¿®æ¹ç¶æ
FA031=è¯¥ç»ç»æ æ¬åºç¨æéï¼åæ¢å¤±è´¥
FA032=ä¸ä¼ æä»¶ä¸è½ä¸ºç©º
FA033=æä»¶ä¸ä¼ å¤±è´¥ï¼
FA034=éæ³è¯·æ±, ç¼ºå°è®¤è¯ä¿¡æ¯
FA035=æªè·åå°ç§æ·æå®æ°æ®æºä¿¡æ¯
FA036=å¸¸ç¨æ°æ®å·²å­å¨
FA037=æ¥å£è¯·æ±å¤±è´¥
FA038=æä»¶å­å¨è·¯å¾éè¯¯
FA039=é¾æ¥å·²å¤±æ
FA040=é¢è§å¤±è´¥,è¯·æ£æ¥æä»¶ç±»åæ¯å¦è§è
FA041=é¢è§å¤±è´¥,è¯·éæ°ä¸ä¼ æä»¶
FA042=è¯·è¾å¥æ­£ç¡®çæä»¶æ ¼å¼
FA043=å­å¨ååæä»¶ï¼
FA044=ä¸å­å¨è¯¥æä»¶
FA045=å é¤æä»¶:{0}å¤±è´¥
FA046=æä»¶è¯»åå¤±è´¥
FA047=æªåç°æä»¶
FA048=å¾®ä¿¡å¬ä¼å·åå§idä¸è½éå¤
FA049=æ­¤è®°å½ä¸âæ¶æ¯åééç½®âå³èå¼ç¨ï¼ä¸åè®¸è¢«ç¦ç¨
FA050=æ­¤è®°å½ä¸âæ¶æ¯åééç½®âå³èå¼ç¨ï¼ä¸åè®¸è¢«å é¤
FA051=æ¥å£å·²éç½®å å¯, æ°æ®è§£å¯å¤±è´¥.
FA052=è¯¥èº«ä»½æªåéæé
FA101=ä¿å­å¤±è´¥
FA102=æ´æ°å¤±è´¥
FA103=å é¤å¤±è´¥
FA104=è·åå¤±è´¥
FA105=é¢è§å¤±è´¥,è¯·åä¿å­å¨é¢è§æ°æ®
FA106=é¢è§å¤±è´¥,ååæ ¼éç½®åºç°æ­»å¾ªç¯
FM001=æªæ¾å°æ¥å£
FM002=è¡¨åä¿¡æ¯ä¸å­å¨
FM003=å­è¡¨éå¤
FM004=å·²å°è¾¾è¯¥æ¨¡æ¿å¤å¶ä¸éï¼è¯·å¤å¶æºæ¨¡æ¿!
FM005=è¯¥è¡¨åå·²è¢«æµç¨å¼ç¨ï¼æ æ³å é¤ï¼
FM006=è¯¥è¡¨åæªåå¸ï¼æ æ³åæ»è¡¨ååå®¹
FM007=è¯¥æ¨¡æ¿åè¡¨ååå®¹ä¸ºç©ºï¼æ æ³åå¸
FM008=è¯¥åè½æªå¯¼å¥æµç¨è¡¨å
FM009=æµç¨æªè®¾è®¡ï¼è¯·åè®¾è®¡æµç¨ï¼
FM010=è¯¥åè½æµç¨å¤äºåç¨ç¶æï¼
FM011=è¡¨[{0}]æ ä¸»é®!
FM012=ä¸»é®ç­ç¥:{0}ï¼ä¸è¡¨[{1}]ä¸»é®ç­ç¥ä¸ä¸è´!
FM013=è¡¨æ°å¢éè¯¯:{0}
GT101=æå
GT102=å¤±è´¥
GT103=éªè¯éè¯¯
GT104=å¼å¸¸
GT105=ç»å½è¿æ,è¯·éæ°ç»å½
GT106=æ¨çå¸å·å¨å¶ä»å°æ¹å·²ç»å½,è¢«å¼ºå¶è¸¢åº
GT107=Tokenéªè¯å¤±è´¥
GT108=è¯·æ±è¶è¿æå¤§æ°
IMP001=å¯¼å¥æå
IMP002=å¯¼å¥å¤±è´¥ï¼æä»¶æ ¼å¼éè¯¯
IMP003=å¯¼å¥å¤±è´¥ï¼æ°æ®å·²å­å¨
IMP004=å¯¼å¥å¤±è´¥ï¼æ°æ®æè¯¯
IMP005=å¯¼åºå¤±è´¥
IMP006=å¯¼å¥æ°æ®æ ¼å¼ä¸æ­£ç¡®
IMP007=éå¤
IMP008=åç§°
IMP009=ç¼ç 
IMP010=å¯¼å¥å¤±è´¥ï¼æ¥è¯¢ä¸å°ä¸çº§åç±»
IMP011=è¯·éæ©å¯¼åºå­æ®µ
LOG001=è´¦æ·å¼å¸¸
LOG002=æ³¨éæå
LOG004=è´¦å·å¼å¸¸ï¼è¯·èç³»ç®¡çåä¿®æ¹æå±ç»ç»ä¿¡æ¯
LOG005=è´¦å·æªè¢«æ¿æ´»
LOG006=è´¦å·å·²è¢«ç¦ç¨
LOG007=è´¦å·å·²è¢«å é¤
LOG010=æ­¤IPæªå¨ç½ååä¸­
LOG011=ç»å½å¤±è´¥ï¼ç¨æ·ææªç»å®è§è²
LOG012=è´¦å·å·²è¢«éå®ï¼è¯·èç³»ç®¡çåè§£é¤éå®
LOG101=è´¦å·æå¯ç éè¯¯
LOG102=è´¦å·æè¯¯ï¼è¯·éæ°è¾å¥
LOG103=è¯·è¾å¥éªè¯ç 
LOG104=éªè¯ç éè¯¯
LOG105=è¿æ¥ç§æ·æå¡å¤±è´¥ï¼è¯·ç¨ååè¯
LOG106=ç­ä¿¡éªè¯ç éè¯¯
LOG107=éªè¯ç å·²å¤±æ
LOG108=è¯·ç­å¾{0}åéååè¿è¡ç»å½ï¼æèç³»ç®¡çåè§£é¤éå®
LOG109=ç§æ·ç»å½å¤±è´¥ï¼è¯·ç¨ææºéªè¯ç ç»å½
LOG110=æ°æ®åºå¼å¸¸ï¼è¯·èç³»ç®¡çåå¤ç
LOG111=å·²å¼å¯åç¹ç»å½, ä¸æ¯ææ­¤ç»å½æ¹å¼
LOG112=ä¸æ¯ææ­¤ç»å½æ¹å¼
LOG113=æªè®¾ç½®ç§æ·ä¿¡æ¯
LOG114=ç§æ·ç¼ç ä¸åè®¸ä¸ºç©º
LOG115=ç§æ·ä¿¡æ¯è·åå¤±è´¥
LOG116=ä¸æ¯ææ­¤éªè¯
LOG117=ç­ä¿¡éªè¯ç éªè¯å¤±è´¥ï¼{0}
LOG118=ç§æ·åºåä¸ºç©º
LOG201=æ§å¯ç éè¯¯
LOG202=ä¿®æ¹æåï¼è¯·ç¢è®°æ°å¯ç 
LOG203=ä¿®æ¹å¤±è´¥ï¼è´¦å·ä¸å­å¨
LOG204=ä¿®æ¹å¤±è´¥ï¼æ°å»ºå¯ç ä¸è½ä¸æ§å¯ç ä¸æ ·
LOG205=éç½®å¯ç æå
LOG206=éç½®å¯ç å¤±è´¥
MSERR101=åéå¤±è´¥ï¼å¤±è´¥åå ï¼SMTPæå¡ä¸ºç©º
MSERR102=åéå¤±è´¥ï¼å¤±è´¥åå ï¼åä»¶äººé®ç®±ä¸ºç©º
MSERR103=åéå¤±è´¥ï¼å¤±è´¥åå ï¼åä»¶äººå¯ç ä¸ºç©º
MSERR104=åéå¤±è´¥ï¼å¤±è´¥åå ï¼æ¥æ¶äººä¸ºç©º
MSERR105=åéå¤±è´¥ãå¤±è´¥åå ï¼{0}çé®ç®±è´¦å·æ ¼å¼æè¯¯ï¼
MSERR106=åéå¤±è´¥ãå¤±è´¥åå ï¼{0}çé®ç®±è´¦å·ä¸ºç©ºï¼
MSERR107=åéå¤±è´¥ãå¤±è´¥åå ï¼æ¥æ¶äººå¯¹åºçé®ç®±å¨é¨ä¸ºç©º
MSERR108=åéå¤±è´¥ãå¤±è´¥åå ï¼{0}
MSERR109=è¿æ¥æå
MSERR110=è¿æ¥å¤±è´¥ãå¤±è´¥åå ï¼{0}
MSERR111=å·²åé
MSERR112=åå®¹ä¸è½åå«<ç¬¦å·
MSERR113=ææ æªè¯»æ¶æ¯
MSERR114=èªå®ä¹æ¨¡æ¿ç¼ç ä¸è½ä½¿ç¨ç³»ç»æ¨¡æ¿ç¼ç è§å
MSERR115=åå»ºå¤±è´¥ï¼å­å¨å¤ä¸ªæ é¢åæ°
MSERR116=åå»ºå¤±è´¥ï¼ä¸å­å¨æ é¢åæ°
MSERR117=æ´æ°å¤±è´¥ï¼å­å¨å¤ä¸ªæ é¢åæ°
MSERR118=æ´æ°å¤±è´¥ï¼ä¸å­å¨æ é¢åæ°
MSERR119=è¯·ååå¾ç³»ç»åæ­¥è®¾ç½®ï¼éç½®ééè´¦å·
MSERR120=è¯·ååå¾ç³»ç»åæ­¥è®¾ç½®ï¼éç½®ä¼ä¸å¾®ä¿¡è´¦å·
MSERR121=éç½®æ¨¡æ¿æ æ°æ®ï¼æ æ³æµè¯
OA001=ç¨æ·ç»å½
OA002=è®¾å¤
OA003=TOKEN
OA004=ç¨æ·éåº
OA005=ç¨æ·è¸¢åº
OA006=ç¨æ·é¡¶æ¿
OA007=ç»å½å¼å¸¸
OA008=ç»å½è·åç³»ç»éç½®å¤±è´¥
OA009=è¯¥ç¨æ·æªåéæé
OA010=ä»æ¯æpcç«¯è®¿é®,APPç«¯ä¸æ¯æ
OA011=åºç¨ä¸å­å¨
OA012=å½ååºç¨å·²è¢«ç¦ç¨
OA013=ç»å½å¯ç è§£å¯å¤±è´¥
OA014=æ³¨éæå
OA015=ç»å½æå
OA016=ç»å½ç¥¨æ®å·²å¤±æ
OA017=ç¬¬ä¸æ¹æªç»å®è´¦å·
OA018=ä¸åè®¸è®¿é®æ­¤ç»å½æ¥å£
OA019=è´¦å·ä¸å­å¨
OA020=è´¦æ·æå¯ç éè¯¯ï¼è¯·éæ°è¾å¥
OA021=éªè¯æå
OA022=éå¶ä¼è¯, ä¸åè®¸è®¿é®ç³»ç»
OA023=ç®¡çåä¸è½æ³¨é
OA024=ç»å½å¤±è´¥
OA025=è¶çº§ç®¡çå
OA026=æ®éç®¡çå
OA027=æ®éç¨æ·
OA028=æªç¥æ¥æº
PRI001=æå°æ¨¡æ¿ä¸å­å¨
PRI002=æ°å­å­å¸ä¸å­å¨printDevçå­å¸åç±»
PRI003=ç¬¬1æ¡SQLè¯­å¥ï¼æ¥è¯¢åºå¤æ¡è¡¨å¤´ä¿¡æ¯
PRI004=ç¬¬1æ¡SQLè¯­å¥ï¼æªæ¥åºè¡¨å¤´ä¿¡æ¯
PRI005=ç¬¬{index}æ¡SQLè¯­å¥ï¼
PRI006=å·²å°è¾¾è¯¥æ¨¡æ¿å¤å¶ä¸éï¼è¯·å¤å¶æºæ¨¡æ¿
PRI007=Sqlè¯­æ³éè¯¯
PRI008=è¯¥æ¥è¡¨å·²å é¤
PS001=æ­¤è®°å½ä¸"{0}"å³èå¼ç¨ï¼ä¸åè®¸è¢«å é¤
PS003=ç»ç»
PS004=å²ä½
PS005=ç¨æ·
PS006=è§è²
PS007=è´¦å·ä¸è½ä¸ºç©º
PS008=å§åä¸è½ä¸ºç©º
PS009=ç¨æ·é¢åº¦å·²è¾¾å°ä¸é
PS010=æéå·²åæ´ï¼è¯·éæ°ç»å½
PS011=å¯ç å·²åæ´ï¼è¯·éæ°ç»å½
PS012=ç±»åä¸è½ä¸ºç©º
PS013=å½åæºæIdä¸è½ä¸ç¶æºæIdç¸å
PS014=è¯¥åºç¨å·²ç¦ç¨
PS015=æ æ³è®¾ç½®å½åç¨æ·ä¸ºåçº§ç®¡çå
PS016=æ æ³è®¾ç½®è¶ç®¡ä¸ºåçº§ç®¡çå
PS017=æ æ³è®¾ç½®å½åç¨æ·æä½æé
PS018=è§£ç»å¤±è´¥
PS019=ç¬¬ä¸æ¹ç»å½æªéç½®
PS020=æ§å«ä¸è½ä¸ºç©º
PS021=æ æ³ç¦ç¨ç®¡çåç¨æ·
PS022=ç®¡çååªè½ä¿®æ¹èªå·±ï¼ä¸è½ä¿®æ¹å¶ä»ç®¡çå
PS023=æ æ³ä¿®æ¹ç®¡çåè´¦æ·
PS024=ç´å±ä¸»ç®¡ä¸è½æ¯èªå·±
PS025=ç´å±ä¸»ç®¡ä¸è½æ¯æçä¸å±ç¨æ·
PS026=æ æ³å é¤ç®¡çåè´¦æ·
PS027=æ­¤ç¨æ·ä¸ºæé¨é¨ä¸»ç®¡ï¼æ æ³å é¤
PS028=æ­¤ç¨æ·æä¸å±ï¼æ æ³å é¤
PS029=æ æ³ä¿®æ¹ç®¡çåè´¦æ·ç¶æ
PS030=ç¨æ·ä¿¡æ¯å·²åæ´ï¼è¯·éæ°ç»å½
PS031=è¯¥åºç¨å·²å é¤
PS032=è¯¥ç»ç»æ æ¬åºç¨æé,åæ¢å¤±è´¥
PS033=å·¥ä½äº¤æ¥æå!
PS034=å·¥ä½äº¤æ¥æ æ³è½¬ç§»ç»ç®¡çå
PS035=å·¥ä½äº¤æ¥æ æ³è½¬ç§»ç»æ¬äºº
SC001=æä½å¤±è´¥ï¼ä»»å¡ä¸å­å¨
SU000=Success
SU001=æ°å»ºæå
SU002=ä¿å­æå
SU003=å é¤æå
SU004=æ´æ°æå
SU005=æä½æå
SU006=æäº¤æåï¼è¯·èå¿ç­å¾
SU007=å¤å¶æå
SU008=åæ­¢æå
SU009=ç»æ­¢æå
SU010=è¿åæå
SU011=åå¸æå
SU012=åéæå
SU013=æ¥å£ä¿®æ¹æå
SU014=æ´æ°æ¥å£ç¶ææå
SU015=ä¸ä¼ æå
SU016=è®¾ç½®æå
SU017=éªè¯æå
SU018=æ·»å æå
SU019=è·åæå
SU020=åæ»æå
SU021=ç§»é¤æå
SU022=æ¥è¯¢æå
SYS001=åºåç¼ç ä¸è½éå¤
SYS002=å é¤å¤±è´¥ï¼å½åæå­èç¹æ°æ®
SYS003=åæ®å·²ç»è¢«ä½¿ç¨,ä¸åè®¸è¢«å é¤
SYS004=æ¸çæå
SYS005=æ¥å£åå»ºæå
SYS006=å½åSQLå«æææå­:{0}
SYS007=æ¥å£è¯·æ±æå
SYS008=æ¥å£ä¸ç¬¦åè§è
SYS009=åéåä¸è½åå«ææå­ç¬¦
SYS010=åéåå·²å­å¨
SYS011=æ°æ®åºè¿æ¥ä¸è½ç¸å
SYS012=è¯·æ£æ¥ï¼åä¸æ°æ®åºä¸æ æ³åæ­¥æ°æ®
SYS013=åæ­¥å¤±è´¥:{0}
SYS014=å­å¸ç±»åä¸é¢æå­å¸å¼ç¦æ­¢å é¤
SYS015=æ¨¡æ¿ä¸å­å¨
SYS016=å½åç®å½å­å¨æ°æ®,ä¸è½ä¿®æ¹ç±»å
SYS017=å é¤å¤±è´¥ï¼è¯·åå é¤å­èå
SYS018=å½åå¯¼å¥èåä¸º{0}ç«¯èåï¼è¯·å¨å¯¹åºæ¨¡åä¸å¯¼å¥ï¼
SYS019=è¯·å¨é¡¶çº§èç¹ä¸åå»ºç®å½ååè¿è¡èåå¯¼å¥
SYS020=è¯¥å­æ®µå¨æ¹æ¡{0}ä¸­å·²è¢«ä½¿ç¨
SYS021=ä¿®æ¹å¤±è´¥ï¼è¯¥æ¹æ¡ä¸åè®¸ç¼è¾
SYS022=ç¼ç éè¯¯
SYS023=è¯·æ±åçéè¯¯ï¼
SYS024=è·åä¸å°æ°æ®ï¼
SYS025=è·åä¼ä¸å¾®ä¿¡access_tokenå¤±è´¥
SYS026=æ­£å¨è¿è¡åæ­¥ï¼è¯·ç¨ååè¯
SYS027=è¯·åä»ä¼ä¸å¾®ä¿¡åæ­¥é¨é¨å°æ¬å°
SYS028=è¯·åä»ééåæ­¥é¨é¨å°æ¬å°
SYS029=éªè¯ç ä½æ°ä¸è½å¤§äº6
SYS030=éªè¯ç ä½æ°ä¸è½å°äº3
SYS031=æµè¯åéæ¶æ¯çè¿æ¥å¤±è´¥ï¼{0}
SYS032=æµè¯åéæ¶æ¯è¿æ¥æå
SYS033=æµè¯ç»ç»åæ­¥çè¿æ¥å¤±è´¥ï¼{0}
SYS034=æµè¯ç»ç»åæ­¥è¿æ¥æå
SYS035=æµè¯è¿æ¥ç±»åéè¯¯
SYS036=æµè¯ééè¿æ¥å¤±è´¥ï¼
SYS037=æµè¯è¿æ¥æå
SYS038=è¡¨ä¿¡æ¯æ½åå¼å¸¸
SYS039=å é¤å¤±è´¥ï¼è¯·åå é¤è¯¥åºç¨ä¸çèååé¨æ·
SYS040=å é¤å¤±è´¥ï¼è¯·åå é¤è¯¥åºç¨ä¸çèå
SYS041=å é¤å¤±è´¥ï¼è¯·åå é¤è¯¥åºç¨ä¸çé¨æ·
SYS042=è¯¥æ¥ç¨å·²è¢«å é¤
SYS043=æåä¸æ¡æ°æ®ä¸è½å é¤
SYS044=å¯ç¨çæ¬ä¸è½å é¤
SYS045=å½æ¡£çæ¬ä¸è½å é¤
SYS046=æ°æ®éä¸è½éå
SYS047=SQLè¯­å¥ä»æ¯ææ¥è¯¢è¯­å¥
SYS048=SQLè¯­å¥éå¸¦ä¸@formIdæ¡ä»¶
SYS049=æ­£å¨è¿è¡åæ­¥,è¯·ç¨ç­
SYS050=åªè½è¾å¥å­æ¯ãæ°å­ãç¹ãæ¨ªçº¿åä¸åçº¿ï¼ä¸ä»¥å­æ¯å¼å¤´
SYS051=ç¿»è¯æ è®°ä¸è½éå¤
SYS052=ç¿»è¯è¯­è¨è³å°å¡«åä¸é¡¹
SYS053=è·åééaccess_tokenå¤±è´¥
SYS101=æ´æ°å¤±è´¥ï¼ä¸»ç³»ç»ä¸åè®¸ç¦ç¨
SYS102=ä¸»ç³»ç»ä¸åè®¸å é¤
SYS103=ç³»ç»å¨å®¡æ¹å¸¸ç¨è¯­ä¸­è¢«ä½¿ç¨ï¼ä¸åè®¸å é¤
SYS104=æ´æ°å¤±è´¥ï¼ä¸»ç³»ç»ä¸åè®¸ä¿®æ¹ç¼ç 
SYS105=å¸¸ç¨è¯­å·²å­å¨
SYS121=æ¥å£æåªæ¯æHTTPåHTTPSæ¹å¼
SYS122=æ¥å£è¯·æ±å¤±è´¥
SYS123=æ¥å£è¯·æ±å¤±è´¥, JSè°ç¨å¤±è´¥,éè¯¯ï¼{0}
SYS124=éªè¯è¯·æ±è¶æ¶
SYS125=appSecretéè¯¯
SYS126=appIdä½¿ç¨æéå·²å°æ
SYS127=appIdåæ°éè¯¯
SYS128={0}ä¸è½ä½¿ç¨ç³»ç»ãå¼åè¯­è¨åæ°æ®åºå³é®å­å½å
SYS129=å½åæ°æ®æºä¸æ¯æå¨è¿æ¥
SYS130=æ é¢ä¸è½ä¸ºç©º
SYS131=ç»ææ¶é´å¿é¡»æäºå¼å§æ¶é´
SYS132=ç»æéå¤å¿é¡»æäºå¼å§æ¶é´
VS001=åæ­¥å°æµç¨æ¶ï¼{0}
VS002=åå¸å¤±è´¥ï¼æµç¨æªè®¾è®¡ï¼
VS003=æ è¡¨çææè¡¨å¤±è´¥
VS004=åå¸
VS005=é¢è§
VS006=ä¸è½½
VS007=åæ­¥æå
VS008=åæ»å¤±è´¥,ææ çº¿ä¸çæ¬
VS009=åæ°è§£æéè¯¯ï¼
VS010=æ æé¾æ¥
VS011=å¯ç éè¯¯
VS012=æªæ¾å°è¯¥åè½è¡¨å
VS013=æªå¼å¯è¡¨åå¤é¾ï¼
VS014=ä¸è½½é¾æ¥å·²å¤±æ
VS015=å­æ®µä¸è½ä¸ºç©º
VS016=è·¯å¾éè¯¯
VS017=éæå©æè¢«ç¦ç¨
VS018=è¡¨è§èåç§°ä¸è½éå¤
VS019=è§èåç§°ä¸è½ä½¿ç¨ç³»ç»å³é®å­æJAVAå³é®å­
VS020=å­æ®µè§èåç§°ä¸è½éå¤
VS021=â{0}âå½åä¸ç¬¦åè§è
VS022=ä¸»é®ç­ç¥:[éªè±ID],è¡¨[ {0} ]ä¸»é®è®¾ç½®ä¸æ¯æ!
VS023=ä¸»é®ç­ç¥:[èªå¢ID],è¡¨[ {0} ]ä¸»é®è®¾ç½®ä¸æ¯æ!
VS024=è¡¨åä¸å­å¨æèæªåå¸ï¼
VS025=æªè·åå°æµç¨åèµ·äºº
VS026=è§èåç§°åä¸¤å­æ¯å¿é¡»å°å
VS027=èªå¨çæçã{0}ãè¶åºé¿åº¦ï¼æäº¤å¤±è´¥ï¼
VS028=è§å¾æå¤æ°å»º5ä¸ª
VS401=è¯¥æ¨¡æ¿åè¡¨ååå®¹ä¸ºç©ºï¼æ æ³
VS402=è¯¥æ¨¡æ¿ååè¡¨åå®¹ä¸ºç©ºï¼æ æ³
VS403=è¯¥åè½æªéç½®æµç¨ä¸å¯ç¨
VS404=åè¡è¾å¥ä¸è½éå¤
VS405=å½åè¡¨ååæ°æ®å·²è¢«è°æ´ï¼è¯·éæ°è¿å¥è¯¥é¡µé¢ç¼è¾å¹¶æäº¤æ°æ®
VS406=è¯¥åè½éç½®çæµç¨å¤äºåç¨
VS407=è¡¨å¤´åç§°ä¸å¯æ´æ¹ï¼è¡¨å¤´è¡ä¸è½å é¤
VS408=è¯·è³å°éæ©ä¸ä¸ªæ°æ®è¡¨
VS409=æªæ¾å°ä¸»è¡¨ä¿¡æ¯
VS410=è¯·å¯¼å¥å¯¹åºåè½çjsonæä»¶
VS411=å·²å­å¨ç¸ååè½
VS412=è¯¥è¡¨åå·²å é¤
VS413=åºç¨ä¸è½ä¸ºç©º
VS414=é¨æ·æ°æ®ä¿¡æ¯å­å¨éå¤
VS415=è¯¥é¨æ·å·²å é¤
WF001=å®¡æ ¸æå
WF002=éåæå
WF003=è½¬åæå
WF004=å ç­¾æå
WF005=å½åæµç¨è¢«éåï¼æ æ³æ¤åæµç¨
WF006=æµç¨å·²æ¤åï¼ä¸è½éå¤æä½
WF007=æ¤åå¤±è´¥,è½¬åæ°æ®æ æ³æ¤å
WF008=æ¤åæå
WF009=åè½æµç¨ä¸è½ç»æ­¢
WF010=ææ´¾æå
WF011=æ¹éæä½å®æ
WF012=è¯¥æµç¨ä¸è½æä½
WF013=å¤æ´»æå
WF014=åæ´æå
WF015=æèµ·æå
WF016=æ¢å¤æå
WF017=å§æäººåè¢«å§æäººç¸åï¼å§æå¤±è´¥
WF018=æä½å¤±è´¥ï¼åä¸æ¶é´åæç¸åæµç¨çå§æ
WF019=æä½å¤±è´¥ï¼åä¸æ¶é´åæç¸åæµç¨ï¼ä¸è½ç¸äºå§æ
WF020=åè½æµç¨ä¸è½å é¤
WF021=ä¸è½å é¤
WF022=å¬åæå
WF023=æªæ¾å°å¬åäºº
WF024=è¯¥åè½å·²è¢«æµç¨å¼ç¨ï¼è¯·éæ°éæ©å³èåè½
WF025=å¯ç¨å¤±è´¥ï¼æµç¨æªè®¾è®¡
WF026=å¯ç¨æå
WF027=ç¦ç¨æå
WF028=è¯¥çæ¬åæå·¥åä»»å¡æµè½¬ï¼æ æ³å é¤
WF029=æ¨æ²¡æåèµ·è¯¥æµç¨çæé
WF030=è¡¨åæªæ¾å°
WF031=å·²å®¡æ ¸å®æ
WF032=å»ç»ä¸è½æä½
WF033=è½¬åèç¹ä¸å­å¨æéç½®éè¯¯
WF034=è½¬åå¤±è´¥ï¼è½¬åèç¹æªå®¡æ¹
WF035=éåè³æ¨çå®¡æ¹ï¼ä¸è½ååèµ·éå
WF036=æµç¨å·²å¤çï¼æ æ³æ¤å
WF037=å½åæµç¨åå«å­æµç¨ï¼æ æ³æ¤å
WF038=å­æµç¨æ æ³æ¤å
WF039=ä¸ä¸èç¹ä¸ºéæ©åæ¯æ æ³æ¹éå®¡æ¹
WF040=æ¡ä»¶æµç¨åå«åéäººæ æ³æ¹ééè¿
WF041=è¯¥æµç¨å·¥åå·²ç»æ­¢
WF042=è¯¥æµç¨å·¥åå·²æ¤å
WF043=è¯¥èç¹æ²¡ææ°æ®,æ æ³å¤æ´»
WF044=æ­¤æµç¨ä¸æ¯æåæ´
WF045=å½åèç¹æå­æµç¨æ æ³åæ´
WF046=éåèç¹åå«å­æµç¨ï¼éåå¤±è´¥
WF047=å½åèç¹æªå®¡æ¹ï¼ä¸è½éå
WF048=æµç¨å¤äºæèµ·ç¶æï¼ä¸å¯æä½
WF049=å½åæµç¨æ­£å¨è¿è¡ä¸è½å é¤
WF050=å·²è¢«æèµ·ä¸è½å é¤
WF051=æ²¡æå é¤æé
WF052=ä¸»çæ¬æ²¡æåå®¹
WF053=æµç¨æ²¡æå¯ç¨
WF054=æµç¨ç¼ç ä¸è½éå¤
WF055=æµç¨è¡¨åä¸ä¸è´ï¼è¯·éæ°éæ©
WF056=è¯¥æµç¨ç±å¨çº¿å¼åçæçï¼æ æ³ç´æ¥å é¤ï¼è¯·å¨åè½è®¾è®¡ä¸­å é¤ç¸å³åè½
WF057=è¯¥æµç¨åå·¥åä»»å¡æµè½¬æªç»æï¼æ æ³å é¤
WF058=å½åæµç¨æ­£å¨è¿è¡ä¸è½éå¤æäº¤
WF059=æµç¨èªå¨åèµ·å®¡æ¹å¤±è´¥
WF060=é©³åèç¹ä¸è½æ¯å­æµç¨
WF061=ä¸ä¸èç¹æ å®¡æ¹äººåè¯·èç³»ç®¡çå
WF062=è¡¨åå·²è¢«å¼ç¨ï¼è¯·éæ°éæ©
WF063=æµç¨å·²åèµ·ï¼æ æ³å é¤
WF064=ä»»å¡ä¸å­å¨,æèå·²å¤ç
WF065=æç»æå
WF066=åææå
WF067=ååæå
WF068=ååä¿å­æå
WF069=åç­¾æå
WF070=æ¤éæå
WF071=æåä¸æ¡æ°æ®ä¸è½å é¤
WF072=å¯ç¨çæ¬ä¸è½å é¤
WF073=å½æ¡£çæ¬ä¸è½å é¤
WF074=æåæå
WF075=æ¡ä»¶ä¸æ»¡è¶³æ æ³æµè½¬
WF076=èç¹ä¸å­å¨
WF077=æµç¨æ æ³æ¤å
WF078=æµç¨æªåæï¼æ æ³æ¤é
WF079=å½æ¡£å¼å¸¸
WF080=éæ©çæ°æ®ä¸è½éç­¾
WF081=æ æ³å ç­¾
WF082=æ æ³åç­¾
WF083=æ æ³éå
WF084=æ æ³è½¬å®¡
WF085=æ æ³åå
WF086=æ æ³æ¹éå®¡æ¹
WF087=ç»åæªç­¾æ¶
WF088=ç»åæªå¼å§åç
WF089=æµç¨åå¸å¤±è´¥
WF090=æµç¨åå¸å¤±è´¥
WF091=æµç¨æäº¤å¤±è´¥
WF092=è·åå¼æå½åä»»å¡å¤±è´¥
WF093=æµç¨å é¤å¤±è´¥
WF094=è·ååºçº¿éåå¤±è´¥
WF095=è·åçº¿ä¹åçä»»å¡èç¹å¤±è´¥
WF096=è·åä¸ä¸çº§ä»»å¡èç¹éåå¤±è´¥
WF097=è·åä¸ä¸çº§ä»»å¡èç¹éåå¤±è´¥
WF098=ä»»å¡å®æå¤±è´¥
WF099=è·åæµç¨å®ä¾å¤±è´¥
WF100=è·åæªç»è¿çèç¹å¤±è´¥
WF101=è·åèç¹çåç»­èç¹å¤±è´¥
WF102=è·åå¯åéçèç¹å¤±è´¥
WF103=éåå¤±è´¥
WF104=èç¹è·³è½¬å¤±è´¥
WF105=è¡¥å¿å¤±è´¥
WF106=ä¸è½å ç­¾ç»èªå·±
WF107=ä¸è½è½¬å®¡ç»èªå·±
WF108=ä¸è½ååç»èªå·±
WF109=å¿é¡»ä¿çä¸åå ç­¾äººå
WF110=å®¡æ¹å¼å¸¸æ æ³æ¤é
WF111=æéæµç¨åå«æ¡ä»¶åéäºº
WF112=éæ©çæ°æ®å¯¹åºæµç¨å·²æå
WF113=å·²è¢«æåä¸è½å é¤
WF114=æµç¨å¤äºæåç¶æï¼ä¸å¯æä½
WF115=æµç¨å·²åçï¼æ æ³å é¤
WF116=ä¸è½å ç­¾ç»å§æäºº
WF117=ä¸è½è½¬å®¡ç»å§æäºº
WF118=ä¸è½ååç»å§æäºº
WF119=è®¾ç½®äºæµè½¬æ¡ä»¶ï¼æ æ³æ¹éå®¡æ¹
WF120=ä¸ä¸èç¹å®¡æ¹å¼å¸¸ï¼æ æ³æ¹éå®¡æ¹
WF121=å­æµç¨èªå¨åèµ·å®¡æ¹å¤±è´¥
WF122=æµç¨ä¸å­å¨
WF123=æµç¨å¤äºç»æ­¢ç¶æï¼ä¸å¯æä½
WF124=è¯¥æµç¨å·²åèµ·æ°æ®ï¼æ æ³å é¤!
WF125=æ¨æ²¡æåèµ·å§ææµç¨
WF126=æ¤éæµç¨ä¸è½è½¬å®¡
WF127=æ¤éæµç¨ä¸è½éå
WF128=è¯¥ç¨æ·å·²å®¡æ¹ï¼è¯·éæ°æå¼çé¢
WF129=å§æäººå·²æ è¯¥æµç¨æé
WF130=ç®¡çåä¸è½æ°å»ºå§æ/ä»£ç
WF131=ä¸è½éæ©admin
WF132=å·²æäººæ¥åï¼ä¸å¯ç¼è¾
WF133=æµè½¬æ¡ä»¶ä¸æ»¡è¶³ï¼æ æ³èªå¨åèµ·å®¡æ¹
WF134=ç¬¬ä¸ä¸ªå®¡æ¹èç¹è®¾ç½®åéäººï¼æ æ³èªå¨åèµ·å®¡æ¹
WF135=ç¬¬ä¸ä¸ªå®¡æ¹èç¹å¼å¸¸ï¼æ æ³èªå¨åèµ·å®¡æ¹
WF136=æ¾ä¸å°åèµ·äººï¼åèµ·å¤±è´¥
WF137=ä»£çäººåè¢«ä»£çäººç¸åï¼ä»£çå¤±è´¥
WF138=å­å¨æªç­¾æ¶çæ°æ®ï¼æ æ³å³é­
WF139=è¯¥æµç¨å·²è§¦åäºä»»å¡ï¼æ æ³å é¤
WF144=æä½å¤±è´¥ï¼åä¸æ¶é´åæç¸åæµç¨çä»£ç
WF145=æä½å¤±è´¥ï¼åä¸æ¶é´åæç¸åæµç¨ï¼ä¸è½ç¸äºä»£ç
app.apply.expandData=å±å¼æ°æ®
app.apply.location.location=æ·»å å®ä½
app.apply.location.modalTitle=éæ©ä½ç½®
app.apply.location.relocation=éæ°å®ä½
app.apply.noMoreData=æ²¡ææ´å¤æ°æ®
app.apply.pleaseKeyword=è¯·è¾å¥å³é®å­æç´¢
app.apply.screen=ç­é
app.apply.sort=æåº
app.my.accountSecurity=è´¦å·å®å¨
app.my.agencyMe=ä»£çç»æ
app.my.allFlow=å¨é¨æµç¨
app.my.entrustedAgency=å§æä»£ç
app.my.entrustMe=å§æç»æ
app.my.flowSelect=æµç¨éæ©
app.my.logOut=éåºç»å½
app.my.myAgency=æçä»£ç
app.my.myEntrust=æçå§æ
app.my.organization=æçç»ç»
app.my.position=æçå²ä½
app.my.scanCode=æ«ä¸æ«
app.my.setting=è®¾ç½®
app.my.settings.About=å³äºå¹³å°
app.my.settings.changePassword=ä¿®æ¹å¯ç 
app.my.settings.contact=èç³»æä»¬
app.my.settings.language=å¤è¯­è¨
app.my.settings.privacyPolicy=éç§æ¿ç­
app.my.settings.userAgreement=ç¨æ·åè®®
app.my.sto=ç»æ­¢
app.my.subordinates=æçä¸å±
app.my.switchIdentity=åæ¢èº«ä»½
common.add1Text=æ·»å 
common.add2Text=æ°å¢
common.addText=æ°å»º
common.back=è¿å
common.batchDelText=æ¹éå é¤
common.batchDelTip=æ¨ç¡®å®è¦å é¤è¿äºæ°æ®å, æ¯å¦ç»§ç»­?
common.batchPrintText=æ¹éæå°
common.cancelText=åæ¶
common.chooseText=è¯·éæ©
common.chooseTextPrefix=è¯·éæ©
common.cleanText=æ¸ç©º
common.closeList=å³é­åè¡¨
common.closeText=å³é­
common.collapseAll=æå 
common.continueAndAddText=ç¡®å®å¹¶æ°å¢
common.continueText=ç¡®å®å¹¶ç»§ç»­
common.copyText=å¤å¶
common.dark=é»æä¸»é¢
common.delText=å é¤
common.delTip=æ­¤æä½å°æ°¸ä¹å é¤è¯¥æ°æ®, æ¯å¦ç»§ç»­ï¼
common.detailText=è¯¦æ
common.drawerSearchText=è¯·è¾å¥å³é®è¯
common.editText=ç¼è¾
common.enterKeyword=è¯·è¾å¥å³é®è¯
common.expandAll=å±å¼
common.exportText=å¯¼åº
common.importText=å¯¼å¥
common.inputPlaceholder=è¯·è¾å¥
common.inputText=è¯·è¾å¥
common.inputTextPrefix=è¯·è¾å¥
common.keyword=å³é®è¯
common.leftTreeSearchText=è¯·è¾å¥å³é®è¯
common.light=äº®è²ä¸»é¢
common.loadingText=å è½½ä¸­...
common.moreText=æ´å¤
common.next=ä¸ä¸æ­¥
common.nextRecord=ä¸ä¸æ¡
common.noData=ææ æ°æ®
common.okText=ç¡®å®
common.prev=ä¸ä¸æ­¥
common.previewText=é¢è§
common.prevRecord=ä¸ä¸æ¡
common.printText=æå°
common.queryText=æ¥è¯¢
common.redo=å·æ°
common.redoText=éå
common.resetText=éç½®
common.saveText=ä¿å­
common.searchText=æç´¢
common.selectDataTip=è¯·éæ©ä¸æ¡æ°æ®
common.selectI18nCode=éæ©ç¿»è¯æ è®°
common.selectPlaceholder=è¯·éæ©
common.submitText=æäº¤
common.superQuery=é«çº§æ¥è¯¢
common.syncText=ç¬¬ä¸æ¹åæ­¥
common.tipTitle=æç¤º
common.undoText=æ¤é
component.app.searchNotData=ææ æç´¢ç»æ
component.app.toNavigate=åæ¢
component.app.toSearch=ç¡®è®¤
component.countdown.normalText=è·åéªè¯ç 
component.countdown.sendText={0}ç§åéæ°è·å
component.cropper.btn_reset=éç½®
component.cropper.btn_rotate_left=éæ¶éæè½¬
component.cropper.btn_rotate_right=é¡ºæ¶éæè½¬
component.cropper.btn_scale_x=æ°´å¹³ç¿»è½¬
component.cropper.btn_scale_y=åç´ç¿»è½¬
component.cropper.btn_zoom_in=æ¾å¤§
component.cropper.btn_zoom_out=ç¼©å°
component.cropper.modalTitle=å¤´åä¸ä¼ 
component.cropper.okText=ç¡®è®¤å¹¶ä¸ä¼ 
component.cropper.preview=é¢è§
component.cropper.selectImage=éæ©å¾ç
component.cropper.uploadSuccess=ä¸ä¼ æå
component.drawer.cancelText=å³é­
component.drawer.loadingText=å è½½ä¸­...
component.drawer.okText=ç¡®è®¤
component.excel.exportModalTitle=å¯¼åºæ°æ®
component.excel.fileName=æä»¶å
component.excel.fileType=æä»¶ç±»å
component.form.apiSelectNotFound=è¯·ç­å¾æ°æ®å è½½å®æ...
component.form.fold=æ¶èµ·
component.form.maxTip=å­ç¬¦æ°åºå°äº{0}ä½
component.form.unfold=å±å¼
component.icon.copy=å¤å¶å¾æ æå!
component.icon.placeholder=ç¹å»éæ©å¾æ 
component.icon.search=æç´¢å¾æ 
component.jnpf.areaSelect.modalTitle=çå¸åº
component.jnpf.calculate.storage=ç¨äºå±ç¤ºè®¡ç®ç»æï¼ä¸æ°æ®åæ¶ä¼ä¿å­å¥åº
component.jnpf.calculate.unStorage=ç¨äºå±ç¤ºè®¡ç®ç»æï¼ä¸æ°æ®ä¸ä¼ä¿å­
component.jnpf.common.allData=å¨é¨æ°æ®
component.jnpf.common.autoGenerate=ç³»ç»èªå¨çæ
component.jnpf.common.clearAll=æ¸ç©ºåè¡¨
component.jnpf.common.selected=å·²é
component.jnpf.dateRange.endPlaceholder=ç»ææ¥æ
component.jnpf.dateRange.startPlaceholder=å¼å§æ¥æ
component.jnpf.depSelect.modalTitle=éæ©é¨é¨
component.jnpf.groupSelect.modalTitle=éæ©åç»
component.jnpf.iconPicker.modalTitle=å¾æ éæ©
component.jnpf.iconPicker.searchPlaceholder=è¯·è¾å¥å³é®è¯
component.jnpf.iconPicker.select=éæ©
component.jnpf.iconPicker.ymCustom=ymCustomå¾æ 
component.jnpf.iconPicker.ymIcon=ymIconå¾æ 
component.jnpf.location.location=æ·»å å®ä½
component.jnpf.location.modalTitle=éæ©ä½ç½®
component.jnpf.location.relocation=éæ°å®ä½
component.jnpf.location.searchPlaceholder=æç´¢æç´æ¥å¨å°å¾ä¸ç¹é
component.jnpf.numberRange.max=æå¤§å¼
component.jnpf.numberRange.min=æå°å¼
component.jnpf.organizeSelect.modalTitle=éæ©ç»ç»
component.jnpf.popupAttr.storage=ç¨äºå±ç¤ºå³èå¼¹çªçå±æ§ï¼ä¸æ°æ®åæ¶ä¼ä¿å­å¥åº
component.jnpf.popupAttr.unStorage=ç¨äºå±ç¤ºå³èå¼¹çªçå±æ§ï¼ä¸æ°æ®ä¸ä¼ä¿å­
component.jnpf.popupSelect.modalTitle=éæ©æ°æ®
component.jnpf.posSelect.modalTitle=éæ©å²ä½
component.jnpf.relationFormAttr.storage=ç¨äºå±ç¤ºå³èè¡¨åçå±æ§ï¼ä¸æ°æ®åæ¶ä¼ä¿å­å¥åº
component.jnpf.relationFormAttr.unStorage=ç¨äºå±ç¤ºå³èè¡¨åçå±æ§ï¼ä¸æ°æ®ä¸ä¼ä¿å­
component.jnpf.roleSelect.modalTitle=éæ©è§è²
component.jnpf.sign.operateTip=è¯·å¨æ­¤åºåä½¿ç¨é¼ æ æåç­¾å
component.jnpf.sign.signPlaceholder=è¯·ç­¾å
component.jnpf.sign.signTip=æåç­¾å
component.jnpf.timeRange.endPlaceholder=ç»ææ¶é´
component.jnpf.timeRange.startPlaceholder=å¼å§æ¶é´
component.jnpf.userSelect.modalTitle=éæ©ç¨æ·
component.menu.search=èåæç´¢
component.modal.cancelText=å³é­
component.modal.close=å³é­
component.modal.maximize=æå¤§å
component.modal.okText=ç¡®è®¤
component.modal.restore=è¿å
component.table.action=æä½
component.table.index=åºå·
component.table.settingColumn=åè®¾ç½®
component.table.settingColumnShow=åå±ç¤º
component.table.settingDens=å¯åº¦
component.table.settingDensDefault=é»è®¤
component.table.settingDensMiddle=ä¸­ç­
component.table.settingDensSmall=ç´§å
component.table.settingFixedLeft=åºå®å°å·¦ä¾§
component.table.settingFixedRight=åºå®å°å³ä¾§
component.table.settingFullScreen=å¨å±
component.table.settingIndexColumnShow=åºå·å
component.table.settingSelectColumnShow=å¾éå
component.table.status=ç¶æ
component.table.summary=åè®¡
component.table.total=å± {total} æ¡æ°æ®
component.time.after=å
component.time.before=å
component.time.days=å¤©
component.time.hours=å°æ¶
component.time.just=åå
component.time.minutes=åé
component.time.seconds=ç§
component.tree.checkStrictly=å±çº§å³è
component.tree.checkUnStrictly=å±çº§ç¬ç«
component.tree.expandAll=å±å¼å¨é¨
component.tree.reload=å·æ°æ°æ®
component.tree.selectAll=éæ©å¨é¨
component.tree.unExpandAll=æå å¨é¨
component.tree.unSelectAll=åæ¶éæ©
component.upload.accept=æ¯æ{0}æ ¼å¼
component.upload.acceptUpload=åªè½ä¸ä¼ {0}æ ¼å¼æä»¶
component.upload.audio=é³é¢
component.upload.buttonText=ç¹å»ä¸ä¼ 
component.upload.checking=æä»¶æ ¡éªä¸­
component.upload.choose=éæ©æä»¶
component.upload.del=å é¤
component.upload.download=ä¸è½½
component.upload.downloadAll=å¨é¨ä¸è½½
component.upload.fileMaxNumber=æå¤å¯ä»¥ä¸ä¼ {0}ä¸ªæä»¶
component.upload.fileMaxSize=æä»¶å¤§å°è¶è¿{size}{unit}
component.upload.fileName=æä»¶å
component.upload.fileReadError=æä»¶{0}è¯»ååºéï¼è¯·æ£æ¥è¯¥æä»¶
component.upload.fileSize=æä»¶å¤§å°
component.upload.fileStatue=ç¶æ
component.upload.fileTypeCheck=è¯·éæ©{0}ç±»åçæä»¶
component.upload.image=å¾ç
component.upload.imageMaxNumber=æå¤å¯ä»¥ä¸ä¼ {0}å¼ å¾ç
component.upload.imageMaxSize=å¾çå¤§å°è¶è¿{size}{unit}
component.upload.imgUpload=å¾çä¸ä¼ 
component.upload.legend=ç¥ç¼©å¾
component.upload.maxNumber=æå¤åªè½ä¸ä¼ {0}ä¸ªæä»¶
component.upload.maxSize=åä¸ªæä»¶ä¸è¶è¿{0}MB
component.upload.maxSizeMultiple=åªè½ä¸ä¼ ä¸è¶è¿{0}MBçæä»¶!
component.upload.operating=æä½
component.upload.paused=æåä¸­
component.upload.preview=é¢è§
component.upload.reUploadFailed=éæ°ä¸ä¼ å¤±è´¥æä»¶
component.upload.save=ä¿å­
component.upload.saveError=æ²¡æä¸ä¼ æåçæä»¶ï¼æ æ³ä¿å­!
component.upload.saveWarn=è¯·ç­å¾æä»¶ä¸ä¼ åï¼ä¿å­!
component.upload.startUpload=å¼å§ä¸ä¼ 
component.upload.upload=ä¸ä¼ 
component.upload.uploaded=å·²ä¸ä¼ 
component.upload.uploadError=ä¸ä¼ å¤±è´¥
component.upload.uploadImg=è¯·ä¸ä¼ å¾ç
component.upload.uploading=ä¸ä¼ ä¸­
component.upload.uploadSuccess=ä¸ä¼ æå
component.upload.uploadWait=è¯·ç­å¾æä»¶ä¸ä¼ ç»æåæä½
component.upload.video=è§é¢
component.upload.videoNoPreview=é³è§é¢æä»¶ä¸è½é¢è§
component.upload.view=æ¥ç
component.upload.viewImage=æ¥çå¾ç
component.upload.waiting=ç­å¾ä¸­
component.upload.zipNoPreview=åç¼©åä¸è½é¢è§
component.verify.dragText=è¯·æä½æ»åæå¨
component.verify.error=éªè¯å¤±è´¥ï¼
component.verify.redoTip=ç¹å»å¾çå¯å·æ°
component.verify.successText=éªè¯éè¿
component.verify.time=éªè¯æ ¡éªæå,èæ¶{time}ç§ï¼
formGenerator.cleanComponentTip=ç¡®å®è¦æ¸ç©ºææç»ä»¶å?
formGenerator.component.alert=æç¤º
formGenerator.component.areaSelect=çå¸åºå
formGenerator.component.autoComplete=ä¸æè¡¥å¨
formGenerator.component.barcode=æ¡å½¢ç 
formGenerator.component.billRule=åæ®ç»ä»¶
formGenerator.component.button=æé®
formGenerator.component.calculate=è®¡ç®å¬å¼
formGenerator.component.card=å¡çå®¹å¨
formGenerator.component.cascader=çº§èéæ©
formGenerator.component.checkbox=å¤éæ¡ç»
formGenerator.component.collapse=æå é¢æ¿
formGenerator.component.colorPicker=é¢è²éæ©
formGenerator.component.createTime=åå»ºæ¶é´
formGenerator.component.createUser=åå»ºäººå
formGenerator.component.currOrganize=æå±ç»ç»
formGenerator.component.currPosition=æå±å²ä½
formGenerator.component.datePicker=æ¥æéæ©
formGenerator.component.depSelect=é¨é¨éæ©
formGenerator.component.divider=åå²çº¿
formGenerator.component.editor=å¯ææ¬
formGenerator.component.groupSelect=åç»éæ©
formGenerator.component.groupTitle=åç»æ é¢
formGenerator.component.iframe=Iframe
formGenerator.component.input=åè¡è¾å¥
formGenerator.component.inputNumber=æ°å­è¾å¥
formGenerator.component.link=é¾æ¥
formGenerator.component.location=å®ä½
formGenerator.component.modifyTime=ä¿®æ¹æ¶é´
formGenerator.component.modifyUser=ä¿®æ¹äººå
formGenerator.component.organizeSelect=ç»ç»éæ©
formGenerator.component.popupAttr=å¼¹çªéæ©å±æ§
formGenerator.component.popupSelect=å¼¹çªéæ©
formGenerator.component.popupTableSelect=ä¸æè¡¨æ ¼
formGenerator.component.posSelect=å²ä½éæ©
formGenerator.component.qrcode=äºç»´ç 
formGenerator.component.radio=åéæ¡ç»
formGenerator.component.rate=è¯å
formGenerator.component.relationForm=å³èè¡¨å
formGenerator.component.relationFormAttr=å³èè¡¨åå±æ§
formGenerator.component.roleSelect=è§è²éæ©
formGenerator.component.row=æ æ ¼å®¹å¨
formGenerator.component.select=ä¸æéæ©
formGenerator.component.sign=æåç­¾å
formGenerator.component.slider=æ»å
formGenerator.component.switch=å¼å³
formGenerator.component.tab=æ ç­¾é¢æ¿
formGenerator.component.table=è®¾è®¡å­è¡¨
formGenerator.component.tableGrid=è¡¨æ ¼å®¹å¨
formGenerator.component.text=ææ¬
formGenerator.component.textarea=å¤è¡è¾å¥
formGenerator.component.timePicker=æ¶é´éæ©
formGenerator.component.treeSelect=ä¸ææ å½¢
formGenerator.component.uploadFile=æä»¶ä¸ä¼ 
formGenerator.component.uploadImg=å¾çä¸ä¼ 
formGenerator.component.userSelect=ç¨æ·éæ©
formGenerator.component.usersSelect=ç¨æ·ç»ä»¶
formGenerator.copyComponentTip=ç¡®å®å¤å¶è¯¥ç»ä»¶?
formGenerator.delComponentTip=ç¡®å®å é¤è¯¥ç»ä»¶?
layout.footer.onlineDocument=å¨çº¿ææ¡£
layout.footer.onlinePreview=å¨çº¿é¢è§
layout.header.about=å³äºå¹³å°
layout.header.commonMenus=å¸¸ç¨èå
layout.header.dropdownItemDoc=ææ¡£
layout.header.dropdownItemLoginOut=éåºç³»ç»
layout.header.feedback=åé¦é®é¢
layout.header.home=é¦é¡µ
layout.header.lockScreen=éå®å±å¹
layout.header.lockScreenBtn=éå®
layout.header.lockScreenPassword=éå±å¯ç 
layout.header.profile=ä¸ªäººä¿¡æ¯
layout.header.setting=è®¾ç½®
layout.header.standingChange=åæ¢èº«ä»½
layout.header.statement=å®æ¹å£°æ
layout.header.systemChange=åºç¨åæ¢
layout.header.tooltipChat=èå¤©
layout.header.tooltipEntryFull=å¨å±
layout.header.tooltipErrorLog=éè¯¯æ¥å¿
layout.header.tooltipExitFull=éåºå¨å±
layout.header.tooltipLock=éå®å±å¹
layout.header.tooltipNotify=æ¶æ¯
layout.multipleTab.close=å³é­æ ç­¾é¡µ
layout.multipleTab.closeAll=å³é­å¨é¨æ ç­¾é¡µ
layout.multipleTab.closeLeft=å³é­å·¦ä¾§æ ç­¾é¡µ
layout.multipleTab.closeOther=å³é­å¶å®æ ç­¾é¡µ
layout.multipleTab.closeRight=å³é­å³ä¾§æ ç­¾é¡µ
layout.multipleTab.reload=éæ°å è½½
layout.multipleTab.setCommon=è®¾ä¸ºå¸¸ç¨èå
layout.setting.animation=å¨ç»
layout.setting.animationType=å¨ç»ç±»å
layout.setting.autoScreenLock=èªå¨éå±
layout.setting.blueBg=èèæè¾°
layout.setting.breadcrumb=é¢åå±
layout.setting.breadcrumbIcon=é¢åå±å¾æ 
layout.setting.cachePage=ç¼å­é¡µé¢
layout.setting.clearBtn=æ¸ç©ºç¼å­å¹¶è¿åç»å½é¡µ
layout.setting.closeMixSidebarOnChange=åæ¢é¡µé¢å³é­èå
layout.setting.collapseMenuDisplayName=æå èåæ¾ç¤ºåç§°
layout.setting.colorWeak=è²å¼±æ¨¡å¼
layout.setting.contentMode=åå®¹åºåå®½åº¦
layout.setting.contentModeFixed=å®å®½
layout.setting.contentModeFull=æµå¼
layout.setting.copyBtn=æ·è´
layout.setting.darkMode=ä¸»é¢
layout.setting.defaultBg=ç»å¸ä¸»é¢
layout.setting.drawerTitle=éç½®
layout.setting.expandedMenuWidth=èåå±å¼å®½åº¦
layout.setting.fixedHeader=åºå®header
layout.setting.fixedSideBar=åºå®Sidebar
layout.setting.footer=é¡µè
layout.setting.fullContent=å¨å±åå®¹
layout.setting.grayMode=ç°è²æ¨¡å¼
layout.setting.greenBg=ç¢§ç»¿ç¿ é£
layout.setting.header=é¡¶æ 
layout.setting.headerTheme=é¡¶æ ä¸»é¢
layout.setting.interfaceDisplay=çé¢æ¾ç¤º
layout.setting.interfaceFunction=çé¢åè½
layout.setting.menuAccordion=ä¾§è¾¹èåæé£ç´æ¨¡å¼
layout.setting.menuCollapse=æå èå
layout.setting.menuCollapseButton=èåæå æé®
layout.setting.menuDrag=ä¾§è¾¹èåææ½
layout.setting.menuSearch=èåæç´¢
layout.setting.menuTriggerBottom=åºé¨
layout.setting.menuTriggerNone=ä¸æ¾ç¤º
layout.setting.menuTriggerTop=é¡¶é¨
layout.setting.menuTypeMix=é¡¶é¨æ··åæ¨¡å¼
layout.setting.menuTypeMixSidebar=å·¦ä¾§æ··åæ¨¡å¼
layout.setting.menuTypeSidebar=å·¦ä¾§èåæ¨¡å¼
layout.setting.menuTypeTopMenu=é¡¶é¨èåæ¨¡å¼
layout.setting.minute=åé
layout.setting.mixSidebarFixed=åºå®å±å¼èå
layout.setting.mixSidebarTrigger=æ··åèåè§¦åæ¹å¼
layout.setting.navMode=å¯¼èªæ æ¨¡å¼
layout.setting.notAutoScreenLock=ä¸èªå¨éå±
layout.setting.off=å³
layout.setting.on=å¼
layout.setting.operatingContent=å¤å¶æå,è¯·å° src/settings/projectSetting.ts ä¸­ä¿®æ¹éç½®ï¼
layout.setting.operatingTitle=æä½æå
layout.setting.progress=é¡¶é¨è¿åº¦æ¡
layout.setting.purpleBg=ç´«è¤èå°
layout.setting.resetSuccess=éç½®æåï¼
layout.setting.sidebar=å·¦ä¾§èå
layout.setting.sidebarTheme=èåä¸»é¢
layout.setting.splitMenu=åå²èå
layout.setting.switchAnimation=åæ¢å¨ç»
layout.setting.switchLoading=åæ¢loading
layout.setting.systemBackground=ç³»ç»èæ¯
layout.setting.sysTheme=ç³»ç»ä¸»é¢
layout.setting.tabDetail=æ ç­¾è¯¦æé¡µ
layout.setting.tabs=æ ç­¾é¡µ
layout.setting.tabsFoldBtn=æ ç­¾é¡µæå æé®
layout.setting.tabsIcon=æ ç­¾å¾æ 
layout.setting.tabsQuickBtn=æ ç­¾é¡µå¿«æ·æé®
layout.setting.tabsRedoBtn=æ ç­¾é¡µå·æ°æé®
layout.setting.toggleLocale=è¯­è¨åæ¢
layout.setting.topMenuAlignCenter=å±å³
layout.setting.topMenuAlignLeft=å±å·¦
layout.setting.topMenuAlignRight=å±ä¸­
layout.setting.topMenuLayout=é¡¶é¨èåå¸å±
layout.setting.triggerClick=ç¹å»
layout.setting.triggerHover=æ¬å
routes.basic.emailDetail=æ¥çé®ä»¶
routes.basic.errorLogList=éè¯¯æ¥å¿åè¡¨
routes.basic.externalLink=é¾æ¥
routes.basic.home=é¦é¡µ
routes.basic.login=ç»å½
routes.basic.previewModel=åè½é¢è§
routes.basic.workFlowDetail=æµç¨è¯¦æ
routes.commonWords=å®¡æ¹å¸¸ç¨è¯­
routes.dataReport=æ¥è¡¨ç¤ºä¾(å)
routes.extend=å¼åç¤ºä¾
routes.extend-barCode=æ¡ç ç¤ºä¾
routes.extend-bigData=ç¾ä¸æ°æ®
routes.extend-documentPreview=ææ¡£ç¤ºä¾
routes.extend-email=é®ä»¶æ¶å
routes.extend-formDemo=è¡¨åç¤ºä¾
routes.extend-formDemo-fieldForm1=è¡¨åå­æ®µ1
routes.extend-formDemo-fieldForm2=è¡¨åå­æ®µ2
routes.extend-formDemo-fieldForm3=è¡¨åå­æ®µ3
routes.extend-formDemo-fieldForm4=è¡¨åå­æ®µ4
routes.extend-formDemo-fieldForm5=è¡¨åå­æ®µ5
routes.extend-formDemo-fieldForm6=è¡¨åå­æ®µ6
routes.extend-formDemo-verifyForm=è¡¨åéªè¯
routes.extend-formDemo-verifyForm1=è¡¨åéªè¯1
routes.extend-functionDemo=åè½ç¤ºä¾
routes.extend-graphDemo=å¾è¡¨ç¤ºä¾
routes.extend-graphDemo-echartsBar=E-æ±ç¶å¾
routes.extend-graphDemo-echartsBarAcross=E-æ¨ªç¶æ¡å½¢å¾
routes.extend-graphDemo-echartsCandlestick=E-Kçº¿å¾
routes.extend-graphDemo-echartsFunnel=E-æ¼æå¾
routes.extend-graphDemo-echartsGauge=E-ä»ªè¡¨å¾
routes.extend-graphDemo-echartsLineArea=E-çº¿å½¢å¾
routes.extend-graphDemo-echartsLineBar=E-ææ±æ··åå¾
routes.extend-graphDemo-echartsPie=E-é¥¼ç¶å¾
routes.extend-graphDemo-echartsScatter=E-æ£ç¹å¾
routes.extend-graphDemo-echartsTree=E-æ å½¢å¾
routes.extend-graphDemo-highchartsArea=H-é¢ç§¯å¾
routes.extend-graphDemo-highchartsBellcurve=H-è´å°æ²çº¿
routes.extend-graphDemo-highchartsBullet=H-å­å¼¹å¾
routes.extend-graphDemo-highchartsColumn=H-æ±ç¶å¾
routes.extend-graphDemo-highchartsFunnel=H-æ¼æå¾
routes.extend-graphDemo-highchartsGauge=H-ä»ªè¡¨å¾
routes.extend-graphDemo-highchartsLine=H-çº¿æ§å¾
routes.extend-graphDemo-highchartsPie=H-é¥¼ç¶å¾
routes.extend-graphDemo-highchartsScatter=H-æ£ç¹å¾
routes.extend-graphDemo-highchartsWordcloud=H-è¯äºå¾
routes.extend-importAndExport=å¯¼å¥å¯¼åº
routes.extend-map=å°å¾ç¤ºä¾
routes.extend-order=è®¢åç®¡ç
routes.extend-orderDemo=è®¢åç¤ºä¾
routes.extend-portalDemo=é¨æ·ç¤ºä¾
routes.extend-printData=æå°ç¤ºä¾
routes.extend-projectGantt=é¡¹ç®ç®¡ç
routes.extend-schedule=æ¥ç¨å®æ
routes.extend-signature=çµå­ç­¾å
routes.extend-signet=çµå­ç­¾ç« 
routes.extend-tableDemo=è¡¨æ ¼ç¤ºä¾
routes.extend-tableDemo-commonTable=æ®éè¡¨æ ¼
routes.extend-tableDemo-complexHeader=å¤æè¡¨å¤´
routes.extend-tableDemo-extension=å»¶ä¼¸æ©å±
routes.extend-tableDemo-groupingTable=è¡¨æ ¼åç»
routes.extend-tableDemo-lockTable=è¡¨æ ¼éå®
routes.extend-tableDemo-mergeTable=è¡¨æ ¼åå¹¶
routes.extend-tableDemo-postilTable=è¡¨æ ¼æ¹æ³¨
routes.extend-tableDemo-printTable=è¡¨æ ¼æå°
routes.extend-tableDemo-redactTable=è¡¨æ ¼ç¼è¾
routes.extend-tableDemo-signTable=è¡¨æ ¼æ è®°
routes.extend-tableDemo-statisticsTable=è¡¨æ ¼ç»è®¡
routes.extend-tableDemo-tableTree=è¡¨æ ¼æ å½¢
routes.extend-tableDemo-treeTable=æ å½¢è¡¨æ ¼
routes.flowEngine=æµç¨å¼æ
routes.formDesign=ç³»ç»è¡¨å
routes.generator=ä»£ç çæ
routes.generator-appForm=ç§»å¨è¡¨å
routes.generator-flowForm=åèµ·è¡¨å
routes.generator-webForm=åè½è¡¨å
routes.lioui=æµç¨ç¤ºä¾
routes.mainSystem=å¼åå¹³å°
routes.moreMenu=æ´å¤...
routes.msgCenter=æ¶æ¯ä¸­å¿
routes.msgCenter-accountConfig=è´¦å·éç½®
routes.msgCenter-accountConfig-ding=éééç½®
routes.msgCenter-accountConfig-mail=é®ç®±éç½®
routes.msgCenter-accountConfig-mp=å¾®ä¿¡å¬ä¼å·éç½®
routes.msgCenter-accountConfig-shortMsg=ç­ä¿¡éç½®
routes.msgCenter-accountConfig-webhook=webhookéç½®
routes.msgCenter-accountConfig-weCom=ä¼ä¸å¾®ä¿¡éç½®
routes.msgCenter-msgMonitor=æ¶æ¯çæ§
routes.msgCenter-msgTemplate=æ¶æ¯æ¨¡æ¿
routes.msgCenter-sendConfig=åééç½®
routes.onlineDev=å¨çº¿å¼å
routes.onlineDev-appDesign=ç§»å¨è®¾è®¡
routes.onlineDev-dataReport=æ¥è¡¨è®¾è®¡ï¼åï¼
routes.onlineDev-dataScreen=å¤§å±è®¾è®¡
routes.onlineDev-integration=éæå©æ
routes.onlineDev-printDev=æå°è®¾è®¡
routes.onlineDev-report=æ¥è¡¨è®¾è®¡
routes.onlineDev-visualPortal=é¨æ·è®¾è®¡
routes.onlineDev-webDesign=è¡¨åè®¾è®¡
routes.permission=ç»ç»æé
routes.permission-auth=æéç®¡ç
routes.permission-authorize=æéç»
routes.permission-department=é¨é¨ç®¡ç
routes.permission-grade=ç®¡çå
routes.permission-group=åç»ç®¡ç
routes.permission-organize=ç»ç»ç®¡ç
routes.permission-position=å²ä½ç®¡ç
routes.permission-role=è§è²ç®¡ç
routes.permission-user=ç¨æ·ç®¡ç
routes.permission-userOnline=å¨çº¿ç¨æ·
routes.printDemo=æå°ç¤ºä¾
routes.report=æ¥è¡¨ç¤ºä¾
routes.reportBI=å¤§å±ç¤ºä¾
routes.system=ç³»ç»ç®¡ç
routes.system-area=è¡æ¿åºå
routes.system-billRule=åæ®æ¨¡æ¿
routes.system-cache=ç³»ç»ç¼å­
routes.system-icons=ç³»ç»å¾æ 
routes.system-kit=è¡¨åæ¨¡æ¿
routes.system-language=ç¿»è¯ç®¡ç
routes.system-log=ç³»ç»æ¥å¿
routes.system-menu=åºç¨èå
routes.system-messageTemplate=æ¶æ¯æ¨¡æ¿
routes.system-monitor=ç³»ç»çæ§
routes.system-notice=ç³»ç»å¬å
routes.system-signature=ç­¾ç« ç®¡ç
routes.system-smsTemplate=ç­ä¿¡æ¨¡æ¿
routes.system-sysConfig=ç³»ç»éç½®
routes.system-systemTemplate=ç³»ç»æ¨¡æ¿
routes.system-task=ç³»ç»è°åº¦
routes.systemData=æ°æ®åºç¨
routes.systemData-dataBackup=æ°æ®å¤ä»½
routes.systemData-dataInterface=æ°æ®æ¥å£
routes.systemData-dataModel=æ°æ®å»ºæ¨¡
routes.systemData-dataSource=æ°æ®è¿æ¥
routes.systemData-dataSync=æ°æ®åæ­¥
routes.systemData-dictionary=æ°æ®å­å¸
routes.systemData-interfaceAuth=æ¥å£è®¤è¯
routes.systemData-map=æ°æ®å°å¾
routes.weChat=å¾®ä¿¡éç½®
routes.weChat-mpConfig=å¬ä¼å·éç½®
routes.weChat-mpMaterial=å¬ä¼å·ç´ æ
routes.weChat-mpMenu=å¬ä¼å·èå
routes.weChat-mpMessage=å¬ä¼å·æ¶æ¯
routes.weChat-mpUser=å¬ä¼å·ç¨æ·
routes.weChat-qyDepartment=ä¼ä¸å·ç»ç»
routes.weChat-qyhConfig=ä¼ä¸å·éç½®
routes.weChat-qyMessage=ä¼ä¸å·æ¶æ¯
routes.weChat-qyUser=ä¼ä¸å·ç¨æ·
routes.workFlow=åååå¬
routes.workFlow-addFlow=åèµ·æµç¨
routes.workFlow-document=ææ¡£ä¸­å¿
routes.workFlow-entrust=æµç¨å§æ
routes.workFlow-flowCirculate=æéæç
routes.workFlow-flowDoing=æçå¨å
routes.workFlow-flowDone=æçå·²å
routes.workFlow-flowEngine=æµç¨è®¾è®¡
routes.workFlow-flowLaunch=æåèµ·ç
routes.workFlow-flowMonitor=æµç¨çæ§
routes.workFlow-flowTodo=æçå¾å
routes.workFlow-flowToSign=æçå¾ç­¾
routes.workFlow-form=è¡¨åè®¾è®¡
routes.workFlow-printTemplate=æå°æ¨¡æ¿
routes.workFlow-schedule=æ¥ç¨å®æ
routes.workSystem=ä¸å¡å¹³å°
sys.api.apiRequestFailed=è¯·æ±åºéï¼è¯·ç¨åéè¯
sys.api.apiTimeoutMessage=æ¥å£è¯·æ±è¶æ¶,è¯·å·æ°é¡µé¢éè¯!
sys.api.errMsg401=ç¨æ·æ²¡ææéï¼ä»¤çãç¨æ·åãå¯ç éè¯¯ï¼!
sys.api.errMsg403=ç¨æ·å¾å°ææï¼ä½æ¯è®¿é®æ¯è¢«ç¦æ­¢çã!
sys.api.errMsg404=ç½ç»è¯·æ±éè¯¯,æªæ¾å°è¯¥èµæº!
sys.api.errMsg405=ç½ç»è¯·æ±éè¯¯,è¯·æ±æ¹æ³æªåè®¸!
sys.api.errMsg408=ç½ç»è¯·æ±è¶æ¶!
sys.api.errMsg500=æå¡å¨éè¯¯,è¯·èç³»ç®¡çå!
sys.api.errMsg501=ç½ç»æªå®ç°!
sys.api.errMsg502=ç½ç»éè¯¯!
sys.api.errMsg503=æå¡ä¸å¯ç¨ï¼æå¡å¨ææ¶è¿è½½æç»´æ¤!
sys.api.errMsg504=ç½ç»è¶æ¶!
sys.api.errMsg505=httpçæ¬ä¸æ¯æè¯¥è¯·æ±!
sys.api.errorMessage=æä½å¤±è´¥,ç³»ç»å¼å¸¸!
sys.api.errorTip=éè¯¯æç¤º
sys.api.networkException=ç½ç»å¼å¸¸
sys.api.networkExceptionMsg=ç½ç»å¼å¸¸ï¼è¯·æ£æ¥æ¨çç½ç»è¿æ¥æ¯å¦æ­£å¸¸!
sys.api.operationFailed=æä½å¤±è´¥
sys.api.timeoutMessage=ç»å½è¶æ¶,è¯·éæ°ç»å½!
sys.app.logoutMessage=æ¯å¦ç¡®è®¤éåºç³»ç»?
sys.app.logoutTip=æ¸©é¦¨æé
sys.app.menuLoading=èåå è½½ä¸­...
sys.errorLog.enableMessage=åªå¨`/src/settings/projectSetting.ts` åçuseErrorHandle=trueæ¶çæ.
sys.errorLog.fireAjaxError=ç¹å»è§¦åajaxéè¯¯
sys.errorLog.fireResourceError=ç¹å»è§¦åèµæºå è½½éè¯¯
sys.errorLog.fireVueError=ç¹å»è§¦åvueéè¯¯
sys.errorLog.modalTitle=éè¯¯è¯¦æ
sys.errorLog.tableActionDesc=è¯¦æ
sys.errorLog.tableColumnDate=æ¶é´
sys.errorLog.tableColumnFile=æä»¶
sys.errorLog.tableColumnMsg=éè¯¯ä¿¡æ¯
sys.errorLog.tableColumnStackMsg=stackä¿¡æ¯
sys.errorLog.tableColumnType=ç±»å
sys.errorLog.tableTitle=éè¯¯æ¥å¿åè¡¨
sys.exception.backHome=è¿åé¦é¡µ
sys.exception.backLogin=è¿åç»å½
sys.exception.networkErrorSubTitle=æ±æ­ï¼æ¨çç½ç»è¿æ¥å·²æ­å¼ï¼è¯·æ£æ¥æ¨çç½ç»ï¼
sys.exception.networkErrorTitle=ç½ç»éè¯¯
sys.exception.noDataTitle=å½åé¡µæ æ°æ®
sys.exception.subTitle403=æ±æ­ï¼æ¨æ æè®¿é®æ­¤é¡µé¢ã
sys.exception.subTitle404=æ±æ­ï¼æ¨è®¿é®çé¡µé¢ä¸å­å¨ã
sys.exception.subTitle500=æ±æ­ï¼æå¡å¨æ¥åéè¯¯ã
sys.lock.alert=éå±å¯ç éè¯¯
sys.lock.backToLogin=è¿åç»å½
sys.lock.entry=è¿å¥ç³»ç»
sys.lock.placeholder=è¯·è¾å¥ç»å½å¯ç 
sys.lock.unlock=ç¹å»è§£é
sys.login.accountPlaceholder=è¯·è¾å¥è´¦å·
sys.login.accountTip=è¯·è¾å¥è´¦å·
sys.login.backSignIn=è¿å
sys.login.changeCode=ç¹å»åæ¢éªè¯ç 
sys.login.codeTip=è¯·è¾å¥éªè¯ç 
sys.login.codeTitle=ææºéªè¯ç»å½
sys.login.company=è¯·è¾å¥å¬å¸å
sys.login.confirmLogin=è¯·å¨ææºç«¯ç¡®è®¤ç»å½
sys.login.confirmPassword=ç¡®è®¤å¯ç 
sys.login.contacts=è¯·è¾å¥èç³»äºº
sys.login.diffPwd=ä¸¤æ¬¡è¾å¥å¯ç ä¸ä¸è´
sys.login.email=é®ç®±
sys.login.expired=äºç»´ç å·²å¤±æ
sys.login.forgetFormTitle=éç½®å¯ç 
sys.login.forgetPassword=å¿è®°å¯ç ?
sys.login.getCode=è·åéªè¯ç 
sys.login.lastLoginInfo=ä¸æ¬¡ç»å½ä¿¡æ¯
sys.login.logIn=ç»å½
sys.login.loginButton=ç»å½
sys.login.mobile=è¯·è¾å¥ææºå·
sys.login.mobilePlaceholder=è¯·è¾å¥ææºå·ç 
sys.login.mobileSignInFormTitle=ææºéªè¯ç ç»å½
sys.login.otherLogin=å¶ä»ç»å½æ¹å¼
sys.login.otherSignIn=å¶ä»ç»å½æ¹å¼
sys.login.password=è¯·è¾å¥å¯ç 
sys.login.passwordPlaceholder=è¯·è¾å¥å¯ç 
sys.login.passwordTip=è¯·è¾å¥å¯ç 
sys.login.policy=æåæxxxéç§æ¿ç­
sys.login.policyPlaceholder=å¾éåæè½æ³¨å
sys.login.qrCodeTip=è¯·ä½¿ç¨APPæ«æäºç»´ç ç»å½ï¼180ç§åäºç»´ç å¤±æ
sys.login.qrSignInFormTitle=æ«ç ç»å½
sys.login.recoverCode=åæ¶ç»å½
sys.login.refreshCode=ç¹å»å·æ°
sys.login.registerButton=æ³¨å
sys.login.rememberMe=è®°ä½æ
sys.login.reSend=éæ°åé
sys.login.rightMobile=è¯·è¾å¥æ­£ç¡®çææºå·
sys.login.rule=ç§æ·å­è´¦æ·è§åï¼ç§æ·å·{'@'}è´¦æ· ä¾ï¼18577778888{'@'}101001
sys.login.scanSign=æ«ç åç¹å»"ç¡®è®¤"ï¼å³å¯å®æç»å½
sys.login.scanSuccessful=æ«ç æå
sys.login.scanTip=æ«ç ç»å½
sys.login.scanTitle=æ«ç ç»å½
sys.login.signInDesc=è¾å¥æ¨çä¸ªäººè¯¦ç»ä¿¡æ¯å¼å§ä½¿ç¨ï¼
sys.login.signInFormTitle=è´¦å·å¯ç ç»å½
sys.login.signInTitle=å¼ç®±å³ç¨çä¸­åå°ç®¡çç³»ç»
sys.login.signUpFormTitle=æ³¨å
sys.login.smsCode=è¯·è¾å¥éªè¯ç 
sys.login.smsPlaceholder=è¯·è¾å¥éªè¯ç 
sys.login.subTitle=éè¿è´¦å·å¯ç ç»å½
sys.login.subTitle1=éè¿ææºéªè¯ç ç»å½ï¼æèåæ¢ä¸º
sys.login.subTitle2=éè¿è´¦å·å¯ç ç»å½ï¼æèåæ¢ä¸º
sys.login.subTitle3=éè¿æ«ç ç»å½ï¼æèåæ¢ä¸º
sys.login.title=è´¦æ·å¯ç ç»å½
sys.login.upper=å¤§åå·²éå®
sys.login.username=è¯·è¾å¥è´¦å·
sys.login.version=çæ¬
sys.login.welcome=æ¬¢è¿ä½¿ç¨
sys.validate.arrayRequiredPrefix=è¯·è³å°éæ©ä¸ä¸ª
sys.validate.date=è¯·è¾å¥æ­£ç¡®çæ¥æ
sys.validate.email=è¯·è¾å¥æ­£ç¡®çé®ç®±
sys.validate.idCard=è¯·è¾å¥æ­£ç¡®çèº«ä»½è¯å·ç 
sys.validate.mobilePhone=è¯·è¾å¥æ­£ç¡®çææºå·ç 
sys.validate.money=è¯·è¾å¥æ­£ç¡®çéé¢
sys.validate.number=è¯·è¾å¥æ­£ç¡®çæ°å­
sys.validate.phone=è¯·è¾å¥æ­£ç¡®çèç³»æ¹å¼
sys.validate.telephone=è¯·è¾å¥æ­£ç¡®ççµè¯å·ç 
sys.validate.textRequiredSuffix=ä¸è½ä¸ºç©º
sys.validate.url=è¯·è¾å¥æ­£ç¡®çç½å
views.dynamicModel.hideSome=éèé¨å
views.dynamicModel.passwordPlaceholder=è¯·è¾å¥å¯ç 
views.dynamicModel.scanAndShare=æ«æäºç»´ç ï¼åäº«æ­¤é¾æ¥
views.dynamicModel.showMore=å è½½æ´å¤
views.http404.goBackBtn=è¿åé¦é¡µ
views.http404.subTips=è¯·æ£æ¥æ¨è¾å¥çURLæ¯å¦æ­£ç¡®ï¼æåå»æé®è¿åé¦é¡µã
views.http404.tips=æ±æ­ï¼ä½ è®¿é®çé¡µé¢ä¸å­å¨ææ æè®¿é®!
package jnpf.controller;

import jnpf.entity.ColdStorageProjectEntity;
import jnpf.model.ColdStorageProjectVO;
import jnpf.util.EntityConverter;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Date;

import static org.junit.jupiter.api.Assertions.*;

/**
 * EntityConverter工具类测试
 * 验证实体转换是否正确处理字段复制问题
 *
 * @版本： V5.2.0
 * @版权： 杭州多协信息技术有限公司（https://www.duoxieyun.com）
 * @作者： 多协开发组
 * @日期： 2025-01-27
 */
@ExtendWith(MockitoExtension.class)
class EntityConverterTest {

    @Test
    void testToProjectVO_AllFieldsCopied() {
        // 准备测试数据
        ColdStorageProjectEntity entity = new ColdStorageProjectEntity();
        entity.setId("test-id-123");
        entity.setProjectCode("PROJ001");
        entity.setProjectName("测试项目");
        entity.setCompanyName("测试公司");
        entity.setProjectManager("张三");
        entity.setManagerPhone("13800138000");
        entity.setProjectStatus(1);
        entity.setProjectRemark("测试备注");
        
        Date now = new Date();
        entity.setCreatorTime(now);
        entity.setLastModifyTime(now);

        // 执行转换
        ColdStorageProjectVO vo = EntityConverter.toProjectVO(entity);

        // 验证结果
        assertNotNull(vo);
        assertEquals("test-id-123", vo.getId());
        assertEquals("PROJ001", vo.getProjectCode());
        assertEquals("测试项目", vo.getProjectName());
        assertEquals("测试公司", vo.getCompanyName());
        assertEquals("张三", vo.getProjectManager());
        assertEquals("13800138000", vo.getManagerPhone());
        assertEquals(1, vo.getProjectStatus());
        assertEquals("测试备注", vo.getProjectRemark());
        assertEquals(now.getTime(), vo.getCreatorTime());
        assertEquals(now.getTime(), vo.getLastModifyTime());
    }

    @Test
    void testToProjectVO_NullEntity() {
        // 测试空实体
        ColdStorageProjectVO vo = EntityConverter.toProjectVO(null);
        assertNull(vo);
    }

    @Test
    void testToProjectVO_PartialFields() {
        // 准备测试数据 - 部分字段为空
        ColdStorageProjectEntity entity = new ColdStorageProjectEntity();
        entity.setId("test-id-456");
        entity.setProjectCode("PROJ002");
        entity.setProjectName("测试项目2");
        // 其他字段保持null

        // 执行转换
        ColdStorageProjectVO vo = EntityConverter.toProjectVO(entity);

        // 验证结果
        assertNotNull(vo);
        assertEquals("test-id-456", vo.getId());
        assertEquals("PROJ002", vo.getProjectCode());
        assertEquals("测试项目2", vo.getProjectName());
        assertNull(vo.getCompanyName());
        assertNull(vo.getProjectManager());
        assertNull(vo.getManagerPhone());
        assertNull(vo.getProjectStatus());
        assertNull(vo.getProjectRemark());
        assertNull(vo.getCreatorTime());
        assertNull(vo.getLastModifyTime());
    }

    @Test
    void testToProjectVOList_EmptyList() {
        // 测试空列表
        var result = EntityConverter.toProjectVOList(null);
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void testToProjectVOList_MultipleEntities() {
        // 准备测试数据
        ColdStorageProjectEntity entity1 = new ColdStorageProjectEntity();
        entity1.setId("id-1");
        entity1.setProjectCode("PROJ001");
        entity1.setProjectName("项目1");

        ColdStorageProjectEntity entity2 = new ColdStorageProjectEntity();
        entity2.setId("id-2");
        entity2.setProjectCode("PROJ002");
        entity2.setProjectName("项目2");

        var entities = java.util.Arrays.asList(entity1, entity2);

        // 执行转换
        var voList = EntityConverter.toProjectVOList(entities);

        // 验证结果
        assertNotNull(voList);
        assertEquals(2, voList.size());
        assertEquals("id-1", voList.get(0).getId());
        assertEquals("PROJ001", voList.get(0).getProjectCode());
        assertEquals("项目1", voList.get(0).getProjectName());
        assertEquals("id-2", voList.get(1).getId());
        assertEquals("PROJ002", voList.get(1).getProjectCode());
        assertEquals("项目2", voList.get(1).getProjectName());
    }
} 
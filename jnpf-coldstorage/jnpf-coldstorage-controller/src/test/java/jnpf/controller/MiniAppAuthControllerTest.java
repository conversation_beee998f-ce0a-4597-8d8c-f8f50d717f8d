package jnpf.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import jnpf.base.ActionResult;
import jnpf.model.MiniAppLoginForm;
import jnpf.model.MiniAppLoginVO;
import jnpf.model.ColdStorageCustomerVO;
import jnpf.model.SendSmsCodeForm;
import jnpf.model.CustomerRegisterForm;
import jnpf.service.MiniAppAuthService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 小程序认证Controller测试类
 *
 * @版本： V5.2.0
 * @版权： 杭州多协信息技术有限公司（https://www.duoxieyun.com）
 * @作者： 多协开发组
 * @日期： 2025-01-27
 */
@ExtendWith(MockitoExtension.class)
class MiniAppAuthControllerTest {

    @Mock
    private MiniAppAuthService miniAppAuthService;

    @InjectMocks
    private MiniAppAuthController miniAppAuthController;

    private MockMvc mockMvc;
    private ObjectMapper objectMapper;

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.standaloneSetup(miniAppAuthController).build();
        objectMapper = new ObjectMapper();
    }

    /**
     * 测试小程序登录 - 成功
     */
    @Test
    void testLoginSuccess() throws Exception {
        // 准备测试数据
        MiniAppLoginForm loginForm = new MiniAppLoginForm();
        loginForm.setLoginType("1"); // 账号密码登录
        loginForm.setMobilePhone("13800138000");
        loginForm.setPassword("123456");

        MiniAppLoginVO loginVO = new MiniAppLoginVO();
        loginVO.setToken("test-token");
        loginVO.setRefreshToken("test-refresh-token");
        loginVO.setExpiresIn(7200L);

        // Mock服务层方法
        when(miniAppAuthService.login(any(MiniAppLoginForm.class), anyString())).thenReturn(loginVO);

        // 执行测试
        mockMvc.perform(post("/api/coldstorage/miniapp/auth/login")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(loginForm)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.token").value("test-token"));

        // 验证服务层方法被调用
        verify(miniAppAuthService).login(any(MiniAppLoginForm.class), anyString());
    }

    /**
     * 测试小程序登录 - 失败
     */
    @Test
    void testLoginFailure() throws Exception {
        // 准备测试数据
        MiniAppLoginForm loginForm = new MiniAppLoginForm();
        loginForm.setLoginType("1");
        loginForm.setMobilePhone("13800138000");
        loginForm.setPassword("wrong-password");

        // Mock服务层方法抛出异常
        when(miniAppAuthService.login(any(MiniAppLoginForm.class), anyString()))
                .thenThrow(new RuntimeException("登录失败"));

        // 执行测试
        mockMvc.perform(post("/api/coldstorage/miniapp/auth/login")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(loginForm)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(400));

        // 验证服务层方法被调用
        verify(miniAppAuthService).login(any(MiniAppLoginForm.class), anyString());
    }

    /**
     * 测试获取用户信息 - 成功
     */
    @Test
    void testVerifyTokenSuccess() throws Exception {
        // 准备测试数据
        String token = "test-token";
        ColdStorageCustomerVO customerVO = new ColdStorageCustomerVO();
        customerVO.setId("customer-id-1");
        customerVO.setCustomerName("测试客户");
        customerVO.setMobilePhone("13800138000");

        // Mock服务层方法
        when(miniAppAuthService.verifyToken(token)).thenReturn(customerVO);

        // 执行测试
        mockMvc.perform(get("/api/coldstorage/miniapp/auth/userinfo")
                .header("Authorization", "Bearer " + token))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.id").value("customer-id-1"));

        // 验证服务层方法被调用
        verify(miniAppAuthService).verifyToken(token);
    }

    /**
     * 测试获取用户信息 - 失败
     */
    @Test
    void testVerifyTokenFailure() throws Exception {
        // 准备测试数据
        String token = "invalid-token";

        // Mock服务层方法
        when(miniAppAuthService.verifyToken(token)).thenReturn(null);

        // 执行测试
        mockMvc.perform(get("/api/coldstorage/miniapp/auth/userinfo")
                .header("Authorization", "Bearer " + token))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(401));

        // 验证服务层方法被调用
        verify(miniAppAuthService).verifyToken(token);
    }

    /**
     * 测试刷新Token - 成功
     */
    @Test
    void testRefreshTokenSuccess() throws Exception {
        // 准备测试数据
        String refreshToken = "refresh-token";
        MiniAppLoginVO loginVO = new MiniAppLoginVO();
        loginVO.setToken("new-token");
        loginVO.setRefreshToken("new-refresh-token");

        // Mock服务层方法
        when(miniAppAuthService.refreshToken(refreshToken)).thenReturn(loginVO);

        // 执行测试
        mockMvc.perform(post("/api/coldstorage/miniapp/auth/refresh")
                .param("refreshToken", refreshToken))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.token").value("new-token"));

        // 验证服务层方法被调用
        verify(miniAppAuthService).refreshToken(refreshToken);
    }

    /**
     * 测试刷新Token - 失败
     */
    @Test
    void testRefreshTokenFailure() throws Exception {
        // 准备测试数据
        String refreshToken = "invalid-refresh-token";

        // Mock服务层方法
        when(miniAppAuthService.refreshToken(refreshToken)).thenReturn(null);

        // 执行测试
        mockMvc.perform(post("/api/coldstorage/miniapp/auth/refresh")
                .param("refreshToken", refreshToken))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(401));

        // 验证服务层方法被调用
        verify(miniAppAuthService).refreshToken(refreshToken);
    }

    /**
     * 测试退出登录
     */
    @Test
    void testLogout() throws Exception {
        // 准备测试数据
        String token = "test-token";

        // Mock服务层方法
        doNothing().when(miniAppAuthService).logout(token);

        // 执行测试
        mockMvc.perform(post("/api/coldstorage/miniapp/auth/logout")
                .header("Authorization", "Bearer " + token))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));

        // 验证服务层方法被调用
        verify(miniAppAuthService).logout(token);
    }

    /**
     * 测试发送短信验证码
     */
    @Test
    void testSendSmsCode() throws Exception {
        // 准备测试数据
        SendSmsCodeForm smsForm = new SendSmsCodeForm();
        smsForm.setMobilePhone("13800138000");

        // Mock服务层方法
        doNothing().when(miniAppAuthService).sendSmsCode(any(SendSmsCodeForm.class));

        // 执行测试
        mockMvc.perform(post("/api/coldstorage/miniapp/auth/send-code")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(smsForm)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));

        // 验证服务层方法被调用
        verify(miniAppAuthService).sendSmsCode(any(SendSmsCodeForm.class));
    }

    /**
     * 测试客户注册
     */
    @Test
    void testRegister() throws Exception {
        // 准备测试数据
        CustomerRegisterForm registerForm = new CustomerRegisterForm();
        registerForm.setMobilePhone("13800138000");
        registerForm.setPassword("123456");
        registerForm.setVerifyCode("123456");

        MiniAppLoginVO loginVO = new MiniAppLoginVO();
        loginVO.setToken("register-token");
        loginVO.setRefreshToken("register-refresh-token");

        // Mock服务层方法
        when(miniAppAuthService.register(any(CustomerRegisterForm.class))).thenReturn(loginVO);

        // 执行测试
        mockMvc.perform(post("/api/coldstorage/miniapp/auth/register")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(registerForm)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.token").value("register-token"));

        // 验证服务层方法被调用
        verify(miniAppAuthService).register(any(CustomerRegisterForm.class));
    }

    /**
     * 测试微信小程序登录
     */
    @Test
    void testWechatLogin() throws Exception {
        // 准备测试数据
        String wxCode = "wx-code";
        String wxUserInfo = "wx-user-info";

        MiniAppLoginVO loginVO = new MiniAppLoginVO();
        loginVO.setToken("wechat-token");
        loginVO.setRefreshToken("wechat-refresh-token");

        // Mock服务层方法
        when(miniAppAuthService.wechatLogin(eq(wxCode), eq(wxUserInfo), anyString())).thenReturn(loginVO);

        // 执行测试
        mockMvc.perform(post("/api/coldstorage/miniapp/auth/wechat-login")
                .param("wxCode", wxCode)
                .param("wxUserInfo", wxUserInfo))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.token").value("wechat-token"));

        // 验证服务层方法被调用
        verify(miniAppAuthService).wechatLogin(wxCode, wxUserInfo, anyString());
    }

    /**
     * 测试绑定微信账号
     */
    @Test
    void testBindWechat() throws Exception {
        // 准备测试数据
        String customerId = "customer-id-1";
        String wxCode = "wx-code";
        String wxUserInfo = "wx-user-info";

        // Mock服务层方法
        doNothing().when(miniAppAuthService).bindWechat(customerId, wxCode, wxUserInfo);

        // 执行测试
        mockMvc.perform(post("/api/coldstorage/miniapp/auth/bind-wechat")
                .param("customerId", customerId)
                .param("wxCode", wxCode)
                .param("wxUserInfo", wxUserInfo))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));

        // 验证服务层方法被调用
        verify(miniAppAuthService).bindWechat(customerId, wxCode, wxUserInfo);
    }

    /**
     * 测试解绑微信账号
     */
    @Test
    void testUnbindWechat() throws Exception {
        // 准备测试数据
        String customerId = "customer-id-1";

        // Mock服务层方法
        doNothing().when(miniAppAuthService).unbindWechat(customerId);

        // 执行测试
        mockMvc.perform(post("/api/coldstorage/miniapp/auth/unbind-wechat")
                .param("customerId", customerId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));

        // 验证服务层方法被调用
        verify(miniAppAuthService).unbindWechat(customerId);
    }
} 
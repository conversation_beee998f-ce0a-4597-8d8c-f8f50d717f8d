package jnpf.permission.model.position;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @version V3.1.0
 * @copyright 引迈信息技术有限公司
 * @date 2021/3/12 15:31
 */
@Data
public class PositionConditionSelectorVO extends PositionSelectorVO implements Serializable {

    @Schema(description = "组织id树")
    @JsonIgnore
    private String organizeIdTree;

}

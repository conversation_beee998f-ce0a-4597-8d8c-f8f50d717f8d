AD101=Interface cannot be accessed
AD102=System exception
AD103=Frequent operations
AD104=No access permission, please contact the administrator for authorization
AD105=Authentication failed, unable to access system resources
AD106=Invalid internal authentication, unable to access system resources
COD001=Set condition filtering to obtain an empty target
COPY001=The length of the copied name exceeds the limit length
DB001=The data type encoding does not comply with the standard (please pay attention to capitalization). MySQL , SQLServer , Oracle , DM , KingbaseES , PostgreSQL
DB002=Please check 1. Connection information 2. Network communication 3. Database service startup status. Details: {0}
DB003=Unable to find the corresponding database through URL
DB004=The query result set is empty.
DB005=Corresponding database type not found: {0} ({1})
DB006=No corresponding data type conversion found
DB007=There are duplicate import table names
DB008=The table data does not match the current operation database: {0} ->{1}
DB009=Table information not found: {0}
DB010=Database {0}, table not found: {1}
DB011=The joint primary key class is missing the '{0}' field value
DB012=Indicates that obtaining the corresponding numerical value has failed
DB013=Currently, the {0} data type: {1} is not supported
DB014=Field '{1}' in table '{0}' is the primary key and does not allow data type '{2}'
DB015=Field SQL statement not found
DB016=No initial fields
DB017=SQL exception: {0}
DB018=Please add the corresponding data table to the database
DB019=Add failed
DB101=The system comes with a table that cannot be deleted
DB102=The system comes with a table that cannot be edited
DB201=The table has already been used and cannot be deleted
DB202=The table has been used and cannot be edited
DB301=Database connection successful
DB302=Database connection failed
ETD101=Operation failed, original file does not exist
ETD102=Cannot find parent level
ETD103=Cannot move to one's own folder
ETD104=Unable to find this order
ETD105=Successfully created 10000 pieces of data
ETD106=Acquisition failed
ETD107=Account authentication error
ETD108=You haven't set up an email account yet
ETD109=File export failed
ETD110=The file format is incorrect
ETD111=File not found
ETD112=This record is associated with a reference and cannot be deleted
ETD113=Prevent malicious creation of excessive data
ETD114=Save failed, please log in again
ETD115=Please enter the preview URL
ETD116=Please choose the correct preview method
ETD117=More than 1000 pieces of data
EXIST001=The name cannot be repeated
EXIST002=Encoding cannot be repeated
EXIST003=The original name already exists
EXIST004=Folder names cannot be duplicated
EXIST005=Template name exceeds the limit length
EXIST101=Duplicate name, please re-enter
EXIST102=Duplicate code, please re-enter
EXIST103=Cannot be repeated
FA001=Data does not exist
FA002=Update failed, data does not exist
FA003=Delete failed, data does not exist
FA004=Copy failed, data does not exist
FA005=Sending failed, data does not exist
FA006=Download failed, data does not exist
FA007=Operation failed, data does not exist
FA008=Stop failed, data does not exist
FA009=Termination failed, data does not exist
FA010=Restoration failed, data does not exist
FA011=Publication failed, data does not exist
FA012=Acquisition failed, data does not exist
FA013=Interface modification failed, data does not exist
FA014=Failed to update interface status, data does not exist
FA015=Preview failed, data does not exist
FA016=Delete failed, the folder contains data
FA017=The file format is incorrect
FA018=file does not exist
FA019=Invalid
FA020=No information found
FA021=Operation failed! You do not have permission to operate
FA022=Update failed! You do not have permission to operate (roles can only be operated by super administrators)
FA023=Update failed! User already bound, unable to switch organizations
FA024=Delete failed! User bound
FA025=The permission within the organization is empty, and the organization switch has failed!
FA026=Update failed, associated organization does not exist, please log in again or refresh the page
FA027=The menu under this system is empty, and the system switch has failed
FA028=Failed to add data
FA029=Data modification failed
FA030=Update failed! User bound, unable to modify status
FA031=The organization does not have permission for this application, and the switch has failed
FA032=The uploaded file cannot be empty
FA033=File upload failed!
FA034=Illegal request, lack of authentication information
FA035=Failed to obtain tenant specified data source information
FA036=Common data already exists
FA037=Interface request failed
FA038=File storage path error
FA039=The link has expired
FA040=Preview failed, please check if the file type is standardized
FA041=Preview failed, please upload the file again
FA042=Please enter the correct file format
FA043=There is a file with the same name!
FA044=The file does not exist
FA045=Deleting file: {0} failed
FA046=File read failed
FA047=No files found
FA048=The original ID of WeChat official account cannot be duplicated
FA049=This record is associated with the 'Message Sending Configuration' reference and cannot be disabled
FA050=This record is associated with the 'Message Sending Configuration' reference and cannot be deleted
FA051=The interface has been configured with encryption, but data decryption has failed
FA052=This identity has not been assigned permissions
FA101=Save failed
FA102=Update failed
FA103=Delete failed
FA104=Acquisition failed
FA105=Preview failed, please save in preview data first
FA106=Preview failed, dead loop in cell configuration
FM001=Interface not found
FM002=The form information does not exist
FM003=Duplicate sub table
FM004=The maximum limit for copying this template has been reached. Please copy the source template!
FM005=This form has been referenced by the process and cannot be deleted!
FM006=This form has not been published, and the form content cannot be rolled back
FM007=The form content in this template is empty and cannot be published
FM008=This feature has not imported the process form
FM009=Process not designed, please design the process first!
FM010=This function process is in a disabled state!
FM011=Table [{0}] has no primary key!
FM012=Primary key policy: {0}, inconsistent with the primary key policy of table [{1}]!
FM013=Table addition error: {0}
GT101=success
GT102=fail
GT103=Verification error
GT104=abnormal
GT105=Login expired, please log in again
GT106=Your account has been logged in elsewhere and has been forcibly kicked out
GT107=Token verification failed
GT108=Request exceeds the maximum number
IMP001=Import successful
IMP002=Import failed, file format error
IMP003=Import failed, data already exists
IMP004=Import failed, data error
IMP005=Export failed
IMP006=The imported data format is incorrect
IMP007=Repeat
IMP008=Name
IMP009=Encoding
IMP010=Import failed, unable to query superior classification
IMP011=Please select the export field
LOG001=Account abnormality
LOG002=Cancellation successful
LOG004=Account exception, please contact the administrator to modify the organization information
LOG005=Account not activated
LOG006=Account has been disabled
LOG007=The account has been deleted
LOG010=This IP is not on the whitelist
LOG011=Login failed, user has not yet bound role
LOG012=The account has been locked. Please contact the administrator to unlock it
LOG101=Account or password error
LOG102=Account error, please re-enter
LOG103=Please enter the verification code
LOG104=Verification code error
LOG105=Failed to connect to tenant service, please try again later
LOG106=SMS verification code error
LOG107=The verification code has expired
LOG108=Please wait {0} minutes before logging in, or contact the administrator to unlock
LOG109=Tenant login failed, please use mobile verification code to log in
LOG110=Database exception, please contact the administrator for handling
LOG111=Single sign on has been enabled, this login method is not supported
LOG112=This login method is not supported
LOG113=Tenant information not set
LOG114=Tenant code cannot be empty
LOG115=Tenant information acquisition failed
LOG116=This verification is not supported
LOG117=SMS verification code verification failed: {0}
LOG118=Tenant library name is empty
LOG201=Old password incorrect
LOG202=Modified successfully, please remember the new password
LOG203=Modification failed, account does not exist
LOG204=Modification failed, new password cannot be the same as old password
LOG205=Password reset successful
LOG206=Password reset failed
MSERR101=Sending failed, reason for failure: SMTP service is empty
MSERR102=Sending failed, reason for failure: sender email is empty
MSERR103=Sending failed, reason for failure: sender password is empty
MSERR104=Sending failed, reason for failure: recipient is empty
MSERR105=Sending failed. Reason for failure: The format of the email account for {0} is incorrect!
MSERR106=Sending failed. Reason for failure: The email account for {0} is empty!
MSERR107=Sending failed. Reason for failure: All email addresses corresponding to the recipient are empty
MSERR108=Sending failed. Reason for failure: {0}
MSERR109=Connection successful
MSERR110=Connection failed. Reason for failure: {0}
MSERR111=has been sent
MSERR112=The content cannot contain<symbol
MSERR113=There are currently no unread messages
MSERR114=Custom template encoding cannot use system template encoding rules
MSERR115=Creation failed, multiple title parameters exist
MSERR116=Creation failed, title parameter does not exist
MSERR117=Update failed, multiple title parameters exist
MSERR118=Update failed, title parameter does not exist
MSERR119=Please go to the system synchronization settings first and configure the DingTalk account
MSERR120=Please go to the system synchronization settings first and configure the enterprise WeChat account
MSERR121=Configuration template has no data, unable to test
OA001=User login
OA002=equipment
OA003=TOKEN
OA004=User logout
OA005=User kicked out
OA006=User substitution
OA007=Login exception
OA008=Login to obtain system configuration failed
OA009=The user has not been assigned permissions
OA010=Only supports PC access, not APP access
OA011=The application does not exist
OA012=The current application has been disabled
OA013=Login password decryption failed
OA014=Cancellation successful
OA015=Login successful
OA016=Login ticket has expired
OA017=Third party unbound account
OA018=Access to this login interface is not allowed
OA019=Account does not exist
OA020=Account or password error, please re-enter
OA021=Verification successful
OA022=Restricting sessions and not allowing access to the system
OA023=Administrators cannot log out
OA024=Login failed
OA025=Super administrator
OA026=Ordinary administrator
OA027=Ordinary users
OA028=Unknown source
PRI001=Print template does not exist
PRI002=There is no dictionary classification for printDev in the digital dictionary
PRI003=Article 1 SQL statement: Retrieve multiple header information
PRI004=Article 1 SQL statement: Header information not found
PRI005=The {index} SQL statement:
PRI006=The maximum limit for copying this template has been reached. Please copy the source template
PRI007=SQL syntax error
PRI008=This report has been deleted
PS001=This record is associated with reference '{0}' and cannot be deleted
PS003=organization
PS004=post
PS005=user
PS006=role
PS007=Account cannot be empty
PS008=Name cannot be empty
PS009=The user limit has been reached
PS010=Permission has been changed, please log in again
PS011=Password has been changed, please log in again
PS012=Type cannot be empty
PS013=The current institution ID cannot be the same as the parent institution ID
PS014=This application has been disabled
PS015=Unable to set the current user as a graded administrator
PS016=Unable to set the super manager as a graded administrator
PS017=Unable to set current user operation permissions
PS018=Unbinding failed
PS019=Third party login not configured
PS020=Gender cannot be empty
PS021=Unable to disable administrator user
PS022=Administrators can only modify themselves and cannot modify other administrators
PS023=Unable to modify administrator account
PS024=The immediate supervisor cannot be oneself
PS025=My immediate supervisor cannot be my subordinate user
PS026=Unable to delete administrator account
PS027=This user is a department supervisor and cannot be deleted
PS028=This user has subordinates and cannot be deleted
PS029=Unable to modify administrator account status
PS030=User information has been changed, please log in again
PS031=The application has been deleted
PS032=The organization does not have permission for this application, switching failed
PS033=Work handover successful!
PS034=Work handover cannot be transferred to the administrator
PS035=The handover of work cannot be transferred to me
SC001=Operation failed, task does not exist
SU000=Success
SU001=New created successfully
SU002=Saved successfully
SU003=Delete successfully
SU004=Update successful
SU005=Operation successful
SU006=Submitted successfully, please be patient and wait
SU007=Copy successful
SU008=Stop successfully
SU009=Termination successful
SU010=Restoration successful
SU011=Published successfully
SU012=Sent successfully
SU013=Interface modification successful
SU014=Successfully updated interface status
SU015=Upload successful
SU016=Setting successful
SU017=Verification successful
SU018=Added successfully
SU019=Successfully obtained
SU020=Rollback successful
SU021=Removal successful
SU022=Query successful
SYS001=The area code cannot be duplicated
SYS002=Delete failed, there is currently child node data
SYS003=The document has been used and cannot be deleted
SYS004=Cleanup successful
SYS005=Interface created successfully
SYS006=The current SQL contains sensitive words: {0}
SYS007=Interface request successful
SYS008=Interface does not comply with specifications
SYS009=Variable names cannot contain sensitive characters
SYS010=Variable name already exists
SYS011=Database connections cannot be the same
SYS012=Please check, data cannot be synchronized under the same database
SYS013=Synchronization failed: {0}
SYS014=There are dictionary values under the dictionary type that cannot be deleted
SYS015=Template does not exist
SYS016=The current directory contains data and cannot modify the type
SYS017=Delete failed, please delete the submenu first
SYS018=The current import menu is the {0} side menu, please import under the corresponding module!
SYS019=Please create a directory under the top-level node before importing the menu
SYS020=This field has already been used in scheme {0}
SYS021=Modification failed, this plan does not allow editing
SYS022=Encoding error
SYS023=Request error occurred!
SYS024=Unable to obtain data!
SYS025=Failed to obtain the enterprise WeChat access_token
SYS026=Syncing in progress, please try again later
SYS027=Please synchronize the department from the enterprise WeChat to the local first
SYS028=Please synchronize the department from DingTalk to the local first
SYS029=The number of verification code digits cannot exceed 6
SYS030=The number of verification code digits cannot be less than 3
SYS031=Test failed to send message connection: {0}
SYS032=Test message sending connection successful
SYS033=Test organization synchronization connection failed: {0}
SYS034=Test organization synchronized connection successfully
SYS035=Test connection type error
SYS036=Test nail connection failed:
SYS037=Test connection successful
SYS038=Abnormal extraction of table information
SYS039=Delete failed, please delete the menu and portal under this application first
SYS040=Delete failed, please delete the menu under this app first
SYS041=Delete failed, please delete the portal under this application first
SYS042=This schedule has been deleted
SYS043=The last data cannot be deleted
SYS044=The enabled version cannot be deleted
SYS045=The archived version cannot be deleted
SYS046=Dataset cannot have duplicate names
SYS047=SQL statements only support query statements
SYS048=SQL statements must include the @ formId condition
SYS049=Synchronization in progress, please wait
SYS050=Only letters, numbers, dots, horizontal lines, and underscores can be entered, starting with a letter
SYS051=Translation tags cannot be duplicated
SYS052=At least one translation language should be filled in
SYS053=Failed to obtain DingTalk access_token
SYS101=Update failed, the main system does not allow disabling
SYS102=The main system does not allow deletion
SYS103=The system is used in common language for approval and cannot be deleted
SYS104=Update failed, the main system does not allow modification of encoding
SYS105=Common phrases already exist
SYS121=The interface currently only supports HTTP and HTTPS methods
SYS122=Interface request failed
SYS123=Interface request failed, JS call failed, error: {0}
SYS124=Verification request timeout
SYS125=AppSecret error
SYS126=The usage period of appId has expired
SYS127=AppId parameter error
SYS128={0} cannot use system, development language, and database keyword naming
SYS129=The current data source does not support full connectivity
SYS130=Title cannot be empty
SYS131=The end time must be later than the start time
SYS132=The end of repetition must be later than the start time
VS001=When synchronizing to the process, {0}
VS002=Publication failed, process not designed!
VS003=Failed to generate a table without a table
VS004=release
VS005=preview
VS006=download
VS007=Synchronization successful
VS008=Rollback failed, wireless version temporarily available
VS009=Parameter parsing error!
VS010=Invalid link
VS011=Password error
VS012=The function form was not found
VS013=The form external link has not been opened!
VS014=The download link has expired
VS015=Field cannot be empty
VS016=Path error
VS017=Integrated assistant disabled
VS018=The table specification name cannot be repeated
VS019=Standard names cannot use system keywords or JAVA keywords
VS020=Field specification names cannot be duplicated
VS021=The naming of '{0}' does not conform to the standard
VS022=Primary key strategy: [Snowflake ID], the primary key setting for table [{0}] is not supported!
VS023=Primary key strategy: [auto increment ID], the primary key setting for table [{0}] is not supported!
VS024=The form does not exist or has not been published!
VS025=Process initiator not obtained
VS026=The first two letters of the standard name must be lowercase
VS027=The automatically generated ã {0} ã exceeds the length and failed to submit!
VS028=Up to 5 new views can be created
VS401=The form content in this template is empty, unable to
VS402=The list content in this template is empty, unable to
VS403=This feature is not available without configuring the process
VS404=Single line input cannot be repeated
VS405=The original data of the current form has been adjusted. Please re-enter the page to edit and submit the data
VS406=The process of configuring this feature is currently disabled
VS407=The header name cannot be changed, and header rows cannot be deleted
VS408=Please select at least one data table
VS409=Main table information not found
VS410=Please import the corresponding function's JSON file
VS411=The same function already exists
VS412=This form has been deleted
VS413=The application cannot be empty
VS414=There are duplicates in the portal data information
VS415=The portal has been deleted
WF001=Audit successful
WF002=Returned successfully
WF003=Successfully transferred
WF004=Signed successfully
WF005=The current process has been returned and cannot be revoked
WF006=The process has been withdrawn and cannot be repeated
WF007=Withdrawal failed, the data cannot be recalled
WF008=Withdrawal successful
WF009=The functional process cannot be terminated
WF010=Successfully assigned
WF011=Batch operation completed
WF012=This process cannot be operated
WF013=Resurrected successfully
WF014=Change successful
WF015=Hanging successfully
WF016=recovery was successful
WF017=The principal and the principal are the same, and the commission has failed
WF018=Operation failed, there are commissions with the same process at the same time
WF019=Operation failed, same process at the same time, cannot delegate to each other
WF020=The functional process cannot be deleted
WF021=Cannot delete
WF022=Urged successfully
WF023=No reminder found
WF024=This function has been referenced by the process. Please select the associated function again
WF025=Enabling failed, process not designed
WF026=Successfully enabled
WF027=Disabled successfully
WF028=There is a work order task flow in this version, which cannot be deleted
WF029=You do not have the authority to initiate this process
WF030=Form not found
WF031=Reviewed completed
WF032=Freeze cannot be operated
WF033=The steering node does not exist or is configured incorrectly
WF034=Steering failed, steering node not approved
WF035=Returned to your approval, cannot initiate a return again
WF036=The process has been processed and cannot be revoked
WF037=The current process contains sub processes and cannot be revoked
WF038=The sub process cannot be withdrawn
WF039=The next node cannot be batch approved for selecting branches
WF040=The conditional process includes candidates who cannot be approved in bulk
WF041=The process work order has been terminated
WF042=The process work order has been withdrawn
WF043=This node has no data and cannot be resurrected
WF044=This process does not support changes
WF045=The current node has sub processes that cannot be changed
WF046=Return node contains subprocesses, return failed
WF047=The current node has not been approved and cannot be returned
WF048=The process is in a suspended state and cannot be operated
WF049=The current process is running and cannot be deleted
WF050=Already suspended and cannot be deleted
WF051=No deletion permission
WF052=The main version has no content
WF053=Process not enabled
WF054=Process code cannot be duplicated
WF055=Inconsistent process form, please select again
WF056=This process is generated by online development and cannot be directly deleted. Please remove the relevant functions in the functional design
WF057=The work order task flow within this process has not ended and cannot be deleted
WF058=The current process is running and cannot be resubmitted
WF059=Process failed to initiate approval automatically
WF060=Reject node cannot be a subprocess
WF061=Please contact the administrator if there are no approvers for the next node
WF062=The form has been referenced, please select again
WF063=The process has been initiated and cannot be deleted
WF064=The task does not exist or has already been processed
WF065=Reject successfully
WF066=Agreed successfully
WF067=Successfully co organized
WF068=Co organizer saved successfully
WF069=Successful visa reduction
WF070=Revocation successful
WF071=The last data cannot be deleted
WF072=The enabled version cannot be deleted
WF073=The archived version cannot be deleted
WF074=Pause successful
WF075=Unable to circulate if the conditions are not met
WF076=Node does not exist
WF077=Process cannot be revoked
WF078=Process not agreed upon, cannot be revoked
WF079=Archive exception
WF080=The selected data cannot be returned for signing
WF081=Unable to add signature
WF082=Cannot reduce visa
WF083=Cannot be returned
WF084=Unable to transfer for review
WF085=Cannot co organize
WF086=Unable to batch approve
WF087=Handling not signed for
WF088=The processing has not started yet
WF089=Process publishing failed
WF090=Process publishing failed
WF091=Process submission failed
WF092=Failed to retrieve the current task of the engine
WF093=Process deletion failed
WF094=Failed to retrieve the set of outgoing lines
WF095=Failed to obtain task nodes after the line
WF096=Failed to retrieve the set of next level task nodes
WF097=Failed to retrieve the set of higher-level task nodes
WF098=Task completion failed
WF099=Failed to obtain process instance
WF100=Failed to retrieve nodes that have not passed through
WF101=Failed to obtain subsequent nodes of the node
WF102=Failed to obtain rollback nodes
WF103=Return failed
WF104=Node jump failed
WF105=Compensation failed
WF106=Cannot add a signature to oneself
WF107=Cannot transfer the review to oneself
WF108=Cannot co organize for oneself
WF109=One additional signatory must be retained
WF110=Approval exception cannot be revoked
WF111=The selected process includes conditional candidates
WF112=The process corresponding to the selected data has been paused
WF113=Has been paused and cannot be deleted
WF114=The process is in a paused state and cannot be operated
WF115=The process has been accepted and cannot be deleted
WF116=Cannot add signature to the client
WF117=Cannot be transferred to the client for review
WF118=Cannot co organize with the client
WF119=Flow conditions have been set, and batch approval is not possible
WF120=Next node approval exception, unable to batch approve
WF121=The sub process failed to initiate approval automatically
WF122=Process does not exist
WF123=The process is in a terminated state and cannot be operated
WF124=The process has initiated data and cannot be deleted!
WF125=You did not initiate the delegation process
WF126=The revocation process cannot be transferred for review
WF127=The revocation process cannot be returned
WF128=The user has been approved, please reopen the interface
WF129=The client no longer has the authority for this process
WF130=Administrators cannot create new delegates/agents
WF131=Cannot select admin
WF132=Accepted by someone, cannot be edited
WF133=The circulation conditions are not met, and automatic approval cannot be initiated
WF134=The first approval node sets candidates and cannot automatically initiate approval
WF135=The first approval node is abnormal and cannot automatically initiate approval
WF136=Unable to find initiator, initiation failed
WF137=Proxy and principal are the same, proxy failed
WF138=There is unsigned data, unable to close
WF139=The process has triggered a task and cannot be deleted
WF144=Operation failed, there are agents with the same process at the same time
WF145=Operation failed, there is the same process at the same time, cannot proxy each other
app.apply.expandData=Expand data
app.apply.location.location=Location
app.apply.location.modalTitle=Select position
app.apply.location.relocation=Relocation
app.apply.noMoreData=No More Data
app.apply.pleaseKeyword=Please enter keywords to search
app.apply.screen=Screen
app.apply.sort=Sort
app.my.accountSecurity=accountSecurity
app.my.agencyMe=AgencyMe
app.my.allFlow=AllFlow
app.my.entrustedAgency=EntrustedAgency
app.my.entrustMe=EntrustMe
app.my.flowSelect=Flow Select
app.my.logOut=Log out
app.my.myAgency=MyAgency
app.my.myEntrust=MyEntrust
app.my.organization=Organization
app.my.position=Position
app.my.scanCode=ScanCode
app.my.setting=Setting
app.my.settings.About=About
app.my.settings.changePassword=Change Password
app.my.settings.contact=Contact
app.my.settings.language=Language
app.my.settings.privacyPolicy=Privacy Policy
app.my.settings.userAgreement=UserA Greement
app.my.sto=Stop
app.my.subordinates=Subordinates
app.my.switchIdentity=SwitchIdentity
common.add1Text=Add
common.add2Text=Add
common.addText=Add
common.back=Back
common.batchDelText=Batch Delete
common.batchDelTip=Are you sure you want to delete these data? Do you want to continue?
common.batchPrintText=Batch Print
common.cancelText=Cancel
common.chooseText=Please select
common.chooseTextPrefix=Please select
common.cleanText=Clean Up
common.closeList=Close List
common.closeText=Close
common.collapseAll=Collapse All
common.continueAndAddText=OK & Add
common.continueText=OK & Continue
common.copyText=Copy
common.dark=Dark
common.delText=Delete
common.delTip=This operation will permanently delete the data. Do you want to continue?
common.detailText=Detail
common.drawerSearchText=Please Enter Keyword
common.editText=Edit
common.enterKeyword=Please Enter
common.expandAll=Expand All
common.exportText=Export
common.importText=Import
common.inputPlaceholder=Please enter
common.inputText=Please enter
common.inputTextPrefix=Please enter
common.keyword=Keyword
common.leftTreeSearchText=Enter Keyword
common.light=Light
common.loadingText=Loading...
common.moreText=More
common.next=Next
common.nextRecord=Next
common.noData=No Data
common.okText=OK
common.prev=Prev
common.previewText=Preview
common.prevRecord=Prev
common.printText=Print
common.queryText=Search
common.redo=Refresh
common.redoText=redo
common.resetText=Reset
common.saveText=Save
common.searchText=Search
common.selectDataTip=Please select a piece of data
common.selectI18nCode=Select translation markers
common.selectPlaceholder=Please select
common.submitText=Submit
common.superQuery=Super Query
common.syncText=Sync
common.tipTitle=Tips
common.undoText=undo
component.app.searchNotData=No search results yet
component.app.toNavigate=to navigate
component.app.toSearch=to search
component.countdown.normalText=Get SMS code
component.countdown.sendText=Reacquire in {0}s
component.cropper.btn_reset=Reset
component.cropper.btn_rotate_left=Counterclockwise rotation
component.cropper.btn_rotate_right=Clockwise rotation
component.cropper.btn_scale_x=Flip horizontal
component.cropper.btn_scale_y=Flip vertical
component.cropper.btn_zoom_in=Zoom in
component.cropper.btn_zoom_out=Zoom out
component.cropper.modalTitle=Avatar upload
component.cropper.okText=Confirm and upload
component.cropper.preview=Preivew
component.cropper.selectImage=Select Image
component.cropper.uploadSuccess=Uploaded success!
component.drawer.cancelText=Close
component.drawer.loadingText=Loading...
component.drawer.okText=Confirm
component.excel.exportModalTitle=Export data
component.excel.fileName=File name
component.excel.fileType=File type
component.form.apiSelectNotFound=Wait for data loading to complete...
component.form.fold=Fold
component.form.maxTip=The number of characters should be less than {0}
component.form.unfold=Unfold
component.icon.copy=Copy icon successfully!
component.icon.placeholder=Click the select icon
component.icon.search=Search icon
component.jnpf.areaSelect.modalTitle=Select area
component.jnpf.calculate.storage=The data will also be saved and stored in the database
component.jnpf.calculate.unStorage=The data will not be saved
component.jnpf.common.allData=All data
component.jnpf.common.autoGenerate=Automatically generated by the system
component.jnpf.common.clearAll=Clear all
component.jnpf.common.selected=Selected
component.jnpf.dateRange.endPlaceholder=End date
component.jnpf.dateRange.startPlaceholder=Start date
component.jnpf.depSelect.modalTitle=Select department
component.jnpf.groupSelect.modalTitle=Select group
component.jnpf.iconPicker.modalTitle=Select icon
component.jnpf.iconPicker.searchPlaceholder=Please Enter Keyword
component.jnpf.iconPicker.select=Select
component.jnpf.iconPicker.ymCustom=ymCustom icon
component.jnpf.iconPicker.ymIcon=ymIcon icon
component.jnpf.location.location=Location
component.jnpf.location.modalTitle=Select position
component.jnpf.location.relocation=Relocation
component.jnpf.location.searchPlaceholder=Enter or click to select on the map
component.jnpf.numberRange.max=Max
component.jnpf.numberRange.min=Min
component.jnpf.organizeSelect.modalTitle=Select organize
component.jnpf.popupAttr.storage=The data will also be saved and stored in the database
component.jnpf.popupAttr.unStorage=The data will not be saved
component.jnpf.popupSelect.modalTitle=Select data
component.jnpf.posSelect.modalTitle=Select position
component.jnpf.relationFormAttr.storage=The data will also be saved and stored in the database
component.jnpf.relationFormAttr.unStorage=The data will not be saved
component.jnpf.roleSelect.modalTitle=Select role
component.jnpf.sign.operateTip=Please use the mouse to handwrite your signature in this area
component.jnpf.sign.signPlaceholder=Please signature
component.jnpf.sign.signTip=signature
component.jnpf.timeRange.endPlaceholder=End time
component.jnpf.timeRange.startPlaceholder=Start time
component.jnpf.userSelect.modalTitle=Select user
component.menu.search=Menu search
component.modal.cancelText=Close
component.modal.close=Close
component.modal.maximize=Maximize
component.modal.okText=Confirm
component.modal.restore=Restore
component.table.action=Action
component.table.index=No.
component.table.settingColumn=Column settings
component.table.settingColumnShow=Column display
component.table.settingDens=Density
component.table.settingDensDefault=Default
component.table.settingDensMiddle=Middle
component.table.settingDensSmall=Compact
component.table.settingFixedLeft=Fixed Left
component.table.settingFixedRight=Fixed Right
component.table.settingFullScreen=Full Screen
component.table.settingIndexColumnShow=Index Column
component.table.settingSelectColumnShow=Selection Column
component.table.status=Status
component.table.summary=Total
component.table.total=total of {total}
component.time.after=after
component.time.before=ago
component.time.days=days
component.time.hours=hours
component.time.just=just now
component.time.minutes=minutes
component.time.seconds=seconds
component.tree.checkStrictly=Hierarchical association
component.tree.checkUnStrictly=Hierarchical independence
component.tree.expandAll=Expand All
component.tree.reload=Reload
component.tree.selectAll=Select All
component.tree.unExpandAll=Collapse all
component.tree.unSelectAll=Cancel Select
component.upload.accept=Support {0} format
component.upload.acceptUpload=Only upload files in {0} format
component.upload.audio=audio
component.upload.buttonText=Upload
component.upload.checking=Checking
component.upload.choose=Select the file
component.upload.del=Delete
component.upload.download=Download
component.upload.downloadAll=Download all
component.upload.fileMaxNumber=Up to {0} files can be uploaded
component.upload.fileMaxSize=File size exceeds {size}{unit}
component.upload.fileName=File name
component.upload.fileReadError=File {0} reading error, please check the file
component.upload.fileSize=File size
component.upload.fileStatue=File status
component.upload.fileTypeCheck=Please select a file of {0} type
component.upload.image=image
component.upload.imageMaxNumber=Up to {0} images can be uploaded
component.upload.imageMaxSize=Image size exceeds {size}{unit}
component.upload.imgUpload=ImageUpload
component.upload.legend=Legend
component.upload.maxNumber=Only upload up to {0} files
component.upload.maxSize=A single file does not exceed {0}MB
component.upload.maxSizeMultiple=Only upload files up to {0}MB!
component.upload.operating=Operating
component.upload.paused=Paused
component.upload.preview=Preview
component.upload.reUploadFailed=Re-upload failed files
component.upload.save=Save
component.upload.saveError=There is no file successfully uploaded and cannot be saved!
component.upload.saveWarn=Please wait for the file to upload and save!
component.upload.startUpload=Start upload
component.upload.upload=Upload
component.upload.uploaded=Uploaded
component.upload.uploadError=Upload failed
component.upload.uploadImg=Please upload Image
component.upload.uploading=Uploading
component.upload.uploadSuccess=Upload successfully
component.upload.uploadWait=Please wait for the file upload to finish
component.upload.video=video
component.upload.videoNoPreview=Audio and video files cannot be previewed
component.upload.view=View
component.upload.viewImage=View Image
component.upload.waiting=Waiting
component.upload.zipNoPreview=Compressed package cannot be previewed
component.verify.dragText=Hold down the slider and drag
component.verify.error=verification failedï¼
component.verify.redoTip=Click the picture to refresh
component.verify.successText=Verified
component.verify.time=The verification is successful and it takes {time} secondsï¼
formGenerator.cleanComponentTip=Clear all components?
formGenerator.component.alert=Alert
formGenerator.component.areaSelect=AreaSelect
formGenerator.component.autoComplete=AutoComplete
formGenerator.component.barcode=Barcode
formGenerator.component.billRule=BillRule
formGenerator.component.button=Button
formGenerator.component.calculate=Calculate
formGenerator.component.card=Card
formGenerator.component.cascader=Cascader
formGenerator.component.checkbox=Checkbox
formGenerator.component.collapse=Collapse
formGenerator.component.colorPicker=ColorPicker
formGenerator.component.createTime=CreateTime
formGenerator.component.createUser=CreateUser
formGenerator.component.currOrganize=CurrentOrganize
formGenerator.component.currPosition=CurrentPosition
formGenerator.component.datePicker=DatePicker
formGenerator.component.depSelect=DepartmentSelect
formGenerator.component.divider=Divider
formGenerator.component.editor=Editor
formGenerator.component.groupSelect=GroupSelect
formGenerator.component.groupTitle=GroupTitle
formGenerator.component.iframe=Iframe
formGenerator.component.input=Input
formGenerator.component.inputNumber=InputNumber
formGenerator.component.link=Link
formGenerator.component.location=Location
formGenerator.component.modifyTime=ModifyTime
formGenerator.component.modifyUser=ModifyUser
formGenerator.component.organizeSelect=OrganizeSelect
formGenerator.component.popupAttr=PopupAttr
formGenerator.component.popupSelect=PopupSelect
formGenerator.component.popupTableSelect=PopupTableSelect
formGenerator.component.posSelect=PositionSelect
formGenerator.component.qrcode=Qrcode
formGenerator.component.radio=Radio
formGenerator.component.rate=Rate
formGenerator.component.relationForm=RelationForm
formGenerator.component.relationFormAttr=RelationFormAttr
formGenerator.component.roleSelect=RoleSelect
formGenerator.component.row=Row
formGenerator.component.select=Select
formGenerator.component.sign=Sign
formGenerator.component.slider=Slider
formGenerator.component.switch=Switch
formGenerator.component.tab=Tab
formGenerator.component.table=Table
formGenerator.component.tableGrid=TableGrid
formGenerator.component.text=Text
formGenerator.component.textarea=Textarea
formGenerator.component.timePicker=TimePicker
formGenerator.component.treeSelect=TreeSelect
formGenerator.component.uploadFile=UploadFile
formGenerator.component.uploadImg=UploadImage
formGenerator.component.userSelect=UserSelect
formGenerator.component.usersSelect=UsersSelect
formGenerator.copyComponentTip=Copy this component?
formGenerator.delComponentTip=Delete this component?
layout.footer.onlineDocument=Document
layout.footer.onlinePreview=Preview
layout.header.about=About
layout.header.commonMenus=Common Menus
layout.header.dropdownItemDoc=Document
layout.header.dropdownItemLoginOut=Login Out
layout.header.feedback=Feedback
layout.header.home=Home
layout.header.lockScreen=Lock screen
layout.header.lockScreenBtn=Locking
layout.header.lockScreenPassword=Lock screen password
layout.header.profile=Profile
layout.header.setting=Setting
layout.header.standingChange=Toggle Standing
layout.header.statement=Statement
layout.header.systemChange=Toggle App
layout.header.tooltipChat=Chat
layout.header.tooltipEntryFull=Full Screen
layout.header.tooltipErrorLog=Error log
layout.header.tooltipExitFull=Exit Full Screen
layout.header.tooltipLock=Lock screen
layout.header.tooltipNotify=Notification
layout.multipleTab.close=Close current
layout.multipleTab.closeAll=Close All
layout.multipleTab.closeLeft=Close Left
layout.multipleTab.closeOther=Close Other
layout.multipleTab.closeRight=Close Right
layout.multipleTab.reload=Refresh current
layout.multipleTab.setCommon=Set Common
layout.setting.animation=Animation
layout.setting.animationType=Animation type
layout.setting.autoScreenLock=Auto screen lock
layout.setting.blueBg=Blue
layout.setting.breadcrumb=Breadcrumbs
layout.setting.breadcrumbIcon=Breadcrumbs Icon
layout.setting.cachePage=Cache Page
layout.setting.clearBtn=Clear cache and to the login page
layout.setting.closeMixSidebarOnChange=Switch page to close menu
layout.setting.collapseMenuDisplayName=Collapse menu display name
layout.setting.colorWeak=Color Weak Mode
layout.setting.contentMode=Content area width
layout.setting.contentModeFixed=Fixed width
layout.setting.contentModeFull=Full
layout.setting.copyBtn=Copy
layout.setting.darkMode=Dark mode
layout.setting.defaultBg=Default
layout.setting.drawerTitle=Configuration
layout.setting.expandedMenuWidth=Expanded menu width
layout.setting.fixedHeader=Fixed header
layout.setting.fixedSideBar=Fixed Sidebar
layout.setting.footer=Footer
layout.setting.fullContent=Full content
layout.setting.grayMode=Gray mode
layout.setting.greenBg=Green
layout.setting.header=Header
layout.setting.headerTheme=Header theme
layout.setting.interfaceDisplay=Interface display
layout.setting.interfaceFunction=Interface function
layout.setting.menuAccordion=Sidebar accordion
layout.setting.menuCollapse=Collapse menu
layout.setting.menuCollapseButton=Menu collapse button
layout.setting.menuDrag=Drag Sidebar
layout.setting.menuSearch=Menu search
layout.setting.menuTriggerBottom=Bottom
layout.setting.menuTriggerNone=Not Show
layout.setting.menuTriggerTop=Top
layout.setting.menuTypeMix=Top Mix mode
layout.setting.menuTypeMixSidebar=Left mixed mode
layout.setting.menuTypeSidebar=Left menu mode
layout.setting.menuTypeTopMenu=Top menu mode
layout.setting.minute=Minute
layout.setting.mixSidebarFixed=Fixed expanded menu
layout.setting.mixSidebarTrigger=Mixed menu Trigger
layout.setting.navMode=Navigation mode
layout.setting.notAutoScreenLock=Not auto lock
layout.setting.off=Off
layout.setting.on=On
layout.setting.operatingContent=The copy is successful, please go to src/settings/projectSetting.ts to modify the configuration!
layout.setting.operatingTitle=Successful!
layout.setting.progress=Progress
layout.setting.purpleBg=Purple
layout.setting.resetSuccess=Successfully reset!
layout.setting.sidebar=Sidebar
layout.setting.sidebarTheme=Menu theme
layout.setting.splitMenu=Split menu
layout.setting.switchAnimation=Switch animation
layout.setting.switchLoading=Switch Loading
layout.setting.systemBackground=System background
layout.setting.sysTheme=System theme
layout.setting.tabDetail=Tab Detail
layout.setting.tabs=Tabs
layout.setting.tabsFoldBtn=Tabs flod button
layout.setting.tabsIcon=Tabs Icon
layout.setting.tabsQuickBtn=Tabs quick button
layout.setting.tabsRedoBtn=Tabs redo button
layout.setting.toggleLocale=Locale Toggle
layout.setting.topMenuAlignCenter=Right
layout.setting.topMenuAlignLeft=Left
layout.setting.topMenuAlignRight=Center
layout.setting.topMenuLayout=Top menu layout
layout.setting.triggerClick=Click
layout.setting.triggerHover=Hover
routes.basic.emailDetail=Email Detail
routes.basic.errorLogList=Error Log
routes.basic.externalLink=ExternalLink
routes.basic.home=Home
routes.basic.login=Login
routes.basic.previewModel=Model Preview
routes.basic.workFlowDetail=WorkFlow Detail
routes.commonWords=CommonWords
routes.dataReport=DataReport Demo(past)
routes.extend=Examples
routes.extend-barCode=BarCode
routes.extend-bigData=BigData
routes.extend-documentPreview=Document Demo
routes.extend-email=Email
routes.extend-formDemo=Form Demo
routes.extend-formDemo-fieldForm1=FieldForm1
routes.extend-formDemo-fieldForm2=FieldForm2
routes.extend-formDemo-fieldForm3=FieldForm3
routes.extend-formDemo-fieldForm4=FieldForm4
routes.extend-formDemo-fieldForm5=FieldForm5
routes.extend-formDemo-fieldForm6=FieldForm6
routes.extend-formDemo-verifyForm=VerifyForm
routes.extend-formDemo-verifyForm1=VerifyForm1
routes.extend-functionDemo=Function Demo
routes.extend-graphDemo=Graph Demo
routes.extend-graphDemo-echartsBar=E-Bar
routes.extend-graphDemo-echartsBarAcross=E-BarAcross
routes.extend-graphDemo-echartsCandlestick=E-Candlestick
routes.extend-graphDemo-echartsFunnel=E-Funnel
routes.extend-graphDemo-echartsGauge=E-Gauge
routes.extend-graphDemo-echartsLineArea=E-LineArea
routes.extend-graphDemo-echartsLineBar=E-LineBar
routes.extend-graphDemo-echartsPie=E-Pie
routes.extend-graphDemo-echartsScatter=E-Scatter
routes.extend-graphDemo-echartsTree=E-Tree
routes.extend-graphDemo-highchartsArea=H-Area
routes.extend-graphDemo-highchartsBellcurve=H-Bellcurve
routes.extend-graphDemo-highchartsBullet=H-Bullet
routes.extend-graphDemo-highchartsColumn=H-Column
routes.extend-graphDemo-highchartsFunnel=H-Funnel
routes.extend-graphDemo-highchartsGauge=H-Gauge
routes.extend-graphDemo-highchartsLine=H-Line
routes.extend-graphDemo-highchartsPie=H-Pie
routes.extend-graphDemo-highchartsScatter=H-Scatter
routes.extend-graphDemo-highchartsWordcloud=H-Wordcloud
routes.extend-importAndExport=ImportAndExport
routes.extend-map=Map
routes.extend-order=Order
routes.extend-orderDemo=Order Demo
routes.extend-portalDemo=Portal Demo
routes.extend-printData=PrintData
routes.extend-projectGantt=ProjectGantt
routes.extend-schedule=Schedule
routes.extend-signature=Signature
routes.extend-signet=Signet
routes.extend-tableDemo=Table Demo
routes.extend-tableDemo-commonTable=CommonTable
routes.extend-tableDemo-complexHeader=ComplexHeader
routes.extend-tableDemo-extension=Extension
routes.extend-tableDemo-groupingTable=GroupingTable
routes.extend-tableDemo-lockTable=LockTable
routes.extend-tableDemo-mergeTable=MergeTable
routes.extend-tableDemo-postilTable=PostilTable
routes.extend-tableDemo-printTable=PrintTable
routes.extend-tableDemo-redactTable=RedactTable
routes.extend-tableDemo-signTable=SignTable
routes.extend-tableDemo-statisticsTable=StatisticsTable
routes.extend-tableDemo-tableTree=TableTree
routes.extend-tableDemo-treeTable=TreeTable
routes.flowEngine=FlowEngine
routes.formDesign=SystemForm
routes.generator=Generator
routes.generator-appForm=AppForm
routes.generator-flowForm=FlowForm
routes.generator-webForm=WebForm
routes.lioui=WorkFlow Demo
routes.mainSystem=MainSystem
routes.moreMenu=More...
routes.msgCenter=MsgCenter
routes.msgCenter-accountConfig=AccountConfig
routes.msgCenter-accountConfig-ding=DingTalk
routes.msgCenter-accountConfig-mail=Mail
routes.msgCenter-accountConfig-mp=MP
routes.msgCenter-accountConfig-shortMsg=ShortMsg
routes.msgCenter-accountConfig-webhook=Webhook
routes.msgCenter-accountConfig-weCom=WeCom
routes.msgCenter-msgMonitor=MsgMonitor
routes.msgCenter-msgTemplate=MsgTemplate
routes.msgCenter-sendConfig=SendConfig
routes.onlineDev=OnlineDev
routes.onlineDev-appDesign=APPDesign
routes.onlineDev-dataReport=Report Design (Original)
routes.onlineDev-dataScreen=DataScreen
routes.onlineDev-integration=Integration
routes.onlineDev-printDev=PrintDesign
routes.onlineDev-report=DataReport
routes.onlineDev-visualPortal=VisualPortal
routes.onlineDev-webDesign=FormDesign
routes.permission=Permission
routes.permission-auth=Authorize
routes.permission-authorize=AuthGroup
routes.permission-department=Department
routes.permission-grade=Grade
routes.permission-group=Group
routes.permission-organize=Organize
routes.permission-position=Position
routes.permission-role=Role
routes.permission-user=User
routes.permission-userOnline=UserOnline
routes.printDemo=Print Demo
routes.report=DataReport Demo
routes.reportBI=ReportBI Demo
routes.system=System
routes.system-area=Area
routes.system-billRule=BillRule
routes.system-cache=CacheManage
routes.system-icons=Icons
routes.system-kit=Form Template
routes.system-language=Language
routes.system-log=Log
routes.system-menu=Menu
routes.system-messageTemplate=MessageTemplate
routes.system-monitor=Monitor
routes.system-notice=Notice
routes.system-signature=Signature
routes.system-smsTemplate=SmsTemplate
routes.system-sysConfig=SysConfig
routes.system-systemTemplate=SystemTemplate
routes.system-task=Task
routes.systemData=SystemData
routes.systemData-dataBackup=DataBackup
routes.systemData-dataInterface=DataInterface
routes.systemData-dataModel=DataModel
routes.systemData-dataSource=DataSource
routes.systemData-dataSync=DataSync
routes.systemData-dictionary=Dictionary
routes.systemData-interfaceAuth=InterfaceAuth
routes.systemData-map=Map
routes.weChat=WeChat
routes.weChat-mpConfig=MPConfig
routes.weChat-mpMaterial=MPMaterial
routes.weChat-mpMenu=MPMenu
routes.weChat-mpMessage=MPMessage
routes.weChat-mpUser=MPUser
routes.weChat-qyDepartment=QYDepartment
routes.weChat-qyhConfig=QYHConfig
routes.weChat-qyMessage=QYMessage
routes.weChat-qyUser=QYUser
routes.workFlow=WorkFlow
routes.workFlow-addFlow=AddFlow
routes.workFlow-document=Document
routes.workFlow-entrust=Entrust
routes.workFlow-flowCirculate=FlowCirculate
routes.workFlow-flowDoing=FlowDoing
routes.workFlow-flowDone=FlowDone
routes.workFlow-flowEngine=FlowDesign
routes.workFlow-flowLaunch=FlowLaunch
routes.workFlow-flowMonitor=FlowMonitor
routes.workFlow-flowTodo=FlowTodo
routes.workFlow-flowToSign=FlowToSign
routes.workFlow-form=Form
routes.workFlow-printTemplate=PrintTemplate
routes.workFlow-schedule=Schedule
routes.workSystem=WorkSystem
sys.api.apiRequestFailed=The interface request failed, please try again later!
sys.api.apiTimeoutMessage=The interface request timed out, please refresh the page and try again!
sys.api.errMsg401=The user does not have permission (token, user name, password error)!
sys.api.errMsg403=The user is authorized, but access is forbidden!
sys.api.errMsg404=Network request error, the resource was not found!
sys.api.errMsg405=Network request error, request method not allowed!
sys.api.errMsg408=Network request timed out!
sys.api.errMsg500=Server error, please contact the administrator!
sys.api.errMsg501=The network is not implemented!
sys.api.errMsg502=Network Error!
sys.api.errMsg503=The service is unavailable, the server is temporarily overloaded or maintained!
sys.api.errMsg504=Network timeout!
sys.api.errMsg505=The http version does not support the request!
sys.api.errorMessage=The operation failed, the system is abnormal!
sys.api.errorTip=Error Tip
sys.api.networkException=network anomaly
sys.api.networkExceptionMsg=Please check if your network connection is normal! The network is abnormal
sys.api.operationFailed=Operation failed
sys.api.timeoutMessage=Login timed out, please log in again!
sys.app.logoutMessage=Confirm to exit the system?
sys.app.logoutTip=Reminder
sys.app.menuLoading=Menu loading...
sys.errorLog.enableMessage=Only effective when useErrorHandle=true in `/src/settings/projectSetting.ts`.
sys.errorLog.fireAjaxError=Fire ajax error
sys.errorLog.fireResourceError=Fire resource error
sys.errorLog.fireVueError=Fire vue error
sys.errorLog.modalTitle=Error details
sys.errorLog.tableActionDesc=Details
sys.errorLog.tableColumnDate=Time
sys.errorLog.tableColumnFile=File
sys.errorLog.tableColumnMsg=Error message
sys.errorLog.tableColumnStackMsg=Stack info
sys.errorLog.tableColumnType=Type
sys.errorLog.tableTitle=Error log list
sys.exception.backHome=Back Home
sys.exception.backLogin=Back Login
sys.exception.networkErrorSubTitle=Sorryï¼Your network connection has been disconnected, please check your network!
sys.exception.networkErrorTitle=Network Error
sys.exception.noDataTitle=No data on the current page.
sys.exception.subTitle403=Sorry, you don't have access to this page.
sys.exception.subTitle404=Sorry, the page you visited does not exist.
sys.exception.subTitle500=Sorry, the server is reporting an error.
sys.lock.alert=Lock screen password error
sys.lock.backToLogin=Back to login
sys.lock.entry=Enter the system
sys.lock.placeholder=Please enter the user password
sys.lock.unlock=Click to unlock
sys.login.accountPlaceholder=Please input username
sys.login.accountTip=Please enter the account number
sys.login.backSignIn=Back sign in
sys.login.changeCode=Click to switch verification code
sys.login.codeTip=Please enter your verification code
sys.login.codeTitle=Verify Code Login
sys.login.company=Please enter company name
sys.login.confirmLogin=Confirm login on phone
sys.login.confirmPassword=Confirm Password
sys.login.contacts=Please enter contact
sys.login.diffPwd=The two passwords are inconsistent
sys.login.email=Email
sys.login.expired=Qrcode has expired
sys.login.forgetFormTitle=Reset password
sys.login.forgetPassword=Forget Password?
sys.login.getCode=Get code
sys.login.lastLoginInfo=Last login information
sys.login.logIn=Login
sys.login.loginButton=Login
sys.login.mobile=Please enter mobile number
sys.login.mobilePlaceholder=Please input mobile
sys.login.mobileSignInFormTitle=Verify Code Login
sys.login.otherLogin=Other login
sys.login.otherSignIn=Sign in with
sys.login.password=Password
sys.login.passwordPlaceholder=Please input password
sys.login.passwordTip=Please enter your password
sys.login.policy=I agree to the xxx Privacy Policy
sys.login.policyPlaceholder=Register after checking
sys.login.qrCodeTip=Please use the app to scan the code to login. The code will expire after 180 seconds.
sys.login.qrSignInFormTitle=APP Scan Login
sys.login.recoverCode=Cancel
sys.login.refreshCode=Refresh
sys.login.registerButton=Sign up
sys.login.rememberMe=Remember me
sys.login.reSend=Resend
sys.login.rightMobile=Please enter the correct mobile number
sys.login.rule=Sub Account: mobile{'@'}account example:***********{'@'}101001
sys.login.scanSign=scanning the code to complete the login
sys.login.scanSuccessful=Scanned
sys.login.scanTip=APP Scan code login
sys.login.scanTitle=APP Scan Login
sys.login.signInDesc=Enter your personal details and get started!
sys.login.signInFormTitle=Account Login
sys.login.signInTitle=Backstage management system
sys.login.signUpFormTitle=Sign up
sys.login.smsCode=Please enter the verification code
sys.login.smsPlaceholder=Please input sms code
sys.login.subTitle=Login with account password
sys.login.subTitle1=Login with mobile verify code, or switch to
sys.login.subTitle2=Login with account, or switch to
sys.login.subTitle3=Login with scan code, or switch to 
sys.login.title=Account Login
sys.login.upper=Caps locked
sys.login.username=Username
sys.login.version=V
sys.login.welcome=Welcome
sys.validate.arrayRequiredPrefix=Please select at least one
sys.validate.date=Please enter the correct date
sys.validate.email=Please enter the correct email address
sys.validate.idCard=Please enter the correct ID number
sys.validate.mobilePhone=Please enter the correct mobile phone number
sys.validate.money=Please enter the correct amount
sys.validate.number=Please enter the correct number
sys.validate.phone=Please enter the correct phone number
sys.validate.telephone=Please enter the correct telephone number
sys.validate.textRequiredSuffix=cannot be empty
sys.validate.url=Please enter the correct website address
views.dynamicModel.hideSome=Hide some
views.dynamicModel.passwordPlaceholder=Please enter your password
views.dynamicModel.scanAndShare=Scan & Share
views.dynamicModel.showMore=Show more
views.http404.goBackBtn=Go homepage
views.http404.subTips=Please check if the URL you entered is correct, or click the button to return to the homepage.
views.http404.tips=Sorry, the page you visited does not exist or you do not have permission to access it!
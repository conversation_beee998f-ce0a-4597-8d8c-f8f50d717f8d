# 测试环境配置
spring:
  profiles:
    active: test
  
  # 数据源配置（使用内存数据库）
  datasource:
    driver-class-name: org.h2.Driver
    url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    username: sa
    password: 
  
  # H2数据库控制台配置
  h2:
    console:
      enabled: true
      path: /h2-console
  
  # JPA配置
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.H2Dialect
        format_sql: true

# 日志配置
logging:
  level:
    jnpf: DEBUG
    org.springframework.web: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE

# 测试相关配置
test:
  # 是否启用测试模式
  enabled: true
  # 测试数据初始化
  data:
    init: true 
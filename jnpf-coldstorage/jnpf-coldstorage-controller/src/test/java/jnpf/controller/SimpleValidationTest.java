package jnpf.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import jnpf.model.ColdStorageProjectForm;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import javax.validation.ValidatorFactory;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 简化的参数验证测试
 * 专注于验证注解的功能测试
 *
 * @版本： V5.2.0
 * @版权： 杭州多协信息技术有限公司（https://www.duoxieyun.com）
 * @作者： 多协开发组
 * @日期： 2025-01-27
 */
@ExtendWith(MockitoExtension.class)
class SimpleValidationTest {

    private final ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
    private final Validator validator = factory.getValidator();
    private final ObjectMapper objectMapper = new ObjectMapper();

    @Test
    void testValidProjectForm_ShouldPass() {
        ColdStorageProjectForm form = new ColdStorageProjectForm();
        form.setProjectCode("PROJ001");
        form.setProjectName("测试项目");
        form.setCompanyName("测试公司");
        form.setProjectManager("张三");
        form.setManagerPhone("13800138000");
        form.setProjectStatus(1);
        form.setProjectRemark("测试备注");

        Set<ConstraintViolation<ColdStorageProjectForm>> violations = validator.validate(form);
        assertTrue(violations.isEmpty(), "有效数据应该通过验证");
    }

    @Test
    void testEmptyProjectCode_ShouldFail() {
        ColdStorageProjectForm form = new ColdStorageProjectForm();
        form.setProjectCode(""); // 空项目编号
        form.setProjectName("测试项目");
        form.setCompanyName("测试公司");
        form.setProjectManager("张三");
        form.setManagerPhone("13800138000");
        form.setProjectStatus(1);

        Set<ConstraintViolation<ColdStorageProjectForm>> violations = validator.validate(form);
        assertFalse(violations.isEmpty(), "空项目编号应该验证失败");
        
        boolean hasProjectCodeError = violations.stream()
                .anyMatch(v -> v.getPropertyPath().toString().equals("projectCode") && 
                        v.getMessage().contains("项目编号不能为空"));
        assertTrue(hasProjectCodeError, "应该包含项目编号不能为空的错误");
    }

    @Test
    void testInvalidProjectCode_ShouldFail() {
        ColdStorageProjectForm form = new ColdStorageProjectForm();
        form.setProjectCode("PROJ@001"); // 包含特殊字符
        form.setProjectName("测试项目");
        form.setCompanyName("测试公司");
        form.setProjectManager("张三");
        form.setManagerPhone("13800138000");
        form.setProjectStatus(1);

        Set<ConstraintViolation<ColdStorageProjectForm>> violations = validator.validate(form);
        assertFalse(violations.isEmpty(), "无效项目编号应该验证失败");
        
        boolean hasProjectCodeError = violations.stream()
                .anyMatch(v -> v.getPropertyPath().toString().equals("projectCode") && 
                        v.getMessage().contains("项目编号只能包含字母、数字、下划线和横线"));
        assertTrue(hasProjectCodeError, "应该包含项目编号格式错误");
    }

    @Test
    void testProjectCodeTooShort_ShouldFail() {
        ColdStorageProjectForm form = new ColdStorageProjectForm();
        form.setProjectCode("A"); // 太短
        form.setProjectName("测试项目");
        form.setCompanyName("测试公司");
        form.setProjectManager("张三");
        form.setManagerPhone("13800138000");
        form.setProjectStatus(1);

        Set<ConstraintViolation<ColdStorageProjectForm>> violations = validator.validate(form);
        assertFalse(violations.isEmpty(), "项目编号太短应该验证失败");
        
        boolean hasProjectCodeError = violations.stream()
                .anyMatch(v -> v.getPropertyPath().toString().equals("projectCode") && 
                        v.getMessage().contains("项目编号长度必须在2-50个字符之间"));
        assertTrue(hasProjectCodeError, "应该包含项目编号长度错误");
    }

    @Test
    void testEmptyProjectName_ShouldFail() {
        ColdStorageProjectForm form = new ColdStorageProjectForm();
        form.setProjectCode("PROJ001");
        form.setProjectName(""); // 空项目名称
        form.setCompanyName("测试公司");
        form.setProjectManager("张三");
        form.setManagerPhone("13800138000");
        form.setProjectStatus(1);

        Set<ConstraintViolation<ColdStorageProjectForm>> violations = validator.validate(form);
        assertFalse(violations.isEmpty(), "空项目名称应该验证失败");
        
        boolean hasProjectNameError = violations.stream()
                .anyMatch(v -> v.getPropertyPath().toString().equals("projectName") && 
                        v.getMessage().contains("项目名称不能为空"));
        assertTrue(hasProjectNameError, "应该包含项目名称不能为空的错误");
    }

    @Test
    void testEmptyCompanyName_ShouldFail() {
        ColdStorageProjectForm form = new ColdStorageProjectForm();
        form.setProjectCode("PROJ001");
        form.setProjectName("测试项目");
        form.setCompanyName(""); // 空公司名称
        form.setProjectManager("张三");
        form.setManagerPhone("13800138000");
        form.setProjectStatus(1);

        Set<ConstraintViolation<ColdStorageProjectForm>> violations = validator.validate(form);
        assertFalse(violations.isEmpty(), "空公司名称应该验证失败");
        
        boolean hasCompanyNameError = violations.stream()
                .anyMatch(v -> v.getPropertyPath().toString().equals("companyName") && 
                        v.getMessage().contains("公司名称不能为空"));
        assertTrue(hasCompanyNameError, "应该包含公司名称不能为空的错误");
    }

    @Test
    void testEmptyProjectManager_ShouldFail() {
        ColdStorageProjectForm form = new ColdStorageProjectForm();
        form.setProjectCode("PROJ001");
        form.setProjectName("测试项目");
        form.setCompanyName("测试公司");
        form.setProjectManager(""); // 空项目负责人
        form.setManagerPhone("13800138000");
        form.setProjectStatus(1);

        Set<ConstraintViolation<ColdStorageProjectForm>> violations = validator.validate(form);
        assertFalse(violations.isEmpty(), "空项目负责人应该验证失败");
        
        boolean hasProjectManagerError = violations.stream()
                .anyMatch(v -> v.getPropertyPath().toString().equals("projectManager") && 
                        v.getMessage().contains("项目负责人不能为空"));
        assertTrue(hasProjectManagerError, "应该包含项目负责人不能为空的错误");
    }

    @Test
    void testEmptyManagerPhone_ShouldFail() {
        ColdStorageProjectForm form = new ColdStorageProjectForm();
        form.setProjectCode("PROJ001");
        form.setProjectName("测试项目");
        form.setCompanyName("测试公司");
        form.setProjectManager("张三");
        form.setManagerPhone(""); // 空联系电话
        form.setProjectStatus(1);

        Set<ConstraintViolation<ColdStorageProjectForm>> violations = validator.validate(form);
        assertFalse(violations.isEmpty(), "空联系电话应该验证失败");
        
        boolean hasManagerPhoneError = violations.stream()
                .anyMatch(v -> v.getPropertyPath().toString().equals("managerPhone") && 
                        v.getMessage().contains("负责人联系电话不能为空"));
        assertTrue(hasManagerPhoneError, "应该包含联系电话不能为空的错误");
    }

    @Test
    void testInvalidPhoneFormat_ShouldFail() {
        ColdStorageProjectForm form = new ColdStorageProjectForm();
        form.setProjectCode("PROJ001");
        form.setProjectName("测试项目");
        form.setCompanyName("测试公司");
        form.setProjectManager("张三");
        form.setManagerPhone("1234567890"); // 无效手机号格式
        form.setProjectStatus(1);

        Set<ConstraintViolation<ColdStorageProjectForm>> violations = validator.validate(form);
        assertFalse(violations.isEmpty(), "无效手机号格式应该验证失败");
        
        boolean hasManagerPhoneError = violations.stream()
                .anyMatch(v -> v.getPropertyPath().toString().equals("managerPhone") && 
                        v.getMessage().contains("请输入正确的手机号码格式"));
        assertTrue(hasManagerPhoneError, "应该包含手机号格式错误");
    }

    @Test
    void testNullProjectStatus_ShouldFail() {
        ColdStorageProjectForm form = new ColdStorageProjectForm();
        form.setProjectCode("PROJ001");
        form.setProjectName("测试项目");
        form.setCompanyName("测试公司");
        form.setProjectManager("张三");
        form.setManagerPhone("13800138000");
        form.setProjectStatus(null); // 空项目状态

        Set<ConstraintViolation<ColdStorageProjectForm>> violations = validator.validate(form);
        assertFalse(violations.isEmpty(), "空项目状态应该验证失败");
        
        boolean hasProjectStatusError = violations.stream()
                .anyMatch(v -> v.getPropertyPath().toString().equals("projectStatus") && 
                        v.getMessage().contains("项目状态不能为空"));
        assertTrue(hasProjectStatusError, "应该包含项目状态不能为空的错误");
    }

    @Test
    void testInvalidProjectStatus_ShouldFail() {
        ColdStorageProjectForm form = new ColdStorageProjectForm();
        form.setProjectCode("PROJ001");
        form.setProjectName("测试项目");
        form.setCompanyName("测试公司");
        form.setProjectManager("张三");
        form.setManagerPhone("13800138000");
        form.setProjectStatus(5); // 无效状态值

        Set<ConstraintViolation<ColdStorageProjectForm>> violations = validator.validate(form);
        assertFalse(violations.isEmpty(), "无效项目状态应该验证失败");
        
        boolean hasProjectStatusError = violations.stream()
                .anyMatch(v -> v.getPropertyPath().toString().equals("projectStatus") && 
                        v.getMessage().contains("项目状态值不能大于4"));
        assertTrue(hasProjectStatusError, "应该包含项目状态值错误");
    }

    @Test
    void testProjectStatusTooSmall_ShouldFail() {
        ColdStorageProjectForm form = new ColdStorageProjectForm();
        form.setProjectCode("PROJ001");
        form.setProjectName("测试项目");
        form.setCompanyName("测试公司");
        form.setProjectManager("张三");
        form.setManagerPhone("13800138000");
        form.setProjectStatus(-1); // 负数状态值

        Set<ConstraintViolation<ColdStorageProjectForm>> violations = validator.validate(form);
        assertFalse(violations.isEmpty(), "负数项目状态应该验证失败");
        
        boolean hasProjectStatusError = violations.stream()
                .anyMatch(v -> v.getPropertyPath().toString().equals("projectStatus") && 
                        v.getMessage().contains("项目状态值不能小于0"));
        assertTrue(hasProjectStatusError, "应该包含项目状态值错误");
    }

    @Test
    void testProjectRemarkTooLong_ShouldFail() {
        ColdStorageProjectForm form = new ColdStorageProjectForm();
        form.setProjectCode("PROJ001");
        form.setProjectName("测试项目");
        form.setCompanyName("测试公司");
        form.setProjectManager("张三");
        form.setManagerPhone("13800138000");
        form.setProjectStatus(1);
        
        // 生成超过500字符的备注
        StringBuilder longRemark = new StringBuilder();
        for (int i = 0; i < 501; i++) {
            longRemark.append("测");
        }
        form.setProjectRemark(longRemark.toString());

        Set<ConstraintViolation<ColdStorageProjectForm>> violations = validator.validate(form);
        assertFalse(violations.isEmpty(), "备注过长应该验证失败");
        
        boolean hasProjectRemarkError = violations.stream()
                .anyMatch(v -> v.getPropertyPath().toString().equals("projectRemark") && 
                        v.getMessage().contains("项目备注长度不能超过500个字符"));
        assertTrue(hasProjectRemarkError, "应该包含备注长度错误");
    }

    @Test
    void testMultipleValidationErrors_ShouldReturnAllErrors() {
        ColdStorageProjectForm form = new ColdStorageProjectForm();
        // 故意设置多个错误
        form.setProjectCode(""); // 空项目编号
        form.setProjectName(""); // 空项目名称
        form.setManagerPhone("invalid"); // 无效手机号
        form.setProjectStatus(10); // 无效状态值

        Set<ConstraintViolation<ColdStorageProjectForm>> violations = validator.validate(form);
        assertFalse(violations.isEmpty(), "应该包含多个验证错误");
        assertTrue(violations.size() >= 4, "应该至少包含4个验证错误");
        
        // 验证包含所有预期的错误
        long projectCodeErrors = violations.stream()
                .filter(v -> v.getPropertyPath().toString().equals("projectCode"))
                .count();
        long projectNameErrors = violations.stream()
                .filter(v -> v.getPropertyPath().toString().equals("projectName"))
                .count();
        long managerPhoneErrors = violations.stream()
                .filter(v -> v.getPropertyPath().toString().equals("managerPhone"))
                .count();
        long projectStatusErrors = violations.stream()
                .filter(v -> v.getPropertyPath().toString().equals("projectStatus"))
                .count();
        
        assertTrue(projectCodeErrors > 0, "应该包含项目编号错误");
        assertTrue(projectNameErrors > 0, "应该包含项目名称错误");
        assertTrue(managerPhoneErrors > 0, "应该包含手机号错误");
        assertTrue(projectStatusErrors > 0, "应该包含项目状态错误");
    }
} 
package jnpf.permission.constant;

import jnpf.model.ExcelColumnAttr;
import org.apache.poi.ss.usermodel.IndexedColors;

import java.util.*;

public class RoleColumnMap {

    String excelName = "角色信息";

    Map<String, String> keyMap = new LinkedHashMap() {{
        put("fullName", "角色名称");
        put("enCode", "角色编码");
        put("globalMark", "角色类型");
        put("organizeId", "所属组织");
        put("enabledMark", "状态");
        put("sortCode", "排序");
        put("description", "说明");
    }};

    /**
     * 表格名称
     *
     * @return
     */
    public String getExcelName() {
        return excelName;
    }

    /**
     * 根据类型获取excel表头字段
     *
     * @param type
     * @return
     */
    public Map<String, String> getColumnByType(Integer type) {
        return keyMap;
    }

    /**
     * 获取字段列表
     *
     * @param isError
     * @return
     */
    public List<ExcelColumnAttr> getFieldsModel(boolean isError) {
        List<ExcelColumnAttr> models = new ArrayList<>();
        //异常原因
        if (isError) {
            ExcelColumnAttr attr = ExcelColumnAttr.builder().key("errorsInfo").name("异常原因").build();
            models.add(attr);
        }
        List<String> requireFields = Arrays.asList("fullName", "enCode", "globalMark", "enabledMark");
        for (String key : keyMap.keySet()) {
            ExcelColumnAttr attr = ExcelColumnAttr.builder().key(key).name(keyMap.get(key)).build();
            if (requireFields.contains(key)) {
                attr.setRequire(true);
                attr.setFontColor(IndexedColors.RED.getIndex());
            }
            models.add(attr);
        }
        return models;
    }

    /**
     * 获取默认值
     */
    public List<Map<String, Object>> getDefaultList() {
        List<Map<String, Object>> list = new ArrayList<>();
        Map<String, Object> map = new HashMap<>();
        map.put("organizeId", "公司名称/公司名称1/部门名称");
        list.add(map);
        return list;
    }

}

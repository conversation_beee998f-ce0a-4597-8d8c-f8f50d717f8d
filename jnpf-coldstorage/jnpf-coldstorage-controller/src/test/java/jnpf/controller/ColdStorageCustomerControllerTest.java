package jnpf.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import jnpf.base.ActionResult;
import jnpf.base.vo.PageListVO;
import jnpf.base.vo.PaginationVO;
import jnpf.model.ColdStorageCustomerForm;
import jnpf.model.ColdStorageCustomerQueryForm;
import jnpf.model.ColdStorageCustomerVO;
import jnpf.service.ColdStorageCustomerService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 冷库储能客户管理Controller测试类
 *
 * @版本： V5.2.0
 * @版权： 杭州多协信息技术有限公司（https://www.duoxieyun.com）
 * @作者： 多协开发组
 * @日期： 2025-01-27
 */
@ExtendWith(MockitoExtension.class)
class ColdStorageCustomerControllerTest {

    @Mock
    private ColdStorageCustomerService coldStorageCustomerService;

    @InjectMocks
    private ColdStorageCustomerController coldStorageCustomerController;

    private MockMvc mockMvc;
    private ObjectMapper objectMapper;

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.standaloneSetup(coldStorageCustomerController).build();
        objectMapper = new ObjectMapper();
    }

    /**
     * 测试获取客户列表（条件查询）
     */
    @Test
    void testGetList() throws Exception {
        // 准备测试数据
        List<ColdStorageCustomerVO> customerList = new ArrayList<>();
        ColdStorageCustomerVO customer = new ColdStorageCustomerVO();
        customer.setId("test-id-1");
        customer.setCustomerCode("CS001");
        customer.setCustomerName("测试客户");
        customer.setMobilePhone("13800138000");
        customer.setCustomerStatus(1);
        customerList.add(customer);

        ColdStorageCustomerQueryForm queryForm = new ColdStorageCustomerQueryForm();
        queryForm.setCurrentPage(1);
        queryForm.setPageSize(10);
        queryForm.setCustomerName("测试");

        // Mock服务层方法
        when(coldStorageCustomerService.getList(any(ColdStorageCustomerQueryForm.class))).thenReturn(customerList);
        when(coldStorageCustomerService.getCount(any(ColdStorageCustomerQueryForm.class))).thenReturn(1L);

        // 执行测试
        mockMvc.perform(post("/api/coldstorage/customer/getList")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(queryForm)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.list").isArray())
                .andExpect(jsonPath("$.data.list[0].id").value("test-id-1"))
                .andExpect(jsonPath("$.data.list[0].customerCode").value("CS001"))
                .andExpect(jsonPath("$.data.list[0].customerName").value("测试客户"))
                .andExpect(jsonPath("$.data.pagination.total").value(1));

        // 验证服务层方法被调用
        verify(coldStorageCustomerService).getList(any(ColdStorageCustomerQueryForm.class));
        verify(coldStorageCustomerService).getCount(any(ColdStorageCustomerQueryForm.class));
    }

    /**
     * 测试获取客户列表（无条件）
     */
    @Test
    void testListAll() throws Exception {
        // 准备测试数据
        List<ColdStorageCustomerVO> customerList = new ArrayList<>();
        ColdStorageCustomerVO customer = new ColdStorageCustomerVO();
        customer.setId("test-id-1");
        customer.setCustomerCode("CS001");
        customer.setCustomerName("测试客户");
        customerList.add(customer);

        // Mock服务层方法
        when(coldStorageCustomerService.getList()).thenReturn(customerList);

        // 执行测试
        mockMvc.perform(get("/api/coldstorage/customer"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.list").isArray())
                .andExpect(jsonPath("$.data.list[0].id").value("test-id-1"));

        // 验证服务层方法被调用
        verify(coldStorageCustomerService).getList();
    }

    /**
     * 测试获取客户信息
     */
    @Test
    void testGetInfo() throws Exception {
        // 准备测试数据
        String customerId = "test-id-1";
        ColdStorageCustomerVO customer = new ColdStorageCustomerVO();
        customer.setId(customerId);
        customer.setCustomerCode("CS001");
        customer.setCustomerName("测试客户");

        // Mock服务层方法
        when(coldStorageCustomerService.getInfo(customerId)).thenReturn(customer);

        // 执行测试
        mockMvc.perform(get("/api/coldstorage/customer/{id}", customerId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.id").value(customerId))
                .andExpect(jsonPath("$.data.customerCode").value("CS001"));

        // 验证服务层方法被调用
        verify(coldStorageCustomerService).getInfo(customerId);
    }

    /**
     * 测试新建客户 - 成功
     */
    @Test
    void testCreateSuccess() throws Exception {
        // 准备测试数据
        ColdStorageCustomerForm form = new ColdStorageCustomerForm();
        form.setCustomerCode("CS001");
        form.setCustomerName("测试客户");
        form.setMobilePhone("13800138000");
        form.setContactPerson("张三");
        form.setContactPhone("13800138001");
        form.setCustomerStatus(1);

        // Mock服务层方法
        when(coldStorageCustomerService.existsByMobilePhone(anyString(), isNull())).thenReturn(false);
        when(coldStorageCustomerService.existsByCustomerCode(anyString(), isNull())).thenReturn(false);
        doNothing().when(coldStorageCustomerService).create(any(ColdStorageCustomerForm.class));

        // 执行测试
        mockMvc.perform(post("/api/coldstorage/customer")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(form)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.msg").value("新建成功"));

        // 验证服务层方法被调用
        verify(coldStorageCustomerService).existsByMobilePhone("13800138000", null);
        verify(coldStorageCustomerService).existsByCustomerCode("CS001", null);
        verify(coldStorageCustomerService).create(any(ColdStorageCustomerForm.class));
    }

    /**
     * 测试新建客户 - 手机号已存在
     */
    @Test
    void testCreateMobilePhoneExists() throws Exception {
        // 准备测试数据
        ColdStorageCustomerForm form = new ColdStorageCustomerForm();
        form.setCustomerCode("CS001");
        form.setCustomerName("测试客户");
        form.setMobilePhone("13800138000");
        form.setContactPerson("张三");
        form.setContactPhone("13800138001");
        form.setCustomerStatus(1);

        // Mock服务层方法
        when(coldStorageCustomerService.existsByMobilePhone(anyString(), isNull())).thenReturn(true);

        // 执行测试
        mockMvc.perform(post("/api/coldstorage/customer")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(form)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(400))
                .andExpect(jsonPath("$.msg").value("手机号已存在"));

        // 验证服务层方法被调用
        verify(coldStorageCustomerService).existsByMobilePhone("13800138000", null);
        verify(coldStorageCustomerService, never()).create(any(ColdStorageCustomerForm.class));
    }

    /**
     * 测试新建客户 - 客户编号已存在
     */
    @Test
    void testCreateCustomerCodeExists() throws Exception {
        // 准备测试数据
        ColdStorageCustomerForm form = new ColdStorageCustomerForm();
        form.setCustomerCode("CS001");
        form.setCustomerName("测试客户");
        form.setMobilePhone("13800138000");
        form.setContactPerson("张三");
        form.setContactPhone("13800138001");
        form.setCustomerStatus(1);

        // Mock服务层方法
        when(coldStorageCustomerService.existsByMobilePhone(anyString(), isNull())).thenReturn(false);
        when(coldStorageCustomerService.existsByCustomerCode(anyString(), isNull())).thenReturn(true);

        // 执行测试
        mockMvc.perform(post("/api/coldstorage/customer")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(form)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(400))
                .andExpect(jsonPath("$.msg").value("客户编号已存在"));

        // 验证服务层方法被调用
        verify(coldStorageCustomerService).existsByMobilePhone("13800138000", null);
        verify(coldStorageCustomerService).existsByCustomerCode("CS001", null);
        verify(coldStorageCustomerService, never()).create(any(ColdStorageCustomerForm.class));
    }

    /**
     * 测试修改客户 - 成功
     */
    @Test
    void testUpdateSuccess() throws Exception {
        // 准备测试数据
        String customerId = "test-id-1";
        ColdStorageCustomerForm form = new ColdStorageCustomerForm();
        form.setCustomerCode("CS001");
        form.setCustomerName("测试客户修改");
        form.setMobilePhone("13800138000");
        form.setContactPerson("张三");
        form.setContactPhone("13800138001");
        form.setCustomerStatus(1);

        // Mock服务层方法
        when(coldStorageCustomerService.existsByMobilePhone(anyString(), eq(customerId))).thenReturn(false);
        when(coldStorageCustomerService.existsByCustomerCode(anyString(), eq(customerId))).thenReturn(false);
        doNothing().when(coldStorageCustomerService).update(eq(customerId), any(ColdStorageCustomerForm.class));

        // 执行测试
        mockMvc.perform(put("/api/coldstorage/customer/{id}", customerId)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(form)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.msg").value("修改成功"));

        // 验证服务层方法被调用
        verify(coldStorageCustomerService).existsByMobilePhone("13800138000", customerId);
        verify(coldStorageCustomerService).existsByCustomerCode("CS001", customerId);
        verify(coldStorageCustomerService).update(customerId, form);
    }

    /**
     * 测试删除客户
     */
    @Test
    void testDelete() throws Exception {
        // 准备测试数据
        String customerId = "test-id-1";

        // Mock服务层方法
        doNothing().when(coldStorageCustomerService).delete(customerId);

        // 执行测试
        mockMvc.perform(delete("/api/coldstorage/customer/{id}", customerId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.msg").value("删除成功"));

        // 验证服务层方法被调用
        verify(coldStorageCustomerService).delete(customerId);
    }

    /**
     * 测试重置客户密码 - 成功
     */
    @Test
    void testResetPasswordSuccess() throws Exception {
        // 准备测试数据
        String customerId = "test-id-1";
        String newPassword = "123456";

        // Mock服务层方法
        doNothing().when(coldStorageCustomerService).resetPassword(customerId, newPassword);

        // 执行测试
        mockMvc.perform(post("/api/coldstorage/customer/{id}/reset-password", customerId)
                .param("newPassword", newPassword))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.msg").value("密码重置成功"));

        // 验证服务层方法被调用
        verify(coldStorageCustomerService).resetPassword(customerId, newPassword);
    }

    /**
     * 测试重置客户密码 - 密码为空
     */
    @Test
    void testResetPasswordEmpty() throws Exception {
        // 准备测试数据
        String customerId = "test-id-1";
        String newPassword = "";

        // 执行测试
        mockMvc.perform(post("/api/coldstorage/customer/{id}/reset-password", customerId)
                .param("newPassword", newPassword))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(400))
                .andExpect(jsonPath("$.msg").value("新密码不能为空"));

        // 验证服务层方法没有被调用
        verify(coldStorageCustomerService, never()).resetPassword(anyString(), anyString());
    }

    /**
     * 测试重置客户密码 - 密码长度不足
     */
    @Test
    void testResetPasswordTooShort() throws Exception {
        // 准备测试数据
        String customerId = "test-id-1";
        String newPassword = "123";

        // 执行测试
        mockMvc.perform(post("/api/coldstorage/customer/{id}/reset-password", customerId)
                .param("newPassword", newPassword))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(400))
                .andExpect(jsonPath("$.msg").value("密码长度不能少于6位"));

        // 验证服务层方法没有被调用
        verify(coldStorageCustomerService, never()).resetPassword(anyString(), anyString());
    }

    /**
     * 测试启用/禁用客户 - 启用
     */
    @Test
    void testUpdateStatusEnable() throws Exception {
        // 准备测试数据
        String customerId = "test-id-1";
        Integer status = 1;

        // Mock服务层方法
        doNothing().when(coldStorageCustomerService).updateStatus(customerId, status);

        // 执行测试
        mockMvc.perform(post("/api/coldstorage/customer/{id}/status", customerId)
                .param("status", status.toString()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.msg").value("启用成功"));

        // 验证服务层方法被调用
        verify(coldStorageCustomerService).updateStatus(customerId, status);
    }

    /**
     * 测试启用/禁用客户 - 禁用
     */
    @Test
    void testUpdateStatusDisable() throws Exception {
        // 准备测试数据
        String customerId = "test-id-1";
        Integer status = 0;

        // Mock服务层方法
        doNothing().when(coldStorageCustomerService).updateStatus(customerId, status);

        // 执行测试
        mockMvc.perform(post("/api/coldstorage/customer/{id}/status", customerId)
                .param("status", status.toString()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.msg").value("禁用成功"));

        // 验证服务层方法被调用
        verify(coldStorageCustomerService).updateStatus(customerId, status);
    }

    /**
     * 测试启用/禁用客户 - 状态参数错误
     */
    @Test
    void testUpdateStatusInvalidStatus() throws Exception {
        // 准备测试数据
        String customerId = "test-id-1";
        Integer status = 2;

        // 执行测试
        mockMvc.perform(post("/api/coldstorage/customer/{id}/status", customerId)
                .param("status", status.toString()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(400))
                .andExpect(jsonPath("$.msg").value("状态参数错误"));

        // 验证服务层方法没有被调用
        verify(coldStorageCustomerService, never()).updateStatus(anyString(), anyInt());
    }

    /**
     * 测试根据手机号查询客户 - 成功
     */
    @Test
    void testGetByMobilePhoneSuccess() throws Exception {
        // 准备测试数据
        String mobilePhone = "13800138000";
        String customerId = "test-id-1";
        
        // Mock实体对象
        jnpf.entity.ColdStorageCustomerEntity entity = new jnpf.entity.ColdStorageCustomerEntity();
        entity.setId(customerId);
        entity.setMobilePhone(mobilePhone);
        
        ColdStorageCustomerVO customer = new ColdStorageCustomerVO();
        customer.setId(customerId);
        customer.setMobilePhone(mobilePhone);
        customer.setCustomerName("测试客户");

        // Mock服务层方法
        when(coldStorageCustomerService.getByMobilePhone(mobilePhone)).thenReturn(entity);
        when(coldStorageCustomerService.getInfo(customerId)).thenReturn(customer);

        // 执行测试
        mockMvc.perform(get("/api/coldstorage/customer/mobile/{mobilePhone}", mobilePhone))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.id").value(customerId))
                .andExpect(jsonPath("$.data.mobilePhone").value(mobilePhone));

        // 验证服务层方法被调用
        verify(coldStorageCustomerService).getByMobilePhone(mobilePhone);
        verify(coldStorageCustomerService).getInfo(customerId);
    }

    /**
     * 测试根据手机号查询客户 - 客户不存在
     */
    @Test
    void testGetByMobilePhoneNotFound() throws Exception {
        // 准备测试数据
        String mobilePhone = "13800138000";

        // Mock服务层方法
        when(coldStorageCustomerService.getByMobilePhone(mobilePhone)).thenReturn(null);

        // 执行测试
        mockMvc.perform(get("/api/coldstorage/customer/mobile/{mobilePhone}", mobilePhone))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(400))
                .andExpect(jsonPath("$.msg").value("客户不存在"));

        // 验证服务层方法被调用
        verify(coldStorageCustomerService).getByMobilePhone(mobilePhone);
        verify(coldStorageCustomerService, never()).getInfo(anyString());
    }

    /**
     * 测试根据客户编号查询客户 - 成功
     */
    @Test
    void testGetByCustomerCodeSuccess() throws Exception {
        // 准备测试数据
        String customerCode = "CS001";
        String customerId = "test-id-1";
        
        // Mock实体对象
        jnpf.entity.ColdStorageCustomerEntity entity = new jnpf.entity.ColdStorageCustomerEntity();
        entity.setId(customerId);
        entity.setCustomerCode(customerCode);
        
        ColdStorageCustomerVO customer = new ColdStorageCustomerVO();
        customer.setId(customerId);
        customer.setCustomerCode(customerCode);
        customer.setCustomerName("测试客户");

        // Mock服务层方法
        when(coldStorageCustomerService.getByCustomerCode(customerCode)).thenReturn(entity);
        when(coldStorageCustomerService.getInfo(customerId)).thenReturn(customer);

        // 执行测试
        mockMvc.perform(get("/api/coldstorage/customer/code/{customerCode}", customerCode))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.id").value(customerId))
                .andExpect(jsonPath("$.data.customerCode").value(customerCode));

        // 验证服务层方法被调用
        verify(coldStorageCustomerService).getByCustomerCode(customerCode);
        verify(coldStorageCustomerService).getInfo(customerId);
    }

    /**
     * 测试根据客户编号查询客户 - 客户不存在
     */
    @Test
    void testGetByCustomerCodeNotFound() throws Exception {
        // 准备测试数据
        String customerCode = "CS001";

        // Mock服务层方法
        when(coldStorageCustomerService.getByCustomerCode(customerCode)).thenReturn(null);

        // 执行测试
        mockMvc.perform(get("/api/coldstorage/customer/code/{customerCode}", customerCode))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(400))
                .andExpect(jsonPath("$.msg").value("客户不存在"));

        // 验证服务层方法被调用
        verify(coldStorageCustomerService).getByCustomerCode(customerCode);
        verify(coldStorageCustomerService, never()).getInfo(anyString());
    }
} 
package jnpf.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import jnpf.base.ActionResult;
import jnpf.base.vo.PageListVO;
import jnpf.base.vo.PaginationVO;
import jnpf.model.CustomerCompanyForm;
import jnpf.model.CustomerCompanyVO;
import jnpf.model.CustomerCompanyQueryForm;
import jnpf.service.CustomerCompanyService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import java.util.Collections;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@WebMvcTest(CustomerCompanyController.class)
public class CustomerCompanyControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private CustomerCompanyService customerCompanyService;

    @Autowired
    private ObjectMapper objectMapper;

    @Test
    public void testList() throws Exception {
        CustomerCompanyQueryForm queryForm = new CustomerCompanyQueryForm();
        queryForm.setCurrentPage(1);
        queryForm.setPageSize(10);

        CustomerCompanyVO customerCompanyVO = new CustomerCompanyVO();
        List<CustomerCompanyVO> list = Collections.singletonList(customerCompanyVO);
        PageListVO<CustomerCompanyVO> pageListVO = new PageListVO<>();
        pageListVO.setList(list);
        PaginationVO pagination = new PaginationVO();
        pagination.setTotal(1);
        pagination.setCurrentPage(1L);
        pagination.setPageSize(10L);
        pageListVO.setPagination(pagination);

        when(customerCompanyService.getList(any(CustomerCompanyQueryForm.class))).thenReturn(list);
        when(customerCompanyService.getCount(any(CustomerCompanyQueryForm.class))).thenReturn(1L);

        mockMvc.perform(post("/api/coldstorage/CustomerCompany/getList")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(queryForm)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.list").isArray())
                .andExpect(jsonPath("$.data.pagination.total").value(1));
    }

    @Test
    public void testListAll() throws Exception {
        CustomerCompanyVO customerCompanyVO = new CustomerCompanyVO();
        List<CustomerCompanyVO> list = Collections.singletonList(customerCompanyVO);

        when(customerCompanyService.getList()).thenReturn(list);

        mockMvc.perform(get("/api/coldstorage/CustomerCompany"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.list").isArray());
    }

    @Test
    public void testInfo() throws Exception {
        String id = "testId";
        CustomerCompanyVO customerCompanyVO = new CustomerCompanyVO();
        customerCompanyVO.setId(id);

        when(customerCompanyService.getInfo(id)).thenReturn(customerCompanyVO);

        mockMvc.perform(get("/api/coldstorage/CustomerCompany/{id}", id))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.id").value(id));
    }

    @Test
    public void testCreate() throws Exception {
        CustomerCompanyForm form = new CustomerCompanyForm();
        // Populate form with test data
        form.setCompanyName("Test Company");

        mockMvc.perform(post("/api/coldstorage/CustomerCompany")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(form)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.msg").value("创建成功"));
    }

    @Test
    public void testUpdate() throws Exception {
        String id = "testId";
        CustomerCompanyForm form = new CustomerCompanyForm();
        form.setCompanyName("Updated Company");

        mockMvc.perform(put("/api/coldstorage/CustomerCompany/{id}", id)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(form)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.msg").value("更新成功"));
    }

    @Test
    public void testDelete() throws Exception {
        String id = "testId";

        mockMvc.perform(delete("/api/coldstorage/CustomerCompany/{id}", id))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.msg").value("删除成功"));
    }

    @Test
    public void testUpdateStatus() throws Exception {
        String id = "testId";
        Integer status = 1;

        mockMvc.perform(put("/api/coldstorage/CustomerCompany/{id}/status", id)
                .param("status", status.toString()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.msg").value("状态更新成功"));
    }
}
package jnpf.flowable.model.candidates;

import io.swagger.v3.oas.annotations.media.Schema;
import jnpf.base.Pagination;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 类的描述
 *
 * <AUTHOR> Info. Co., Ltd
 * @version 5.0.x
 * @since 2024/4/20 11:06
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class CandidateUserFo extends Pagination {
    /**
     * 版本主键
     */
    @Schema(description = "版本主键")
    private String flowId;
    /**
     * 节点编码
     */
    @Schema(description = "节点编码")
    private String nodeCode;
    /**
     * 委托人
     */
    @Schema(description = "委托人")
    private String delegateUser;
}

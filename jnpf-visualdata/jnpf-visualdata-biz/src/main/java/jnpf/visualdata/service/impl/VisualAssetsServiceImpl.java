package jnpf.visualdata.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import jnpf.base.service.SuperServiceImpl;
import jnpf.util.RandomUtil;
import jnpf.util.StringUtil;
import jnpf.visualdata.entity.VisualAssetsEntity;
import jnpf.visualdata.mapper.VisualAssetsMapper;
import jnpf.visualdata.model.visual.VisualPaginationModel;
import jnpf.visualdata.service.VisualAssetsService;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * 静态资源
 *
 * <AUTHOR>
 * @version V3.5.0
 * @copyright 引迈信息技术有限公司
 * @date 2023年7月7日
 */
@Service
public class VisualAssetsServiceImpl extends SuperServiceImpl<VisualAssetsMapper, VisualAssetsEntity> implements VisualAssetsService {

    @Override
    public List<VisualAssetsEntity> getList(VisualPaginationModel pagination) {
        QueryWrapper<VisualAssetsEntity> queryWrapper = new QueryWrapper<>();
        if(ObjectUtil.isNotEmpty(pagination.getAssetsName())){
            queryWrapper.lambda().like(VisualAssetsEntity::getAssetsName, pagination.getAssetsName());
        }
        Page page = new Page(pagination.getCurrent(), pagination.getSize());
        IPage<VisualAssetsEntity> iPages = this.page(page, queryWrapper);
        return pagination.setData(iPages);
    }

    @Override
    public List<VisualAssetsEntity> getList() {
        QueryWrapper<VisualAssetsEntity> queryWrapper = new QueryWrapper<>();
        return this.list(queryWrapper);
    }

    @Override
    public VisualAssetsEntity getInfo(String id) {
        QueryWrapper<VisualAssetsEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(VisualAssetsEntity::getId, id);
        return this.getOne(queryWrapper);
    }

    @Override
    public void create(VisualAssetsEntity entity) {
        entity.setId(RandomUtil.uuId());
        this.save(entity);
    }

    @Override
    public boolean update(String id, VisualAssetsEntity entity) {
        entity.setId(id);
        return this.updateById(entity);
    }

    @Override
    public boolean delete(String id) {
        if (StringUtil.isNotEmpty(id)) {
            return this.removeById(id);
        }
        return false;
    }


}

package jnpf.permission.constant;

import jnpf.model.ExcelColumnAttr;
import org.apache.poi.ss.usermodel.IndexedColors;

import java.util.*;

public class OrgColumnMap {

    String excelName = "组织信息";
    /**
     * 全部字段
     */
    private Map<String, String> allKeyMap = new LinkedHashMap() {{
        put("parentId", "所属组织");
        put("fullName", "名称");
        put("enCode", "编码");
    }};
    /**
     * 组织map
     */
    private Map<String, String> orgMap = new LinkedHashMap() {{
        put("category", "类型");
        put("parentId", "上级公司");
        put("fullName", "公司名称");
        put("enCode", "公司编码");
        put("shortName", "公司简称");
        put("enterpriseNature", "公司性质");
        put("industry", "所属行业");
        put("foundedTime", "成立时间");
        put("telePhone", "公司电话");
        put("fax", "公司传真");
        put("webSite", "公司主页");
        put("address", "公司地址");
        put("managerName", "公司法人");
        put("managerTelePhone", "联系电话");
        put("managerMobilePhone", "联系手机");
        put("manageEmail", "联系邮箱");
        put("bankName", "开户银行");
        put("bankAccount", "银行账户");
        put("businessscope", "经营范围");
        put("managerId", "部门主管");
        put("sortCode", "排序");
        put("description", "说明");
    }};
    /**
     * 部门map
     */
    private Map<String, String> depMap = new LinkedHashMap() {{
        put("category", "类型");
        put("parentId", "所属组织");
        put("fullName", "部门名称");
        put("enCode", "部门编码");
        put("managerId", "部门主管");
        put("sortCode", "排序");
        put("description", "说明");
    }};

    /**
     * 根据类型获取excel表头字段
     *
     * @param type
     * @return
     */
    public Map<String, String> getColumnByType(Integer type) {
        Map<String, String> map = new LinkedHashMap();
        switch (type) {
            case 2:
                map = new LinkedHashMap(depMap);
                break;
            case 1:
                map = new LinkedHashMap(orgMap);
                map.remove("managerId");
                break;
            default:
                map = new LinkedHashMap(orgMap);
                map.putAll(allKeyMap);
                break;
        }
        return map;
    }

    public String getExcelName() {
        return excelName;
    }


    public List<ExcelColumnAttr> getFieldsModel(boolean isError, Integer type) {
        List<ExcelColumnAttr> models = new ArrayList<>();
        //异常原因
        if (isError) {
            ExcelColumnAttr attr = new ExcelColumnAttr().builder()
                    .key("errorsInfo")
                    .name("异常原因")
                    .build();
            models.add(attr);
        }
        List<String> requirelist = Arrays.asList("category", "fullName", "enCode");
        // 遍历添加属性
        Map<String, String> keyMap = getColumnByType(type);

        for (String key : keyMap.keySet()) {
            ExcelColumnAttr attr = ExcelColumnAttr.builder()
                    .key(key)
                    .name(keyMap.get(key))
                    .build();
            if (requirelist.contains(key)) {
                attr.setRequire(true);
                attr.setFontColor(IndexedColors.RED.getIndex());
            }
            models.add(attr);
        }
        return models;
    }

    /**
     * 获取默认值
     */
    public List<Map<String, Object>> getDefaultList() {
        Map<String, Object> orgMapDemo = new HashMap<>();
        orgMapDemo.put("fullName", "公司名称/公司名称1");
        orgMapDemo.put("foundedTime", "yyy-MM-dd");
        Map<String, Object> depMapDemo = new HashMap<>();
        depMapDemo.put("fullName", "公司名称/公司名称1/部门名称");
        depMapDemo.put("managerId", "姓名/账号");
        List<Map<String, Object>> list = new ArrayList<>();
        list.add(orgMapDemo);
        list.add(depMapDemo);
        return list;
    }
}

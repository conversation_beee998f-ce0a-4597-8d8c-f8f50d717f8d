package jnpf.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import jnpf.model.ColdStorageProjectForm;
import jnpf.model.ColdStorageProjectVO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.validation.beanvalidation.LocalValidatorFactoryBean;

import jnpf.service.ColdStorageProjectService;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;

import org.springframework.test.web.servlet.MvcResult;

/**
 * 冷库项目控制器参数验证测试
 *
 * @版本： V5.2.0
 * @版权： 杭州多协信息技术有限公司（https://www.duoxieyun.com）
 * @作者： 多协开发组
 * @日期： 2025-01-27
 */
@ExtendWith(MockitoExtension.class)
class ColdStorageProjectControllerValidationTest {

    @Mock
    private ColdStorageProjectService coldStorageProjectService;

    @InjectMocks
    private ColdStorageProjectController coldStorageProjectController;

    private MockMvc mockMvc;
    private ObjectMapper objectMapper;

    @BeforeEach
    void setUp() {
        // 配置验证器和异常处理器
        LocalValidatorFactoryBean validator = new LocalValidatorFactoryBean();
        validator.afterPropertiesSet();
        
        mockMvc = MockMvcBuilders.standaloneSetup(coldStorageProjectController)
                .setValidator(validator)
                .setControllerAdvice(new GlobalExceptionHandler())
                .defaultResponseCharacterEncoding(java.nio.charset.StandardCharsets.UTF_8)
                .build();
        objectMapper = TestEncodingUtils.createUTF8ObjectMapper();
    }

    @Test
    void testCreateProject_ValidData_Success() throws Exception {
        System.out.println("\n=== 开始测试：创建项目 - 有效数据成功场景 ===");
        
        // 准备有效的测试数据
        ColdStorageProjectForm form = new ColdStorageProjectForm();
        form.setProjectCode("PROJ001");
        form.setProjectName("测试项目");
        form.setCompanyName("测试公司");
        form.setProjectManager("张三");
        form.setManagerPhone("***********");
        form.setProjectStatus(1);
        form.setProjectRemark("测试备注");

        System.out.println("有效测试数据: " + objectMapper.writeValueAsString(form));

        // Mock服务层方法
        when(coldStorageProjectService.existsByProjectCode(anyString(), any())).thenReturn(false);
        doNothing().when(coldStorageProjectService).create(any(ColdStorageProjectForm.class));

        System.out.println("Mock配置：项目编号不存在，允许创建");

        // 执行测试并验证
        MvcResult result = mockMvc.perform(post("/api/coldstorage/project")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(form)))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andReturn();

        String responseBody = result.getResponse().getContentAsString(java.nio.charset.StandardCharsets.UTF_8);
        System.out.println("实际响应状态码: " + result.getResponse().getStatus());
        System.out.println("实际响应内容: " + responseBody);
        System.out.println("=== 测试完成：创建项目成功 ===\n");
    }

    @Test
    void testCreateProject_EmptyProjectCode_BusinessValidationError() throws Exception {
        System.out.println("\n=== 开始测试：创建项目 - 空项目编号验证失败 ===");
        
        // 准备无效数据 - 空项目编号
        ColdStorageProjectForm form = new ColdStorageProjectForm();
        form.setProjectCode(""); // 空项目编号
        form.setProjectName("测试项目");
        form.setCompanyName("测试公司");
        form.setProjectManager("张三");
        form.setManagerPhone("***********");
        form.setProjectStatus(1);

        System.out.println("无效测试数据（空项目编号）: " + objectMapper.writeValueAsString(form));
        System.out.println("预期结果：应该返回400错误，包含'项目编号不能为空'消息");

        // 执行测试并验证错误信息
        MvcResult result = mockMvc.perform(post("/api/coldstorage/project")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(form)))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(400))
                .andExpect(jsonPath("$.msg").value(org.hamcrest.Matchers.containsString("项目编号不能为空")))
                .andReturn();

        String responseBody = result.getResponse().getContentAsString(java.nio.charset.StandardCharsets.UTF_8);
        System.out.println("实际响应状态码: " + result.getResponse().getStatus());
        System.out.println("实际响应内容: " + responseBody);
        System.out.println("=== 测试完成：空项目编号验证 ===\n");
    }

    @Test
    void testCreateProject_ProjectCodeExists_BusinessValidationError() throws Exception {
        // 准备有效数据，但项目编号已存在
        ColdStorageProjectForm form = new ColdStorageProjectForm();
        form.setProjectCode("EXIST001");
        form.setProjectName("测试项目");
        form.setCompanyName("测试公司");
        form.setProjectManager("张三");
        form.setManagerPhone("***********");
        form.setProjectStatus(1);

        // Mock服务层方法 - 项目编号已存在
        when(coldStorageProjectService.existsByProjectCode(eq("EXIST001"), any())).thenReturn(true);

        // 执行测试并验证错误信息
        mockMvc.perform(post("/api/coldstorage/project")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(form)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(400))
                .andExpect(jsonPath("$.msg").value("项目编号已存在"));
    }

    @Test
    void testCreateProject_EmptyProjectName_BusinessValidationError() throws Exception {
        // 准备无效数据 - 空项目名称
        ColdStorageProjectForm form = new ColdStorageProjectForm();
        form.setProjectCode("PROJ001");
        form.setProjectName(""); // 空项目名称
        form.setCompanyName("测试公司");
        form.setProjectManager("张三");
        form.setManagerPhone("***********");
        form.setProjectStatus(1);

        // 执行测试并验证错误信息
        mockMvc.perform(post("/api/coldstorage/project")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(form)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(400))
                .andExpect(jsonPath("$.msg").value("项目名称不能为空"));
    }

    @Test
    void testCreateProject_EmptyCompanyName_BusinessValidationError() throws Exception {
        // 准备无效数据 - 空公司名称
        ColdStorageProjectForm form = new ColdStorageProjectForm();
        form.setProjectCode("PROJ001");
        form.setProjectName("测试项目");
        form.setCompanyName(""); // 空公司名称
        form.setProjectManager("张三");
        form.setManagerPhone("***********");
        form.setProjectStatus(1);

        // 执行测试并验证错误信息
        mockMvc.perform(post("/api/coldstorage/project")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(form)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(400))
                .andExpect(jsonPath("$.msg").value("公司名称不能为空"));
    }

    @Test
    void testCreateProject_EmptyProjectManager_BusinessValidationError() throws Exception {
        // 准备无效数据 - 空项目负责人
        ColdStorageProjectForm form = new ColdStorageProjectForm();
        form.setProjectCode("PROJ001");
        form.setProjectName("测试项目");
        form.setCompanyName("测试公司");
        form.setProjectManager(""); // 空项目负责人
        form.setManagerPhone("***********");
        form.setProjectStatus(1);

        // 执行测试并验证错误信息
        mockMvc.perform(post("/api/coldstorage/project")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(form)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(400))
                .andExpect(jsonPath("$.msg").value("项目负责人不能为空"));
    }

    @Test
    void testCreateProject_EmptyManagerPhone_BusinessValidationError() throws Exception {
        // 准备无效数据 - 空联系电话
        ColdStorageProjectForm form = new ColdStorageProjectForm();
        form.setProjectCode("PROJ001");
        form.setProjectName("测试项目");
        form.setCompanyName("测试公司");
        form.setProjectManager("张三");
        form.setManagerPhone(""); // 空联系电话
        form.setProjectStatus(1);

        // 执行测试并验证错误信息
        mockMvc.perform(post("/api/coldstorage/project")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(form)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(400))
                .andExpect(jsonPath("$.msg").value("负责人联系电话不能为空"));
    }

    @Test
    void testCreateProject_NullProjectStatus_BusinessValidationError() throws Exception {
        // 准备无效数据 - 空项目状态
        ColdStorageProjectForm form = new ColdStorageProjectForm();
        form.setProjectCode("PROJ001");
        form.setProjectName("测试项目");
        form.setCompanyName("测试公司");
        form.setProjectManager("张三");
        form.setManagerPhone("***********");
        form.setProjectStatus(null); // 空项目状态

        // 执行测试并验证错误信息
        mockMvc.perform(post("/api/coldstorage/project")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(form)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(400))
                .andExpect(jsonPath("$.msg").value("项目状态不能为空"));
    }

    @Test
    void testCreateProject_NullForm_BusinessValidationError() throws Exception {
        // 测试空表单
        mockMvc.perform(post("/api/coldstorage/project")
                .contentType(MediaType.APPLICATION_JSON)
                .content("null"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(400))
                .andExpect(jsonPath("$.msg").value("请求参数不能为空"));
    }

    @Test
    void testGetProjectInfo_EmptyId_ValidationError() throws Exception {
        // 测试空ID获取项目信息
        mockMvc.perform(get("/api/coldstorage/project/"))
                .andExpect(status().isNotFound());
    }

    @Test
    void testGetProjectInfo_ValidId_Success() throws Exception {
        // Mock服务层方法
        when(coldStorageProjectService.getInfo(eq("test-id"))).thenReturn(null);

        // 测试有效ID但项目不存在
        mockMvc.perform(get("/api/coldstorage/project/test-id"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(400))
                .andExpect(jsonPath("$.msg").value("项目不存在"));
    }

    @Test
    void testGetProjectInfo_ValidIdWithData_Success() throws Exception {
        // 创建模拟的项目数据
        ColdStorageProjectVO mockProject = new ColdStorageProjectVO();
        mockProject.setId("test-id");
        mockProject.setProjectName("测试项目");
        
        // Mock服务层方法
        when(coldStorageProjectService.getInfo(eq("test-id"))).thenReturn(mockProject);

        // 测试有效ID且项目存在
        mockMvc.perform(get("/api/coldstorage/project/test-id"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }

    @Test
    void testUpdateProject_EmptyId_ValidationError() throws Exception {
        // 准备更新数据
        ColdStorageProjectForm form = new ColdStorageProjectForm();
        form.setProjectCode("PROJ001");
        form.setProjectName("测试项目");
        form.setCompanyName("测试公司");
        form.setProjectManager("张三");
        form.setManagerPhone("***********");
        form.setProjectStatus(1);

        // 测试空ID更新项目
        mockMvc.perform(put("/api/coldstorage/project/")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(form)))
                .andExpect(status().isNotFound());
    }

    @Test
    void testUpdateProject_ValidIdProjectNotExists_ValidationError() throws Exception {
        // 准备更新数据
        ColdStorageProjectForm form = new ColdStorageProjectForm();
        form.setProjectCode("PROJ001");
        form.setProjectName("测试项目");
        form.setCompanyName("测试公司");
        form.setProjectManager("张三");
        form.setManagerPhone("***********");
        form.setProjectStatus(1);

        // Mock服务层方法 - 项目不存在
        when(coldStorageProjectService.getInfo(eq("non-exist-id"))).thenReturn(null);

        // 测试项目不存在的更新
        mockMvc.perform(put("/api/coldstorage/project/non-exist-id")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(form)))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(400))
                .andExpect(jsonPath("$.msg").value("项目不存在"));
    }

    @Test
    void testDeleteProject_EmptyId_ValidationError() throws Exception {
        // 测试空ID删除项目
        mockMvc.perform(delete("/api/coldstorage/project/"))
                .andExpect(status().isNotFound());
    }

    @Test
    void testDeleteProject_ValidIdProjectNotExists_ValidationError() throws Exception {
        // Mock服务层方法 - 项目不存在
        when(coldStorageProjectService.getInfo(eq("non-exist-id"))).thenReturn(null);

        // 测试删除不存在的项目
        mockMvc.perform(delete("/api/coldstorage/project/non-exist-id"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(400))
                .andExpect(jsonPath("$.msg").value("项目不存在"));
    }

    @Test
    void testGetProjectsByCustomerId_EmptyCustomerId_ValidationError() throws Exception {
        // 测试空客户ID获取项目列表
        mockMvc.perform(get("/api/coldstorage/project/customer/"))
                .andExpect(status().isNotFound());
    }
} 
package jnpf.permission.constant;

import jnpf.model.ExcelColumnAttr;
import org.apache.poi.ss.usermodel.IndexedColors;

import java.util.*;

public class UserColumnMap {

    String excelName = "用户信息";

    Map<String, String> keyMap = new LinkedHashMap() {{
        put("account", "账户");
        put("realName", "姓名");
        put("gender", "性别");
        put("email", "电子邮箱");
        put("organizeId", "所属组织");
        put("managerId", "直属主管");
        put("positionId", "岗位");
        put("ranks", "职级");
        put("roleId", "角色");
        put("nation", "民族");
        put("nativePlace", "籍贯");
        put("entryDate", "入职时间");
        put("certificatesType", "证件类型");
        put("certificatesNumber", "证件号码");
        put("education", "文化程度");
        put("birthday", "出生年月");
        put("telePhone", "办公电话");
        put("landline", "办公座机");
        put("mobilePhone", "手机号码");
        put("urgentContacts", "紧急联系");
        put("urgentTelePhone", "紧急电话");
        put("postalAddress", "通讯地址");

        put("enabledMark", "状态");
        put("sortCode", "排序");
        put("description", "说明");
    }};

    /**
     * 表格名称
     *
     * @return
     */
    public String getExcelName() {
        return excelName;
    }

    /**
     * 根据类型获取excel表头字段
     *
     * @param type
     * @return
     */
    public Map<String, String> getColumnByType(Integer type) {
        return keyMap;
    }

    /**
     * 获取字段列表
     *
     * @param isError
     * @return
     */
    public List<ExcelColumnAttr> getFieldsModel(boolean isError) {
        List<ExcelColumnAttr> models = new ArrayList<>();
        //异常原因
        if (isError) {
            ExcelColumnAttr attr = ExcelColumnAttr.builder().key("errorsInfo").name("异常原因").build();
            models.add(attr);
        }
        List<String> requireFields = Arrays.asList("account", "realName", "gender", "organizeId", "enabledMark");
        for (String key : keyMap.keySet()) {
            ExcelColumnAttr attr = ExcelColumnAttr.builder().key(key).name(keyMap.get(key)).build();
            if (requireFields.contains(key)) {
                attr.setRequire(true);
                attr.setFontColor(IndexedColors.RED.getIndex());
            }
            models.add(attr);
        }
        return models;
    }

    /**
     * 获取默认值
     */
    public List<Map<String, Object>> getDefaultList() {
        List<Map<String, Object>> list = new ArrayList<>();
        Map<String, Object> map = new HashMap<>();
        map.put("organizeId", "公司名称/公司名称1/部门名称,公司名称/公司名称1/部门名称1");
        map.put("managerId", "姓名/账号");
        map.put("positionId", "岗位名称/岗位编码,岗位名称1/岗位编码1");
        map.put("roleId", "角色名称/角色编码,角色名称1/角色编码1");
        map.put("entryDate", "yyyy-MM-dd");
        map.put("birthday", "yyyy-MM-dd");
        list.add(map);
        return list;
    }
}

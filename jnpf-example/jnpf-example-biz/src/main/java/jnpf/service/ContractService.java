package jnpf.service;

import jnpf.base.service.SuperService;
import com.baomidou.mybatisplus.extension.service.IService;
import jnpf.base.Pagination;
import jnpf.entity.ContractEntity;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @version V3.1.0
 * @copyright 引迈信息技术有限公司
 * @date 2021/3/16 9:47
 */
public interface ContractService extends SuperService<ContractEntity> {

    List<ContractEntity> getlist(Pagination pagination);

    ContractEntity getInfo(String id);

    void create(ContractEntity entity);

    void update(String id, ContractEntity entity);

    void delete(ContractEntity entity);
}

package jnpf;

import jnpf.controller.CustomerCompanyController;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.FilterType;

/**
 * A minimal Spring Boot application for testing purposes.
 */
@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class})
@ComponentScan(basePackages = {"jnpf.controller"}, useDefaultFilters = false, includeFilters = {
    @ComponentScan.Filter(type = FilterType.ASSIGNABLE_TYPE, value = CustomerCompanyController.class)
})
public class TestApplication {
    public static void main(String[] args) {
        // This main method is not intended to be run,
        // but it is required for the class to be a valid Spring Boot application.
    }
}
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="jnpf.mapper.TrainLibraryMoreMapper">

    <select id="getListByParentId" resultType="jnpf.entity.TrainLibraryMoreEntity">
        select f_id        as id,
               f_bank_id   as fbankId,
               f_full_name as ffullName,
               f_options_a as foptionsA,
               f_options_b as foptionsB,
               f_options_c as foptionsC,
               f_options_d as foptionsD,
               f_answer    as fanswer
        from make_train_library_more
        where f_bank_id = #{parentId}
    </select>
</mapper>

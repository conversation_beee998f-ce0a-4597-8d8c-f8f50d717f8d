# 密码安全改进方案

## 概述

为提高系统安全性，我们将原有的MD5密码加密方式升级为SHA-256+盐值的加密方案，大幅提升密码安全性。

## 安全改进

### 原有方案问题
- **MD5算法脆弱** - 易被彩虹表攻击
- **无盐值设计** - 相同密码产生相同哈希
- **计算速度快** - 便于暴力破解
- **无密码强度验证** - 允许弱密码注册

### 新方案优势
- **SHA-256算法** - 更安全的哈希算法
- **随机盐值** - 每个密码使用唯一盐值
- **多次迭代** - 10,000次迭代增加破解难度
- **密码强度验证** - 强制使用安全密码
- **向后兼容** - 平滑迁移，不影响现有用户

## 技术实现

### 1. 数据库变更

在`cs_customer`表中添加盐值字段：
```sql
ALTER TABLE cs_customer ADD COLUMN F_PASSWORD_SALT VARCHAR(100) COMMENT '密码盐值' AFTER F_PASSWORD;
```

### 2. 密码加密流程

#### 新用户注册
```java
// 1. 验证密码强度
if (!SecurePasswordUtil.isPasswordSecure(password)) {
    throw new RuntimeException("密码强度不足");
}

// 2. 生成盐值和加密密码
SecurePasswordUtil.PasswordResult result = SecurePasswordUtil.generatePassword(password);
customer.setPassword(result.getHashedPassword());
customer.setPasswordSalt(result.getSalt());
```

#### 密码验证
```java
// 检查加密方式
if (StringUtils.hasText(customer.getPasswordSalt())) {
    // 新的SHA-256+盐值验证
    return SecurePasswordUtil.verifyPassword(inputPassword, storedHash, salt);
} else {
    // 兼容旧的MD5验证，并自动升级
    if (Md5Util.getStringMd5(inputPassword).equals(customer.getPassword())) {
        // 验证成功，升级加密方式
        upgradePasswordSecurity(customer, inputPassword);
        return true;
    }
    return false;
}
```

### 3. 加密算法详情

- **算法**: SHA-256
- **盐值长度**: 32字节（Base64编码后44字符）
- **迭代次数**: 10,000次
- **编码方式**: Base64

## 密码强度要求

### 强度评分标准
- **长度**: 8位以上（25分），12位以上（+10分）
- **小写字母**: 包含a-z（15分）
- **大写字母**: 包含A-Z（15分）
- **数字**: 包含0-9（15分）
- **特殊字符**: 包含!@#$%等（20分）

### 最低要求
- 总分≥60分才能注册
- 推荐使用8位以上包含大小写字母、数字和特殊字符的密码

### 示例
```
弱密码: "123456" (0分) ❌
中等密码: "password123" (55分) ❌
强密码: "MyPass123!" (85分) ✅
```

## 部署说明

### 1. 数据库迁移
```bash
# 执行SQL脚本添加盐值字段
mysql -u username -p database_name < docs/sql/update_password_security.sql
```

### 2. 代码部署
- 部署包含新密码加密逻辑的代码
- 新注册用户自动使用新加密方式
- 现有用户登录时自动升级密码加密

### 3. 验证测试
```bash
# 测试新用户注册（应使用SHA-256+盐值）
POST /api/coldstorage/miniapp/register
{
    "password": "MySecure123!",
    "confirmPassword": "MySecure123!",
    ...
}

# 测试现有用户登录（应自动升级）
POST /api/coldstorage/miniapp/login
{
    "loginType": "1",
    "mobilePhone": "13912345678",
    "password": "oldpassword"
}
```

## 兼容性保证

### 平滑迁移
1. **新用户** - 直接使用新加密方式注册
2. **现有用户** - 首次登录时自动升级密码加密
3. **数据完整性** - 升级过程不影响用户正常使用

### 回滚方案
如需回滚到旧版本：
1. 保持数据库表结构（F_PASSWORD_SALT字段可保留）
2. 恢复使用Md5Util.getStringMd5()进行密码验证
3. 新用户注册时不设置盐值字段

## 安全建议

### 1. 定期密码策略
- 建议用户定期更换密码（如6个月）
- 实施密码历史记录，防止重复使用
- 考虑添加密码到期提醒功能

### 2. 账户安全
- 实施登录失败锁定机制
- 添加异常登录地点检测
- 考虑引入双因素认证（2FA）

### 3. 监控和审计
- 记录密码修改日志
- 监控异常登录尝试
- 定期安全审计

## 性能影响

### 加密性能
- SHA-256+10000次迭代约需1-5ms
- 对用户体验影响极小
- 服务器CPU使用略有增加

### 存储空间
- 每个用户增加约44字节盐值存储
- 对数据库存储影响微乎其微

## 相关文件

- `SecurePasswordUtil.java` - 密码加密工具类
- `ColdStorageCustomerEntity.java` - 客户实体（添加盐值字段）
- `MiniAppAuthServiceImpl.java` - 认证服务（集成新加密逻辑）
- `update_password_security.sql` - 数据库迁移脚本

## 常见问题

### Q: 现有用户的密码会丢失吗？
A: 不会。系统保持向后兼容，现有用户可正常登录，登录后自动升级为新加密方式。

### Q: 如何判断用户使用的是哪种加密方式？
A: 通过`passwordSalt`字段判断：有值=新方式，NULL=旧方式。

### Q: 忘记密码怎么办？
A: 通过短信验证码重置密码，重置后自动使用新加密方式。

### Q: 密码强度检查太严格怎么办？
A: 可以调整`SecurePasswordUtil.isPasswordSecure()`方法中的最低分数要求。

---

这次密码安全升级大幅提升了系统的安全防护能力，为用户数据提供更可靠的保护。 
package jnpf.permission.model.organize;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @version V3.1.0
 * @copyright 引迈信息技术有限公司
 * @date 2021/3/12 15:31
 */
@Data
public class OrganizeSelectorVO {
    @Schema(description = "主键")
    private String id;
    @Schema(description = "父主键")
    private String parentId;
    @Schema(description = "名称")
    private String fullName;
    @Schema(description = "编码")
    private String enCode;
    @Schema(description = "备注")
    private String description;
    @Schema(description = "图标")
    private String icon;
    @Schema(description = "是否可用")
    private Integer enabledMark;
    @Schema(description = "是否有下级菜单")
    private Boolean hasChildren;
    @Schema(description = "下级菜单列表")
    private List<OrganizeSelectorVO> children;
    @Schema(description = "")
    private Boolean isLeaf;
    @JSONField(name="category")
    private String type;

    private Long creatorTime;
    @Schema(description = "排序")
    private Long sortCode;
    @Schema(description = "组织id树名称")
    private String organizeIdTree;

    @Schema(description = "修改用户")
    private String lastFullName;

    @Schema(description = "组织id")
    private String organize;

    @Schema(description = "组织id树")
    private List<String> organizeIds;

}

# 冷库储能项目模块

## 项目简介

冷库储能项目模块是基于JNPF开发平台构建的冷库管理系统，支持项目管理、冷库管理、客户管理以及小程序客户端功能。

## 功能特性

### 🏗️ 核心功能模块

#### 1. 项目管理模块
- ✅ 项目信息的增删改查
- ✅ 项目编号、名称、地址、公司、负责人等完整信息管理
- ✅ 项目状态管理（规划中、建设中、运营中、暂停、已完成）

#### 2. 冷库管理模块
- ✅ 冷库信息的增删改查
- ✅ 冷库编号、名称、地址、联系人等基础信息
- ✅ 冷库技术参数（类型、容量、温度范围、湿度）
- ✅ 冷库状态管理（停用、正常运行、故障、维护中）
- ✅ 与项目的关联关系（一对多）

#### 3. 客户管理模块（小程序用户）
- ✅ 独立的客户账户系统
- ✅ 客户与项目的多对多关联关系
- ✅ 权限管理（只读、管理权限）

### 🔐 小程序认证功能

#### 支持三种登录方式：
1. **账号密码登录** - 手机号 + 密码
2. **微信小程序授权登录** - 微信一键登录
3. **手机号验证码登录** - 短信验证码登录

#### 认证功能：
- ✅ 用户注册（手机号 + 验证码）
- ✅ Token 管理（访问令牌 + 刷新令牌）
- ✅ 用户退出登录
- ✅ 微信账号绑定/解绑
- ✅ 短信验证码发送

### 📊 统计分析功能
- ✅ 综合统计信息
- ✅ 项目状态统计
- ✅ 冷库类型统计
- ✅ 冷库状态统计
- ✅ 月度统计（最近12个月）
- ✅ 客户专属统计

## 技术架构

### 🏗️ 模块结构
```
jnpf-coldstorage/
├── jnpf-coldstorage-entity/     # 实体类和模型类
├── jnpf-coldstorage-biz/        # 业务逻辑层（Mapper + Service）
├── jnpf-coldstorage-controller/ # 控制器层（REST API）
└── docs/                        # 文档和SQL脚本
    └── sql/
        └── init_coldstorage.sql # 数据库初始化脚本
```

### 🗄️ 数据库设计
- `cs_project` - 项目表
- `cs_cold_storage` - 冷库表
- `cs_customer` - 客户表
- `cs_customer_project_relation` - 客户项目关联表

### 🔧 技术栈
- **框架**: Spring Boot + MyBatis Plus
- **数据库**: MySQL 8.0+
- **缓存**: Redis（用于Token和验证码存储）
- **API文档**: Swagger 3.0

## 快速开始

### 1. 数据库初始化
执行SQL脚本初始化数据库表结构和示例数据：
```sql
source docs/sql/init_coldstorage.sql
```

### 2. 配置依赖
确保主项目pom.xml中已包含冷库储能模块：
```xml
<dependency>
    <groupId>com.jnpf</groupId>
    <artifactId>jnpf-coldstorage-controller</artifactId>
    <version>${project.version}</version>
</dependency>
```

### 3. 启动项目
启动JNPF主项目，冷库储能模块将自动加载。

## API 接口文档

### 📱 管理端接口

#### 项目管理
- `GET /api/coldstorage/project` - 获取项目列表
- `GET /api/coldstorage/project/{id}` - 获取项目详情
- `POST /api/coldstorage/project` - 创建项目
- `PUT /api/coldstorage/project/{id}` - 更新项目
- `DELETE /api/coldstorage/project/{id}` - 删除项目

#### 冷库管理
- `GET /api/coldstorage/storage` - 获取冷库列表
- `GET /api/coldstorage/storage/{id}` - 获取冷库详情
- `POST /api/coldstorage/storage` - 创建冷库
- `PUT /api/coldstorage/storage/{id}` - 更新冷库
- `DELETE /api/coldstorage/storage/{id}` - 删除冷库

#### 客户管理
- `GET /api/coldstorage/customer` - 获取客户列表
- `GET /api/coldstorage/customer/{id}` - 获取客户详情
- `POST /api/coldstorage/customer` - 创建客户
- `PUT /api/coldstorage/customer/{id}` - 更新客户
- `DELETE /api/coldstorage/customer/{id}` - 删除客户

#### 权限管理
- `POST /api/coldstorage/relation/customer/{customerId}/assign` - 为客户分配项目权限
- `DELETE /api/coldstorage/relation/customer/{customerId}/remove` - 移除客户项目权限
- `GET /api/coldstorage/relation/customer/{customerId}` - 获取客户权限列表

#### 统计分析
- `GET /api/coldstorage/statistics/overall` - 获取综合统计
- `GET /api/coldstorage/statistics/project-status` - 项目状态统计
- `GET /api/coldstorage/statistics/storage-type` - 冷库类型统计
- `GET /api/coldstorage/statistics/monthly` - 月度统计

### 📱 小程序端接口

#### 用户认证
- `POST /api/coldstorage/miniapp/login` - 客户登录
- `POST /api/coldstorage/miniapp/register` - 客户注册
- `POST /api/coldstorage/miniapp/sms/send` - 发送验证码
- `POST /api/coldstorage/miniapp/wechat/login` - 微信登录
- `POST /api/coldstorage/miniapp/logout` - 退出登录

#### 数据查询
- `GET /api/coldstorage/miniapp/data/projects` - 获取关联项目列表
- `GET /api/coldstorage/miniapp/data/project/{projectId}` - 获取项目详情
- `GET /api/coldstorage/miniapp/data/project/{projectId}/storages` - 获取项目冷库列表
- `GET /api/coldstorage/miniapp/data/storage/{storageId}` - 获取冷库详情
- `GET /api/coldstorage/miniapp/data/storages` - 获取所有关联冷库

#### 搜索功能
- `GET /api/coldstorage/miniapp/data/projects/search` - 搜索项目
- `GET /api/coldstorage/miniapp/data/storages/search` - 搜索冷库

#### 统计信息
- `GET /api/coldstorage/statistics/customer` - 获取客户统计信息

## 示例数据

系统内置了完整的示例数据，包括：

### 示例项目
- 北京冷链物流园一期
- 上海生鲜配送中心
- 广州冷库扩建项目

### 示例冷库
- 北京物流园1号冷库（冷冻库，5000m³）
- 北京物流园2号冷库（保鲜库，3000m³）
- 上海配送中心主库（冷藏库，8000m³）
- 广州扩建A区冷库（冷冻库，6000m³）

### 示例客户
- 北京鲜果商贸有限公司
- 上海海鲜供应链有限公司
- 广州食品配送中心

## 配置说明

### Redis配置
系统使用Redis存储Token和验证码，需要确保Redis服务正常运行：

```yaml
spring:
  redis:
    host: localhost
    port: 6379
    database: 0
```

### 短信服务配置
需要集成具体的短信服务商API，在 `MiniAppAuthServiceImpl` 中的 `sendSmsCode` 方法中配置。

### 微信小程序配置
需要配置微信小程序的AppID和AppSecret，在 `MiniAppAuthServiceImpl` 中的 `getWechatUserInfo` 方法中配置。

## 开发指南

### 扩展功能
1. 在 `jnpf-coldstorage-entity` 模块中添加新的实体类和模型类
2. 在 `jnpf-coldstorage-biz` 模块中添加业务逻辑
3. 在 `jnpf-coldstorage-controller` 模块中添加REST接口

### 数据库扩展
可以根据业务需求扩展数据库表结构，添加新的字段或表。

### 权限扩展
系统支持灵活的权限配置，可以根据需要扩展权限类型。

## 注意事项

1. **安全性**: 所有密码都经过MD5加密存储
2. **权限验证**: 小程序端接口都需要Token验证
3. **数据完整性**: 删除操作会检查关联关系
4. **缓存策略**: Token有效期2小时，刷新令牌有效期7天

## 更新日志

### v1.0.0 (2025-07-25)
- ✅ 完成基础项目管理功能
- ✅ 完成冷库管理功能
- ✅ 完成客户管理功能
- ✅ 完成小程序三种登录方式
- ✅ 完成权限管理系统
- ✅ 完成统计分析功能
- ✅ 完成数据库设计和初始化

## 技术支持

如有问题或建议，请联系开发团队。

---

**版本**: V5.2.0  
**作者**: JNPF开发平台组  
**日期**: 2025-07-25
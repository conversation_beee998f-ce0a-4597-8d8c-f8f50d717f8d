package jnpf.permission.model.position;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import jakarta.validation.constraints.NotBlank;

import jakarta.validation.constraints.NotNull;

/**
 *
 * <AUTHOR>
 * @version V3.1.0
 * @copyright 引迈信息技术有限公司
 * @date 2021/3/12 15:31
 */
@Data
public class PositionCrForm {
    @NotBlank(message = "必填")
    @Schema(description = "岗位编码")
    private String enCode;
    @NotBlank(message = "必填")
    @Schema(description = "所属部门(id)")
    private String organizeId;
    @NotNull(message = "必填")
    @Schema(description = "岗位状态")
    private Integer enabledMark;
    @NotBlank(message = "必填")
    @Schema(description = "岗位名称")
    private String fullName;

    private String description;
    @NotNull(message = "必填")
    @Schema(description = "岗位类型(id)")
    private String type;
    @Schema(description = "排序")
    private Long sortCode;
}

# 冷库项目参数验证功能总结

## 概述

为 `ColdStorageProjectController` 增加了完善的入参验证功能，确保API接口的数据安全性和可靠性。

## 验证功能特性

### 1. 表单字段验证 (ColdStorageProjectForm)

#### 项目编号 (projectCode)
- **必填验证**: `@NotBlank(message = "项目编号不能为空")`
- **长度验证**: `@Size(min = 2, max = 50, message = "项目编号长度必须在2-50个字符之间")`
- **格式验证**: `@Pattern(regexp = "^[A-Za-z0-9_-]+$", message = "项目编号只能包含字母、数字、下划线和横线")`

#### 项目名称 (projectName)
- **必填验证**: `@NotBlank(message = "项目名称不能为空")`
- **长度验证**: `@Size(min = 2, max = 100, message = "项目名称长度必须在2-100个字符之间")`

#### 公司名称 (companyName)
- **必填验证**: `@NotBlank(message = "公司名称不能为空")`
- **长度验证**: `@Size(min = 2, max = 200, message = "公司名称长度必须在2-200个字符之间")`

#### 项目负责人 (projectManager)
- **必填验证**: `@NotBlank(message = "项目负责人不能为空")`
- **长度验证**: `@Size(min = 2, max = 50, message = "项目负责人姓名长度必须在2-50个字符之间")`

#### 负责人联系电话 (managerPhone)
- **必填验证**: `@NotBlank(message = "负责人联系电话不能为空")`
- **格式验证**: `@Pattern(regexp = "^1[3-9]\\d{9}$", message = "请输入正确的手机号码格式")`

#### 项目状态 (projectStatus)
- **必填验证**: `@NotNull(message = "项目状态不能为空")`
- **范围验证**: `@Min(value = 0, message = "项目状态值不能小于0")`
- **范围验证**: `@Max(value = 4, message = "项目状态值不能大于4")`

#### 项目备注 (projectRemark)
- **长度验证**: `@Size(max = 500, message = "项目备注长度不能超过500个字符")`

### 2. 路径参数验证

#### 项目ID验证
```java
@PathVariable("id") @NotBlank(message = "项目ID不能为空") String id
```

#### 客户ID验证
```java
@PathVariable("customerId") @NotBlank(message = "客户ID不能为空") String customerId
```

### 3. 业务逻辑验证

#### 创建项目时的验证
- 检查项目编号是否已存在
- 参数非空检查
- 异常捕获和处理

#### 更新项目时的验证
- 检查项目是否存在
- 检查项目编号是否重复（排除当前项目）
- 参数非空检查
- 异常捕获和处理

#### 删除项目时的验证
- 检查项目是否存在
- 参数非空检查
- 异常捕获和处理

## 全局异常处理

### GlobalExceptionHandler 功能

#### 1. 参数验证异常处理
- **MethodArgumentNotValidException**: 处理 `@Valid` 注解的验证错误
- **BindException**: 处理 `@Validated` 注解的验证错误
- **ConstraintViolationException**: 处理路径参数验证错误

#### 2. 业务异常处理
- **IllegalArgumentException**: 处理参数错误
- **RuntimeException**: 处理运行时异常
- **Exception**: 处理通用异常

#### 3. 错误响应格式
```json
{
  "success": false,
  "message": "参数验证失败: projectCode: 项目编号不能为空; managerPhone: 请输入正确的手机号码格式",
  "data": null
}
```

## 测试覆盖

### 验证测试用例 (ColdStorageProjectControllerValidationTest)

#### 1. 成功场景测试
- 有效数据创建项目

#### 2. 字段验证测试
- 项目编号为空/格式错误/长度错误
- 项目名称为空/长度错误
- 公司名称为空/长度错误
- 项目负责人为空/长度错误
- 联系电话为空/格式错误
- 项目状态为空/范围错误
- 项目备注长度超限

#### 3. 路径参数测试
- 空项目ID
- 空客户ID

#### 4. 边界值测试
- 最小长度验证
- 最大长度验证
- 数值范围验证

## 验证规则详细说明

### 项目编号验证规则
- **格式**: 只能包含字母、数字、下划线和横线
- **长度**: 2-50个字符
- **示例**: `PROJ001`, `project_2025`, `CS-001`

### 手机号验证规则
- **格式**: 中国大陆手机号格式
- **正则**: `^1[3-9]\d{9}$`
- **示例**: `13800138000`, `15912345678`

### 项目状态验证规则
- **范围**: 0-4
- **含义**: 
  - 0: 规划中
  - 1: 建设中
  - 2: 运营中
  - 3: 暂停
  - 4: 已完成

## 使用示例

### 1. 创建项目 (POST /api/coldstorage/project)
```json
{
  "projectCode": "PROJ001",
  "projectName": "测试项目",
  "companyName": "测试公司",
  "projectManager": "张三",
  "managerPhone": "13800138000",
  "projectStatus": 1,
  "projectRemark": "测试备注"
}
```

### 2. 验证失败响应
```json
{
  "success": false,
  "message": "参数验证失败: projectCode: 项目编号只能包含字母、数字、下划线和横线; managerPhone: 请输入正确的手机号码格式",
  "data": null
}
```

## 最佳实践

### 1. 验证注解使用原则
- 使用 `@NotBlank` 验证字符串非空
- 使用 `@NotNull` 验证对象非空
- 使用 `@Size` 验证长度范围
- 使用 `@Pattern` 验证格式
- 使用 `@Min/@Max` 验证数值范围

### 2. 错误消息设计
- 消息简洁明了
- 指出具体字段
- 提供修正建议

### 3. 异常处理策略
- 统一异常处理
- 详细错误日志
- 友好的错误响应

## 扩展建议

### 1. 自定义验证注解
可以考虑创建自定义验证注解：
- `@ProjectCode`: 项目编号格式验证
- `@PhoneNumber`: 手机号格式验证
- `@ProjectStatus`: 项目状态范围验证

### 2. 分组验证
可以为不同操作定义验证分组：
- `CreateGroup`: 创建时的验证
- `UpdateGroup`: 更新时的验证

### 3. 国际化支持
为错误消息添加国际化支持，支持多语言错误提示。

## 总结

通过完善的参数验证功能，确保了：
1. **数据安全性**: 防止恶意数据输入
2. **数据完整性**: 确保必要字段不为空
3. **数据格式正确性**: 验证数据格式和范围
4. **用户体验**: 提供清晰的错误提示
5. **系统稳定性**: 避免因数据错误导致的系统异常

这套验证机制为冷库项目管理模块提供了可靠的数据安全保障。 
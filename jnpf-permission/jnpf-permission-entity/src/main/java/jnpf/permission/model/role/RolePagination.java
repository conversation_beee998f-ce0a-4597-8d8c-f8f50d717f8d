package jnpf.permission.model.role;

import io.swagger.v3.oas.annotations.media.Schema;
import jnpf.base.Pagination;
import lombok.Data;

@Data
public class RolePagination extends Pagination {
    private String organizeId;
    @Schema(description = "状态")
    private Integer enabledMark;
    @Schema(description = "类型")
    private Integer type;
    /** 查询key */
    private String[] selectKey;
    /** 功能id */
    private String moduleId;

    /** 查询过滤：0-当前页面，1-全部数据 */
    private Integer dataType;
}

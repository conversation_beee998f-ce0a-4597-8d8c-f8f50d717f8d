<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="jnpf.mapper.TrainTestPaperLibraryMapper">
    <insert id="saveRecords">
        INSERT INTO make_train_exam_records
        (f_id, f_number,f_library_id, f_examiners, f_full_name, f_sort, f_mark, f_result_score, f_basic_score, f_question_flag,
         f_question_status, f_question_score, f_enabled_mark, f_sort_code, f_organize_id, f_creator_time,
         f_creator_user_id,f_question_num,f_per_question)
            VALUE (#{id}, #{fnumber},#{flibraryId} ,#{fexaminers}, #{ffullName}, #{fsort}, #{fmark}, #{fresultScore}, #{fbasicScore},
                   #{fquestionFlag},
                   #{fquestionStatus}, #{fquestionScore}, #{fenabledMark}, #{fsortCode}, #{forganizeId}, #{fcreatorTime},
                   #{fcreatorUserId},#{fquestionNum},#{fperQuestion})
    </insert>
</mapper>

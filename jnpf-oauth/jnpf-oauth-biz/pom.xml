<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>jnpf-oauth</artifactId>
        <groupId>com.jnpf</groupId>
        <version>5.2.0-RELEASE</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>jnpf-oauth-biz</artifactId>


    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jnpf</groupId>
            <artifactId>jnpf-oauth-entity</artifactId>
            <version>${project.version}</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.jnpf</groupId>
            <artifactId>jnpf-system-biz</artifactId>
            <version>${project.version}</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.jnpf</groupId>
            <artifactId>jnpf-common-all</artifactId>
            <version>${project.version}</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.jnpf</groupId>
            <artifactId>jnpf-exception</artifactId>
            <version>${project.version}</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.nimbusds</groupId>
            <artifactId>nimbus-jose-jwt</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-pool2</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jnpf</groupId>
            <artifactId>jnpf-message-biz</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.jnpf</groupId>
            <artifactId>jnpf-permission-controller</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.jnpf</groupId>
            <artifactId>jnpf-visualdev-portal-biz</artifactId>
            <version>${project.version}</version>
        </dependency>
        <!-- redis相关依赖包 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jnpf</groupId>
            <artifactId>jnpf-common-security</artifactId>
        </dependency>
    </dependencies>
</project>
package jnpf.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import jnpf.base.ActionResult;
import jnpf.base.vo.ListVO;
import jnpf.base.vo.PageListVO;
import jnpf.base.vo.PaginationVO;
import jnpf.model.DeviceBatchForm;
import jnpf.model.DeviceForm;
import jnpf.model.DeviceQueryForm;
import jnpf.model.DeviceVO;
import jnpf.service.DeviceService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 设备管理Controller测试类
 *
 * @版本： V5.2.0
 * @版权： 杭州多协信息技术有限公司（https://www.duoxieyun.com）
 * @作者： 多协开发组
 * @日期： 2025-01-27
 */
@ExtendWith(MockitoExtension.class)
class DeviceControllerTest {

    @Mock
    private DeviceService deviceService;

    @InjectMocks
    private DeviceController deviceController;

    private MockMvc mockMvc;
    private ObjectMapper objectMapper;

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.standaloneSetup(deviceController).build();
        objectMapper = new ObjectMapper();
    }

    /**
     * 测试获取设备列表（条件查询）
     */
    @Test
    void testGetList() throws Exception {
        // 准备测试数据
        List<DeviceVO> deviceList = new ArrayList<>();
        DeviceVO device = new DeviceVO();
        device.setId("device-id-1");
        device.setDeviceName("测试设备");
        device.setDeviceCode("DEV001");
        device.setDeviceStatus(1);
        deviceList.add(device);

        DeviceQueryForm queryForm = new DeviceQueryForm();
        queryForm.setCurrentPage(1);
        queryForm.setPageSize(10);
        queryForm.setDeviceName("测试");

        // Mock服务层方法
        when(deviceService.getList(any(DeviceQueryForm.class))).thenReturn(deviceList);
        when(deviceService.getCount(any(DeviceQueryForm.class))).thenReturn(1L);

        // 执行测试
        mockMvc.perform(post("/api/coldstorage/device/getList")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(queryForm)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.list").isArray())
                .andExpect(jsonPath("$.data.list[0].id").value("device-id-1"))
                .andExpect(jsonPath("$.data.list[0].deviceCode").value("DEV001"))
                .andExpect(jsonPath("$.data.pagination.total").value(1));

        // 验证服务层方法被调用
        verify(deviceService).getList(any(DeviceQueryForm.class));
        verify(deviceService).getCount(any(DeviceQueryForm.class));
    }

    /**
     * 测试获取设备列表（无条件）
     */
    @Test
    void testListAll() throws Exception {
        // 准备测试数据
        List<DeviceVO> deviceList = new ArrayList<>();
        DeviceVO device = new DeviceVO();
        device.setId("device-id-1");
        device.setDeviceName("测试设备");
        device.setDeviceCode("DEV001");
        deviceList.add(device);

        // Mock服务层方法
        when(deviceService.getList()).thenReturn(deviceList);

        // 执行测试
        mockMvc.perform(get("/api/coldstorage/device"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.list").isArray())
                .andExpect(jsonPath("$.data.list[0].id").value("device-id-1"));

        // 验证服务层方法被调用
        verify(deviceService).getList();
    }

    /**
     * 测试根据客户ID获取设备列表
     */
    @Test
    void testListByCustomerId() throws Exception {
        // 准备测试数据
        String customerId = "customer-id-1";
        List<DeviceVO> deviceList = new ArrayList<>();
        DeviceVO device = new DeviceVO();
        device.setId("device-id-1");
        device.setDeviceName("测试设备");
        device.setCustomerId(customerId);
        deviceList.add(device);

        // Mock服务层方法
        when(deviceService.getListByCustomerId(customerId)).thenReturn(deviceList);

        // 执行测试
        mockMvc.perform(get("/api/coldstorage/device/customer/{customerId}", customerId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.list").isArray())
                .andExpect(jsonPath("$.data.list[0].id").value("device-id-1"));

        // 验证服务层方法被调用
        verify(deviceService).getListByCustomerId(customerId);
    }

    /**
     * 测试根据仓库ID获取设备列表
     */
    @Test
    void testListByStorageId() throws Exception {
        // 准备测试数据
        String storageId = "storage-id-1";
        List<DeviceVO> deviceList = new ArrayList<>();
        DeviceVO device = new DeviceVO();
        device.setId("device-id-1");
        device.setDeviceName("测试设备");
        device.setStorageId(storageId);
        deviceList.add(device);

        // Mock服务层方法
        when(deviceService.getListByStorageId(storageId)).thenReturn(deviceList);

        // 执行测试
        mockMvc.perform(get("/api/coldstorage/device/storage/{storageId}", storageId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.list").isArray())
                .andExpect(jsonPath("$.data.list[0].id").value("device-id-1"));

        // 验证服务层方法被调用
        verify(deviceService).getListByStorageId(storageId);
    }

    /**
     * 测试获取设备信息
     */
    @Test
    void testGetInfo() throws Exception {
        // 准备测试数据
        String deviceId = "device-id-1";
        DeviceVO device = new DeviceVO();
        device.setId(deviceId);
        device.setDeviceName("测试设备");
        device.setDeviceCode("DEV001");

        // Mock服务层方法
        when(deviceService.getInfo(deviceId)).thenReturn(device);

        // 执行测试
        mockMvc.perform(get("/api/coldstorage/device/{id}", deviceId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.id").value(deviceId))
                .andExpect(jsonPath("$.data.deviceCode").value("DEV001"));

        // 验证服务层方法被调用
        verify(deviceService).getInfo(deviceId);
    }

    /**
     * 测试新增设备
     */
    @Test
    void testCreate() throws Exception {
        // 准备测试数据
        DeviceForm deviceForm = new DeviceForm();
        deviceForm.setDeviceName("测试设备");
        deviceForm.setDeviceCode("DEV001");
        deviceForm.setDeviceType(1);
        deviceForm.setCustomerId("customer-id-1");

        // Mock服务层方法 - void方法使用doNothing()
        doNothing().when(deviceService).create(any(DeviceForm.class));

        // 执行测试
        mockMvc.perform(post("/api/coldstorage/device")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(deviceForm)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));

        // 验证服务层方法被调用
        verify(deviceService).create(any(DeviceForm.class));
    }

    /**
     * 测试修改设备
     */
    @Test
    void testUpdate() throws Exception {
        // 准备测试数据
        String deviceId = "device-id-1";
        DeviceForm deviceForm = new DeviceForm();
        deviceForm.setDeviceName("测试设备修改");
        deviceForm.setDeviceCode("DEV001");
        deviceForm.setDeviceType(1);

        // Mock服务层方法 - void方法使用doNothing()
        doNothing().when(deviceService).update(eq(deviceId), any(DeviceForm.class));

        // 执行测试
        mockMvc.perform(put("/api/coldstorage/device/{id}", deviceId)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(deviceForm)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));

        // 验证服务层方法被调用
        verify(deviceService).update(deviceId, deviceForm);
    }

    /**
     * 测试删除设备
     */
    @Test
    void testDelete() throws Exception {
        // 准备测试数据
        String deviceId = "device-id-1";

        // Mock服务层方法 - void方法使用doNothing()
        doNothing().when(deviceService).delete(deviceId);

        // 执行测试
        mockMvc.perform(delete("/api/coldstorage/device/{id}", deviceId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));

        // 验证服务层方法被调用
        verify(deviceService).delete(deviceId);
    }

    /**
     * 测试批量新增设备
     */
    @Test
    void testBatchCreate() throws Exception {
        // 准备测试数据
        DeviceBatchForm batchForm = new DeviceBatchForm();
        // 注意：DeviceBatchForm的实际字段需要根据实际类定义调整

        // Mock服务层方法 - void方法使用doNothing()
        doNothing().when(deviceService).batchCreate(any(DeviceBatchForm.class));

        // 执行测试
        mockMvc.perform(post("/api/coldstorage/device/batch")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(batchForm)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));

        // 验证服务层方法被调用
        verify(deviceService).batchCreate(any(DeviceBatchForm.class));
    }

    /**
     * 测试更新设备状态
     */
    @Test
    void testUpdateDeviceStatus() throws Exception {
        // 准备测试数据
        String iotDeviceId = "iot-device-001";
        Integer status = 1;

        // Mock服务层方法 - void方法使用doNothing()
        doNothing().when(deviceService).updateDeviceStatus(iotDeviceId, status);

        // 执行测试
        mockMvc.perform(put("/api/coldstorage/device/status")
                .param("iotDeviceId", iotDeviceId)
                .param("status", status.toString()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));

        // 验证服务层方法被调用
        verify(deviceService).updateDeviceStatus(iotDeviceId, status);
    }

    /**
     * 测试根据客户统计设备数量
     */
    @Test
    void testCountByCustomerId() throws Exception {
        // 准备测试数据
        String customerId = "customer-id-1";
        Integer count = 10;

        // Mock服务层方法
        when(deviceService.countByCustomerId(customerId)).thenReturn(count);

        // 执行测试
        mockMvc.perform(get("/api/coldstorage/device/count/customer/{customerId}", customerId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data").value(10));

        // 验证服务层方法被调用
        verify(deviceService).countByCustomerId(customerId);
    }

    /**
     * 测试根据仓库统计设备数量
     */
    @Test
    void testCountByStorageId() throws Exception {
        // 准备测试数据
        String storageId = "storage-id-1";
        Integer count = 5;

        // Mock服务层方法
        when(deviceService.countByStorageId(storageId)).thenReturn(count);

        // 执行测试
        mockMvc.perform(get("/api/coldstorage/device/count/storage/{storageId}", storageId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data").value(5));

        // 验证服务层方法被调用
        verify(deviceService).countByStorageId(storageId);
    }
} 
package jnpf.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

/**
 * Controller测试基类
 * 提供通用的测试功能和配置
 *
 * @版本： V5.2.0
 * @版权： 杭州多协信息技术有限公司（https://www.duoxieyun.com）
 * @作者： 多协开发组
 * @日期： 2025-01-27
 */
@SpringBootTest
@AutoConfigureWebMvc
@ActiveProfiles("test")
public abstract class BaseControllerTest {

    @Autowired
    protected WebApplicationContext webApplicationContext;

    protected MockMvc mockMvc;
    protected ObjectMapper objectMapper;

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
        objectMapper = new ObjectMapper();
    }

    /**
     * 获取测试用户Token
     * @return 测试用户Token
     */
    protected String getTestToken() {
        return "test-token";
    }

    /**
     * 获取测试用户ID
     * @return 测试用户ID
     */
    protected String getTestUserId() {
        return "test-user-id";
    }

    /**
     * 获取测试客户ID
     * @return 测试客户ID
     */
    protected String getTestCustomerId() {
        return "test-customer-id";
    }

    /**
     * 获取测试项目ID
     * @return 测试项目ID
     */
    protected String getTestProjectId() {
        return "test-project-id";
    }

    /**
     * 获取测试冷库ID
     * @return 测试冷库ID
     */
    protected String getTestStorageId() {
        return "test-storage-id";
    }

    /**
     * 获取测试设备ID
     * @return 测试设备ID
     */
    protected String getTestDeviceId() {
        return "test-device-id";
    }
} 
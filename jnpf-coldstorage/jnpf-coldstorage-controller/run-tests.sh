#!/bin/bash

# 冷库储能Controller测试运行脚本
# 版本： V5.2.0
# 版权： 杭州多协信息技术有限公司（https://www.duoxieyun.com）
# 作者： 多协开发组
# 日期： 2025-01-27

echo "=========================================="
echo "冷库储能Controller测试开始运行"
echo "=========================================="

# 检查Maven是否安装
if ! command -v mvn &> /dev/null; then
    echo "错误：Maven未安装或未配置到PATH中"
    exit 1
fi

# 清理并编译项目
echo "正在清理和编译项目..."
mvn clean compile test-compile

if [ $? -ne 0 ]; then
    echo "错误：项目编译失败"
    exit 1
fi

# 运行所有测试
echo "正在运行所有测试..."
mvn test

if [ $? -eq 0 ]; then
    echo "=========================================="
    echo "✅ 所有测试通过！"
    echo "=========================================="
else
    echo "=========================================="
    echo "❌ 部分测试失败，请检查测试报告"
    echo "=========================================="
    exit 1
fi

# 生成测试报告
echo "正在生成测试报告..."
mvn surefire-report:report

echo "测试报告已生成，请查看 target/site/surefire-report.html"

echo "=========================================="
echo "测试运行完成"
echo "==========================================" 
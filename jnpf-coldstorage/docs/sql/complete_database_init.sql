-- ========================================
-- 冷库储能管理系统完整数据库初始化脚本
-- ========================================
-- 版本： V5.2.0
-- 作者： JNPF开发平台组
-- 日期： 2025-07-26
-- 说明： 集成所有模块的完整数据库初始化脚本
-- ========================================

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ========================================
-- 第一部分：基础表结构
-- ========================================

-- 1. 创建冷库储能项目表
DROP TABLE IF EXISTS `cs_project`;
CREATE TABLE `cs_project` (
	`F_ID` varchar(50) COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
	`F_PROJECT_CODE` varchar(50) COLLATE utf8mb4_general_ci NOT NULL COMMENT '项目编号',
	`F_PROJECT_NAME` varchar(200) COLLATE utf8mb4_general_ci NOT NULL COMMENT '项目名称',
	`F_COMPANY_NAME` varchar(200) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '公司名称',
	`F_PROJECT_MANAGER` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '项目负责人',
	`F_MANAGER_PHONE` varchar(20) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '项目负责人联系电话',
	`F_PROJECT_STATUS` int DEFAULT NULL COMMENT '项目状态（0-规划中 1-建设中 2-运营中 3-暂停 4-已完成）',
	`F_PROJECT_REMARK` text COLLATE utf8mb4_general_ci COMMENT '项目备注',
	`F_COMPANY_ID` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '所属客户企业ID',
	
	-- 继承的基础字段
	`f_sort_code` bigint DEFAULT 0 COMMENT '排序序号',
	`f_description` varchar(500) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '描述',
	`f_enabled_mark` int DEFAULT 1 COMMENT '有效标志(0-禁用，1-启用)',
	`f_creator_time` datetime DEFAULT NULL COMMENT '创建时间',
	`f_creator_user_id` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人',
	`f_last_modify_time` datetime DEFAULT NULL COMMENT '更新时间',
	`f_last_modify_user_id` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '更新人',
	`f_delete_mark` int DEFAULT 0 COMMENT '删除标记',
	`f_delete_time` datetime DEFAULT NULL COMMENT '删除时间',
	`f_delete_user_id` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '删除人',
	`f_tenant_id` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '租户ID',
	PRIMARY KEY (`F_ID`),
	UNIQUE KEY `uk_project_code` (`F_PROJECT_CODE`),
	KEY `idx_project_status` (`F_PROJECT_STATUS`),
	KEY `idx_company_name` (`F_COMPANY_NAME`),
	KEY `idx_company_id` (`F_COMPANY_ID`),
	KEY `idx_delete_mark` (`f_delete_mark`),
	KEY `idx_sort_code` (`f_sort_code`),
	KEY `idx_enabled_mark` (`f_enabled_mark`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='冷库储能项目表';

-- 2. 创建冷库表
DROP TABLE IF EXISTS `cs_cold_storage`;
CREATE TABLE `cs_cold_storage` (
	`F_ID` varchar(50) COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
	`F_STORAGE_CODE` varchar(50) COLLATE utf8mb4_general_ci NOT NULL COMMENT '冷库编号',
	`F_STORAGE_NAME` varchar(200) COLLATE utf8mb4_general_ci NOT NULL COMMENT '冷库名称',
	`F_PROJECT_ID` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '所属项目ID',
	`F_PROVINCE` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '冷库地址-省',
	`F_CITY` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '冷库地址-市',
	`F_DISTRICT` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '冷库地址-区',
	`F_DETAIL_ADDRESS` varchar(500) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '详细地址',
	`F_LOCATION` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '地理位置（经纬度）',
	`F_CONTACT_PERSON` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '联系人',
	`F_CONTACT_PHONE` varchar(20) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '联系电话',
	`F_STORAGE_TYPE` int DEFAULT NULL COMMENT '冷库类型（0-保鲜库 1-冷冻库 2-冷藏库 3-速冻库）',
	`F_CAPACITY` decimal(18,2) DEFAULT NULL COMMENT '冷库容量（立方米）',
	`F_MIN_TEMPERATURE` decimal(5,2) DEFAULT NULL COMMENT '冷库温度范围-最低温度',
	`F_MAX_TEMPERATURE` decimal(5,2) DEFAULT NULL COMMENT '冷库温度范围-最高温度',
	`F_HUMIDITY` decimal(5,2) DEFAULT NULL COMMENT '冷库湿度（%）',
	`F_STORAGE_STATUS` int DEFAULT NULL COMMENT '冷库状态（0-停用 1-正常运行 2-故障 3-维护中）',
	`F_STORAGE_REMARK` text COLLATE utf8mb4_general_ci COMMENT '冷库备注',
	
	-- 继承的基础字段
	`f_sort_code` bigint DEFAULT 0 COMMENT '排序序号',
	`f_description` varchar(500) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '描述',
	`f_enabled_mark` int DEFAULT 1 COMMENT '有效标志(0-禁用，1-启用)',
	`f_creator_time` datetime DEFAULT NULL COMMENT '创建时间',
	`f_creator_user_id` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人',
	`f_last_modify_time` datetime DEFAULT NULL COMMENT '更新时间',
	`f_last_modify_user_id` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '更新人',
	`f_delete_mark` int DEFAULT 0 COMMENT '删除标记',
	`f_delete_time` datetime DEFAULT NULL COMMENT '删除时间',
	`f_delete_user_id` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '删除人',
	`f_tenant_id` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '租户ID',
	PRIMARY KEY (`F_ID`),
	UNIQUE KEY `uk_storage_code` (`F_STORAGE_CODE`),
	KEY `idx_project_id` (`F_PROJECT_ID`),
	KEY `idx_storage_type` (`F_STORAGE_TYPE`),
	KEY `idx_storage_status` (`F_STORAGE_STATUS`),
	KEY `idx_delete_mark` (`f_delete_mark`),
	KEY `idx_sort_code` (`f_sort_code`),
	KEY `idx_enabled_mark` (`f_enabled_mark`),
	CONSTRAINT `fk_storage_project` FOREIGN KEY (`F_PROJECT_ID`) REFERENCES `cs_project` (`F_ID`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='冷库表';

-- ========================================
-- 第二部分：用户管理模块（重构后的结构）
-- ========================================

-- 3. 创建用户类型表
DROP TABLE IF EXISTS `cs_user_type`;
CREATE TABLE `cs_user_type` (
	`F_ID` varchar(50) COLLATE utf8mb4_general_ci PRIMARY KEY COMMENT '主键ID',
	`F_TYPE_CODE` varchar(50) COLLATE utf8mb4_general_ci NOT NULL COMMENT '类型编码',
	`F_TYPE_NAME` varchar(100) COLLATE utf8mb4_general_ci NOT NULL COMMENT '类型名称',
	

	
	-- 继承的基础字段
    `f_sort_code` bigint DEFAULT 0 COMMENT '排序序号',
    `f_description` varchar(500) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '类型描述',
    `f_enabled_mark` int DEFAULT 1 COMMENT '有效标志(0-禁用，1-启用)', 
	`f_creator_time` datetime DEFAULT NULL COMMENT '创建时间',
	`f_creator_user_id` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人',
	`f_last_modify_time` datetime DEFAULT NULL COMMENT '更新时间',
	`f_last_modify_user_id` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '更新人',
	`f_delete_mark` int DEFAULT 0 COMMENT '删除标记',
	`f_delete_time` datetime DEFAULT NULL COMMENT '删除时间',
	`f_delete_user_id` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '删除人',
    `f_tenant_id` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '租户ID',
	
	UNIQUE KEY `uk_type_code` (`F_TYPE_CODE`),
	KEY `idx_sort_code` (`f_sort_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='用户类型管理表';

-- 4. 创建小程序用户表（原客户表重构）
DROP TABLE IF EXISTS `cs_miniapp_user`;
CREATE TABLE `cs_miniapp_user` (
	`F_ID` varchar(50) COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
	`F_USER_CODE` varchar(50) COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户编号',
	`F_USER_NAME` varchar(100) COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户姓名',
	`F_MOBILE_PHONE` varchar(20) COLLATE utf8mb4_general_ci NOT NULL COMMENT '手机号（登录账号）',
	`F_PASSWORD` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '密码',
	`F_PASSWORD_SALT` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '密码盐值',
	`F_WECHAT_OPENID` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '微信OpenID',
	`F_WECHAT_UNIONID` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '微信UnionID',
	`F_AVATAR` varchar(500) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '头像',
	`F_USER_TYPE_ID` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '用户类型ID',
	`F_USER_STATUS` int DEFAULT '1' COMMENT '用户状态（0-禁用 1-启用）',
	`F_USER_REMARK` text COLLATE utf8mb4_general_ci COMMENT '用户备注',
	
	-- 继承的基础字段
	`f_sort_code` bigint DEFAULT 0 COMMENT '排序序号',
	`f_description` varchar(500) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '描述',
	`f_enabled_mark` int DEFAULT 1 COMMENT '有效标志(0-禁用，1-启用)',
	`f_creator_time` datetime DEFAULT NULL COMMENT '创建时间',
	`f_creator_user_id` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人',
	`f_last_modify_time` datetime DEFAULT NULL COMMENT '更新时间',
	`f_last_modify_user_id` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '更新人',
	`f_delete_mark` int DEFAULT 0 COMMENT '删除标记',
	`f_delete_time` datetime DEFAULT NULL COMMENT '删除时间',
	`f_delete_user_id` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '删除人',
	`f_tenant_id` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '租户ID',
	PRIMARY KEY (`F_ID`),
	UNIQUE KEY `uk_user_code` (`F_USER_CODE`),
	UNIQUE KEY `uk_mobile_phone` (`F_MOBILE_PHONE`),
	UNIQUE KEY `uk_wechat_openid` (`F_WECHAT_OPENID`),
	UNIQUE KEY `uk_wechat_unionid` (`F_WECHAT_UNIONID`),
	KEY `idx_user_type_id` (`F_USER_TYPE_ID`),
	KEY `idx_user_status` (`F_USER_STATUS`),
	KEY `idx_delete_mark` (`f_delete_mark`),
	KEY `idx_sort_code` (`f_sort_code`),
	KEY `idx_enabled_mark` (`f_enabled_mark`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='小程序用户表';

-- 5. 创建客户企业表
DROP TABLE IF EXISTS `cs_customer_company`;
CREATE TABLE `cs_customer_company` (
	`F_ID` varchar(50) COLLATE utf8mb4_general_ci PRIMARY KEY COMMENT '主键ID',
	`F_COMPANY_CODE` varchar(50) COLLATE utf8mb4_general_ci NOT NULL COMMENT '客户编号',
	`F_COMPANY_NAME` varchar(200) COLLATE utf8mb4_general_ci NOT NULL COMMENT '客户企业名称',
	`F_CONTACT_PERSON` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '联系人',
	`F_CONTACT_PHONE` varchar(20) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '联系电话',
	`F_COMPANY_ADDRESS` varchar(500) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '企业地址',
	`F_COMPANY_STATUS` int DEFAULT 1 COMMENT '客户状态（0-禁用 1-启用）',
	`F_COMPANY_REMARK` text COLLATE utf8mb4_general_ci COMMENT '客户备注',
	
	-- 继承的基础字段
	`f_sort_code` bigint DEFAULT 0 COMMENT '排序序号',
	`f_description` varchar(500) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '描述',
	`f_enabled_mark` int DEFAULT 1 COMMENT '有效标志(0-禁用，1-启用)',
	`f_creator_time` datetime DEFAULT NULL COMMENT '创建时间',
	`f_creator_user_id` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人',
	`f_last_modify_time` datetime DEFAULT NULL COMMENT '更新时间',
	`f_last_modify_user_id` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '更新人',
	`f_delete_mark` int DEFAULT 0 COMMENT '删除标记',
	`f_delete_time` datetime DEFAULT NULL COMMENT '删除时间',
	`f_delete_user_id` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '删除人',
	`f_tenant_id` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '租户ID',
	
	UNIQUE KEY `uk_company_code` (`F_COMPANY_CODE`),
	KEY `idx_company_status` (`F_COMPANY_STATUS`),
	KEY `idx_delete_mark` (`f_delete_mark`),
	KEY `idx_sort_code` (`f_sort_code`),
	KEY `idx_enabled_mark` (`f_enabled_mark`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='客户企业信息表';

-- 6. 创建用户冷库关联表
DROP TABLE IF EXISTS `cs_user_storage_relation`;
CREATE TABLE `cs_user_storage_relation` (
	`F_ID` varchar(50) COLLATE utf8mb4_general_ci PRIMARY KEY COMMENT '主键ID',
	`F_USER_ID` varchar(50) COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户ID',
	`F_STORAGE_ID` varchar(50) COLLATE utf8mb4_general_ci NOT NULL COMMENT '冷库ID',
	`F_PERMISSION_TYPE` int DEFAULT 0 COMMENT '权限类型（0-只读 1-管理）',
	`F_RELATION_STATUS` int DEFAULT 1 COMMENT '关联状态（0-禁用 1-启用）',
	`F_RELATION_REMARK` text COLLATE utf8mb4_general_ci COMMENT '关联备注',
	
	-- 继承的基础字段
	`f_sort_code` bigint DEFAULT 0 COMMENT '排序序号',
	`f_description` varchar(500) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '描述',
	`f_enabled_mark` int DEFAULT 1 COMMENT '有效标志(0-禁用，1-启用)',
	`f_creator_time` datetime DEFAULT NULL COMMENT '创建时间',
	`f_creator_user_id` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人',
	`f_last_modify_time` datetime DEFAULT NULL COMMENT '更新时间',
	`f_last_modify_user_id` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '更新人',
	`f_delete_mark` int DEFAULT 0 COMMENT '删除标记',
	`f_delete_time` datetime DEFAULT NULL COMMENT '删除时间',
	`f_delete_user_id` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '删除人',
	`f_tenant_id` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '租户ID',
	
	UNIQUE KEY `uk_user_storage` (`F_USER_ID`, `F_STORAGE_ID`),
	KEY `idx_user_id` (`F_USER_ID`),
	KEY `idx_storage_id` (`F_STORAGE_ID`),
	KEY `idx_relation_status` (`F_RELATION_STATUS`),
	KEY `idx_delete_mark` (`f_delete_mark`),
	KEY `idx_sort_code` (`f_sort_code`),
	KEY `idx_enabled_mark` (`f_enabled_mark`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='用户冷库关联表';

-- 7. 创建用户客户企业关联表
DROP TABLE IF EXISTS `cs_user_company_relation`;
CREATE TABLE `cs_user_company_relation` (
	`F_ID` varchar(50) COLLATE utf8mb4_general_ci PRIMARY KEY COMMENT '主键ID',
	`F_USER_ID` varchar(50) COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户ID',
	`F_COMPANY_ID` varchar(50) COLLATE utf8mb4_general_ci NOT NULL COMMENT '客户企业ID',
	`F_RELATION_TYPE` int DEFAULT 1 COMMENT '关联类型（1-员工 2-管理员）',
	`F_RELATION_STATUS` int DEFAULT 1 COMMENT '关联状态（0-禁用 1-启用）',
	`F_RELATION_REMARK` text COLLATE utf8mb4_general_ci COMMENT '关联备注',
	
	-- 继承的基础字段
	`f_sort_code` bigint DEFAULT 0 COMMENT '排序序号',
	`f_description` varchar(500) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '描述',
	`f_enabled_mark` int DEFAULT 1 COMMENT '有效标志(0-禁用，1-启用)',
	`f_creator_time` datetime DEFAULT NULL COMMENT '创建时间',
	`f_creator_user_id` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人',
	`f_last_modify_time` datetime DEFAULT NULL COMMENT '更新时间',
	`f_last_modify_user_id` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '更新人',
	`f_delete_mark` int DEFAULT 0 COMMENT '删除标记',
	`f_delete_time` datetime DEFAULT NULL COMMENT '删除时间',
	`f_delete_user_id` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '删除人',
	`f_tenant_id` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '租户ID',
	
	UNIQUE KEY `uk_user_company` (`F_USER_ID`, `F_COMPANY_ID`),
	KEY `idx_user_id` (`F_USER_ID`),
	KEY `idx_company_id` (`F_COMPANY_ID`),
	KEY `idx_relation_type` (`F_RELATION_TYPE`),
	KEY `idx_relation_status` (`F_RELATION_STATUS`),
	KEY `idx_delete_mark` (`f_delete_mark`),
	KEY `idx_sort_code` (`f_sort_code`),
	KEY `idx_enabled_mark` (`f_enabled_mark`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='用户客户企业关联表';

-- ========================================
-- 第三部分：设备管理模块
-- ========================================

-- 8. 创建设备表
DROP TABLE IF EXISTS `cs_device`;
CREATE TABLE `cs_device` (
  `F_ID` varchar(50) COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
  `F_DEVICE_CODE` varchar(50) COLLATE utf8mb4_general_ci NOT NULL COMMENT '设备编号',
  `F_DEVICE_NAME` varchar(200) COLLATE utf8mb4_general_ci NOT NULL COMMENT '设备名称',
  `F_DEVICE_TYPE` int DEFAULT NULL COMMENT '设备类型（1-温度传感器 2-湿度传感器 3-制冷机组 4-监控摄像头 5-门禁传感器 6-报警设备 7-照明系统 8-通风设备 9-电力计量设备 99-其他设备）',
  `F_CUSTOMER_ID` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '所属客户ID',
  `F_CUSTOMER_NAME` varchar(200) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '所属客户名称（冗余字段，便于查询）',
  `F_STORAGE_ID` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '所属仓库ID',
  `F_STORAGE_NAME` varchar(200) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '所属仓库名称（冗余字段，便于查询）',
  `F_DEVICE_STATUS` int DEFAULT NULL COMMENT '设备状态（0-离线 1-在线）',
  `F_LAST_ONLINE_TIME` datetime DEFAULT NULL COMMENT '最后在线时间',
  `F_DEVICE_LOCATION` varchar(500) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '设备位置描述',
  `F_GEOGRAPHIC_LOCATION` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '设备地理位置（经纬度）',
  `F_IOT_DEVICE_ID` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'IoT平台设备ID',
  `F_SERIAL_NUMBER` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '设备序列号',
  `F_DEVICE_MODEL` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '设备型号',
  `F_MANUFACTURER` varchar(200) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '设备制造商',
  `F_INSTALL_DATE` datetime DEFAULT NULL COMMENT '安装日期',
  `F_WARRANTY_UNTIL` datetime DEFAULT NULL COMMENT '保修期至',
  `F_IP_ADDRESS` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '设备IP地址',
  `F_MAC_ADDRESS` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '设备MAC地址',
  `F_FIRMWARE_VERSION` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '设备版本号',
  `F_DEVICE_REMARK` text COLLATE utf8mb4_general_ci COMMENT '设备备注',
  
  -- 继承的基础字段
  `f_sort_code` bigint DEFAULT 0 COMMENT '排序序号',
  `f_description` varchar(500) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '描述',
  `f_enabled_mark` int DEFAULT 1 COMMENT '有效标志(0-禁用，1-启用)',
  `f_creator_time` datetime DEFAULT NULL COMMENT '创建时间',
  `f_creator_user_id` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人',
  `f_last_modify_time` datetime DEFAULT NULL COMMENT '更新时间',
  `f_last_modify_user_id` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '更新人',
  `f_delete_mark` int DEFAULT 0 COMMENT '删除标记',
  `f_delete_time` datetime DEFAULT NULL COMMENT '删除时间',
  `f_delete_user_id` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '删除人',
  `f_tenant_id` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '租户ID',
  PRIMARY KEY (`F_ID`),
  UNIQUE KEY `uk_device_code` (`F_DEVICE_CODE`),
  UNIQUE KEY `uk_iot_device_id` (`F_IOT_DEVICE_ID`),
  KEY `idx_customer_id` (`F_CUSTOMER_ID`),
  KEY `idx_storage_id` (`F_STORAGE_ID`),
  KEY `idx_device_type` (`F_DEVICE_TYPE`),
  KEY `idx_device_status` (`F_DEVICE_STATUS`),
  KEY `idx_delete_mark` (`f_delete_mark`),
  KEY `idx_sort_code` (`f_sort_code`),
  KEY `idx_enabled_mark` (`f_enabled_mark`),
  KEY `idx_last_online_time` (`F_LAST_ONLINE_TIME`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='设备信息表';

-- ========================================
-- 第四部分：电价配置模块
-- ========================================

-- 9. 创建电价配置主表
DROP TABLE IF EXISTS `cs_electricity_price_config`;
CREATE TABLE `cs_electricity_price_config` (
    `F_ID` varchar(50) COLLATE utf8mb4_general_ci PRIMARY KEY COMMENT '主键ID',
    `F_CONFIG_NAME` varchar(100) COLLATE utf8mb4_general_ci NOT NULL COMMENT '配置名称',
    `F_COMPANY_ID` varchar(50) COLLATE utf8mb4_general_ci NOT NULL COMMENT '客户企业ID',
    `F_TIME_PERIOD_ENABLED` int DEFAULT 0 COMMENT '是否启用时段条件（0-关闭 1-开启）',
    `F_CONFIG_STATUS` int DEFAULT 1 COMMENT '配置状态（0-禁用 1-启用）',
    `F_CONFIG_REMARK` text COLLATE utf8mb4_general_ci COMMENT '配置备注',
    
    -- 继承的基础字段
    `f_sort_code` bigint DEFAULT 0 COMMENT '排序序号',
    `f_description` varchar(500) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '描述',
    `f_enabled_mark` int DEFAULT 1 COMMENT '有效标志(0-禁用，1-启用)',
    `f_creator_time` datetime DEFAULT NULL COMMENT '创建时间',
    `f_creator_user_id` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人',
    `f_last_modify_time` datetime DEFAULT NULL COMMENT '更新时间',
    `f_last_modify_user_id` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '更新人',
    `f_delete_mark` int DEFAULT 0 COMMENT '删除标记',
    `f_delete_time` datetime DEFAULT NULL COMMENT '删除时间',
    `f_delete_user_id` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '删除人',
    `f_tenant_id` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '租户ID',
    
    KEY `idx_company_id` (`F_COMPANY_ID`),
    KEY `idx_config_name` (`F_CONFIG_NAME`),
    KEY `idx_config_status` (`F_CONFIG_STATUS`),
    KEY `idx_config_company_status` (`F_COMPANY_ID`, `F_CONFIG_STATUS`),
    KEY `idx_delete_mark` (`f_delete_mark`),
    KEY `idx_sort_code` (`f_sort_code`),
    KEY `idx_enabled_mark` (`f_enabled_mark`),
    UNIQUE KEY `uk_config_name_company` (`F_CONFIG_NAME`, `F_COMPANY_ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='电价配置主表';

-- 10. 创建电价时间段配置表
DROP TABLE IF EXISTS `cs_electricity_time_period`;
CREATE TABLE `cs_electricity_time_period` (
    `F_ID` varchar(50) COLLATE utf8mb4_general_ci PRIMARY KEY COMMENT '主键ID',
    `F_CONFIG_ID` varchar(50) COLLATE utf8mb4_general_ci NOT NULL COMMENT '电价配置ID',
    `F_PERIOD_NAME` varchar(50) COLLATE utf8mb4_general_ci NOT NULL COMMENT '时段名称',
    `F_START_HOUR` int NOT NULL COMMENT '开始小时',
    `F_START_MINUTE` int NOT NULL COMMENT '开始分钟',
    `F_END_HOUR` int NOT NULL COMMENT '结束小时',
    `F_END_MINUTE` int NOT NULL COMMENT '结束分钟',
    `F_ELECTRICITY_PRICE` decimal(10,4) NOT NULL COMMENT '电价（元/度）',
    `F_PERIOD_STATUS` int DEFAULT 1 COMMENT '时段状态（0-禁用 1-启用）',
    `F_PERIOD_REMARK` text COLLATE utf8mb4_general_ci COMMENT '时段备注',
    
    -- 继承的基础字段
    `f_sort_code` bigint DEFAULT 0 COMMENT '排序序号',
    `f_description` varchar(500) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '描述',
    `f_enabled_mark` int DEFAULT 1 COMMENT '有效标志(0-禁用，1-启用)',
    `f_creator_time` datetime DEFAULT NULL COMMENT '创建时间',
    `f_creator_user_id` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人',
    `f_last_modify_time` datetime DEFAULT NULL COMMENT '更新时间',
    `f_last_modify_user_id` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '更新人',
    `f_delete_mark` int DEFAULT 0 COMMENT '删除标记',
    `f_delete_time` datetime DEFAULT NULL COMMENT '删除时间',
    `f_delete_user_id` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '删除人',
    `f_tenant_id` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '租户ID',
    
    KEY `idx_config_id` (`F_CONFIG_ID`),
    KEY `idx_time_range` (`F_START_HOUR`, `F_START_MINUTE`, `F_END_HOUR`, `F_END_MINUTE`),
    KEY `idx_period_config_status` (`F_CONFIG_ID`, `F_PERIOD_STATUS`),
    KEY `idx_delete_mark` (`f_delete_mark`),
    KEY `idx_sort_code` (`f_sort_code`),
    KEY `idx_enabled_mark` (`f_enabled_mark`),
    
    CONSTRAINT `ck_hour_range` CHECK ((`F_START_HOUR` >= 0) AND (`F_START_HOUR` <= 23) AND (`F_END_HOUR` >= 0) AND (`F_END_HOUR` <= 23)),
    CONSTRAINT `ck_minute_range` CHECK ((`F_START_MINUTE` >= 0) AND (`F_START_MINUTE` <= 59) AND (`F_END_MINUTE` >= 0) AND (`F_END_MINUTE` <= 59)),
    CONSTRAINT `ck_price_positive` CHECK ((`F_ELECTRICITY_PRICE` > 0)),
    CONSTRAINT `fk_time_period_config` FOREIGN KEY (`F_CONFIG_ID`) REFERENCES `cs_electricity_price_config` (`F_ID`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='电价时间段配置表';

-- ========================================
-- 第五部分：故障管理模块
-- ========================================

-- 11. 创建故障类型字典表
DROP TABLE IF EXISTS `cs_fault_type_dict`;
CREATE TABLE `cs_fault_type_dict` (
    `F_ID` varchar(50) COLLATE utf8mb4_general_ci PRIMARY KEY COMMENT '主键ID',
    `F_TYPE_CODE` varchar(50) COLLATE utf8mb4_general_ci NOT NULL COMMENT '类型编码',
    `F_TYPE_NAME` varchar(100) COLLATE utf8mb4_general_ci NOT NULL COMMENT '类型名称',
    `F_TYPE_DESCRIPTION` varchar(500) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '类型描述',
    
    -- 继承的基础字段
    `f_sort_code` bigint DEFAULT 0 COMMENT '排序序号',
    `f_description` varchar(500) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '描述',
    `f_enabled_mark` int DEFAULT 1 COMMENT '有效标志(0-禁用，1-启用)',
    `f_creator_time` datetime DEFAULT NULL COMMENT '创建时间',
    `f_creator_user_id` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人',
    `f_last_modify_time` datetime DEFAULT NULL COMMENT '更新时间',
    `f_last_modify_user_id` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '更新人',
    `f_delete_mark` int DEFAULT 0 COMMENT '删除标记',
    `f_delete_time` datetime DEFAULT NULL COMMENT '删除时间',
    `f_delete_user_id` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '删除人',
    `f_tenant_id` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '租户ID',
    
    UNIQUE KEY `uk_type_code` (`F_TYPE_CODE`),
    KEY `idx_delete_mark` (`f_delete_mark`),
    KEY `idx_sort_code` (`f_sort_code`),
    KEY `idx_enabled_mark` (`f_enabled_mark`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='故障类型字典表';

-- 12. 创建故障管理主表
DROP TABLE IF EXISTS `cs_fault_management`;
CREATE TABLE `cs_fault_management` (
    `F_ID` varchar(50) COLLATE utf8mb4_general_ci PRIMARY KEY COMMENT '主键ID',
    `F_DEVICE_CODE` varchar(100) COLLATE utf8mb4_general_ci NOT NULL COMMENT '设备编号',
    `F_DEVICE_NAME` varchar(200) COLLATE utf8mb4_general_ci NOT NULL COMMENT '设备名称',
    `F_FAULT_TYPE` varchar(100) COLLATE utf8mb4_general_ci NOT NULL COMMENT '故障类型',
    `F_COMPANY_ID` varchar(50) COLLATE utf8mb4_general_ci NOT NULL COMMENT '所属客户企业ID',
    `F_STORAGE_ID` varchar(50) COLLATE utf8mb4_general_ci NOT NULL COMMENT '所属冷库ID',
    `F_FAULT_DESCRIPTION` text COLLATE utf8mb4_general_ci NOT NULL COMMENT '故障说明',
    `F_PROCESS_STATUS` int DEFAULT 0 COMMENT '处理状态（0-未处理 1-处理中 2-完成处理）',
    `F_PROCESSOR_ID` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '处理人ID',
    `F_PROCESSOR_NAME` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '处理人姓名',
    `F_PROCESS_TIME` datetime DEFAULT NULL COMMENT '处理时间',
    `F_PROCESS_RESULT` text COLLATE utf8mb4_general_ci COMMENT '处理结果',
    `F_COMPLETE_TIME` datetime DEFAULT NULL COMMENT '完成时间',
    `F_FAULT_LEVEL` int DEFAULT 2 COMMENT '故障等级（1-低 2-中 3-高 4-紧急）',
    `F_REPORTER_ID` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '上报人ID',
    `F_REPORTER_NAME` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '上报人姓名',
    `F_FAULT_IMAGES` text COLLATE utf8mb4_general_ci COMMENT '故障图片附件',
    `F_FAULT_REMARK` text COLLATE utf8mb4_general_ci COMMENT '故障备注',
    
    -- 继承的基础字段
    `f_sort_code` bigint DEFAULT 0 COMMENT '排序序号',
    `f_description` varchar(500) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '描述',
    `f_enabled_mark` int DEFAULT 1 COMMENT '有效标志(0-禁用，1-启用)',
    `f_creator_time` datetime DEFAULT NULL COMMENT '创建时间',
    `f_creator_user_id` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人',
    `f_last_modify_time` datetime DEFAULT NULL COMMENT '更新时间',
    `f_last_modify_user_id` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '更新人',
    `f_delete_mark` int DEFAULT 0 COMMENT '删除标记',
    `f_delete_time` datetime DEFAULT NULL COMMENT '删除时间',
    `f_delete_user_id` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '删除人',
    `f_tenant_id` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '租户ID',
    
    KEY `idx_device_code` (`F_DEVICE_CODE`),
    KEY `idx_device_name` (`F_DEVICE_NAME`),
    KEY `idx_fault_type` (`F_FAULT_TYPE`),
    KEY `idx_company_id` (`F_COMPANY_ID`),
    KEY `idx_storage_id` (`F_STORAGE_ID`),
    KEY `idx_process_status` (`F_PROCESS_STATUS`),
    KEY `idx_fault_level` (`F_FAULT_LEVEL`),
    KEY `idx_reporter_id` (`F_REPORTER_ID`),
    KEY `idx_processor_id` (`F_PROCESSOR_ID`),
    KEY `idx_create_time` (`f_creator_time`),
    KEY `idx_process_time` (`F_PROCESS_TIME`),
    KEY `idx_complete_time` (`F_COMPLETE_TIME`),
    KEY `idx_company_status` (`F_COMPANY_ID`, `F_PROCESS_STATUS`),
    KEY `idx_storage_status` (`F_STORAGE_ID`, `F_PROCESS_STATUS`),
    KEY `idx_type_level` (`F_FAULT_TYPE`, `F_FAULT_LEVEL`),
    KEY `idx_status_time` (`F_PROCESS_STATUS`, `f_creator_time`),
    KEY `idx_delete_mark` (`f_delete_mark`),
    KEY `idx_sort_code` (`f_sort_code`),
    KEY `idx_enabled_mark` (`f_enabled_mark`),
    
    CONSTRAINT `ck_process_status` CHECK ((`F_PROCESS_STATUS` IN (0,1,2))),
    CONSTRAINT `ck_fault_level` CHECK ((`F_FAULT_LEVEL` IN (1,2,3,4)))
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='故障管理表';

-- ========================================
-- 第六部分：视图创建
-- ========================================

-- 项目冷库统计视图
CREATE OR REPLACE VIEW `v_project_storage_stats` AS
SELECT 
    p.`F_ID` AS project_id,
    p.`F_PROJECT_CODE` AS project_code,
    p.`F_PROJECT_NAME` AS project_name,
    p.`F_COMPANY_NAME` AS company_name,
    p.`F_PROJECT_STATUS` AS project_status,
    COUNT(s.`F_ID`) AS storage_count,
    SUM(s.`F_CAPACITY`) AS total_capacity,
    SUM(CASE WHEN s.`F_STORAGE_STATUS` = 1 THEN 1 ELSE 0 END) AS running_storage_count,
    SUM(CASE WHEN s.`F_STORAGE_STATUS` = 1 THEN s.`F_CAPACITY` ELSE 0 END) AS running_capacity
FROM `cs_project` p
LEFT JOIN `cs_cold_storage` s ON p.`F_ID` = s.`F_PROJECT_ID` AND s.`f_delete_mark` = 0
WHERE p.`f_delete_mark` = 0
GROUP BY p.`F_ID`, p.`F_PROJECT_CODE`, p.`F_PROJECT_NAME`, p.`F_COMPANY_NAME`, p.`F_PROJECT_STATUS`;

-- 用户冷库权限视图
CREATE OR REPLACE VIEW `v_user_storage_access` AS
SELECT 
    u.`F_ID` AS user_id,
    u.`F_USER_CODE` AS user_code,
    u.`F_USER_NAME` AS user_name,
    u.`F_MOBILE_PHONE` AS mobile_phone,
    s.`F_ID` AS storage_id,
    s.`F_STORAGE_CODE` AS storage_code,
    s.`F_STORAGE_NAME` AS storage_name,
    r.`F_PERMISSION_TYPE` AS permission_type,
    r.`F_RELATION_STATUS` AS relation_status
FROM `cs_miniapp_user` u
INNER JOIN `cs_user_storage_relation` r ON u.`F_ID` = r.`F_USER_ID` AND r.`f_delete_mark` = 0
INNER JOIN `cs_cold_storage` s ON r.`F_STORAGE_ID` = s.`F_ID` AND s.`f_delete_mark` = 0
WHERE u.`f_delete_mark` = 0 AND u.`F_USER_STATUS` = 1;

-- 故障管理视图
CREATE OR REPLACE VIEW `v_fault_management` AS
SELECT 
    fm.`F_ID`,
    fm.`F_DEVICE_CODE`,
    fm.`F_DEVICE_NAME`,
    fm.`F_FAULT_TYPE`,
    fm.`F_COMPANY_ID`,
    cc.`F_COMPANY_NAME`,
    fm.`F_STORAGE_ID`,
    cs.`F_STORAGE_NAME`,
    fm.`F_FAULT_DESCRIPTION`,
    fm.`F_PROCESS_STATUS`,
    CASE fm.`F_PROCESS_STATUS` 
        WHEN 0 THEN '未处理'
        WHEN 1 THEN '处理中'
        WHEN 2 THEN '完成处理'
        ELSE '未知'
    END AS `F_PROCESS_STATUS_TEXT`,
    fm.`F_PROCESSOR_ID`,
    fm.`F_PROCESSOR_NAME`,
    fm.`F_PROCESS_TIME`,
    fm.`F_PROCESS_RESULT`,
    fm.`F_COMPLETE_TIME`,
    fm.`F_FAULT_LEVEL`,
    CASE fm.`F_FAULT_LEVEL` 
        WHEN 1 THEN '低'
        WHEN 2 THEN '中'
        WHEN 3 THEN '高'
        WHEN 4 THEN '紧急'
        ELSE '未知'
    END AS `F_FAULT_LEVEL_TEXT`,
    fm.`F_REPORTER_ID`,
    fm.`F_REPORTER_NAME`,
    fm.`F_FAULT_IMAGES`,
    fm.`F_FAULT_REMARK`,
    fm.`f_creator_time`,
    fm.`f_last_modify_time`,
    -- 计算处理耗时（分钟）
    CASE 
        WHEN fm.`F_COMPLETE_TIME` IS NOT NULL AND fm.`F_PROCESS_TIME` IS NOT NULL 
        THEN TIMESTAMPDIFF(MINUTE, fm.`F_PROCESS_TIME`, fm.`F_COMPLETE_TIME`)
        ELSE NULL
    END AS `F_PROCESS_DURATION`
FROM `cs_fault_management` fm
LEFT JOIN `cs_customer_company` cc ON fm.`F_COMPANY_ID` = cc.`F_ID`
LEFT JOIN `cs_cold_storage` cs ON fm.`F_STORAGE_ID` = cs.`F_ID`
WHERE fm.`f_delete_mark` = 0;

SET FOREIGN_KEY_CHECKS = 1;
#!/bin/bash

# 冷库项目参数验证测试运行脚本
# 解决commons-logging冲突和测试配置问题

echo "=========================================="
echo "冷库项目参数验证测试开始运行"
echo "=========================================="

# 检查Maven是否安装
if ! command -v mvn &> /dev/null; then
    echo "错误：Maven未安装或未配置到PATH中"
    exit 1
fi

# 清理项目
echo "正在清理项目..."
mvn clean

# 编译项目
echo "正在编译项目..."
mvn compile test-compile

if [ $? -ne 0 ]; then
    echo "错误：项目编译失败"
    exit 1
fi

# 运行验证测试
echo "正在运行参数验证测试..."
mvn test -Dtest=SimpleValidationTest

if [ $? -eq 0 ]; then
    echo "=========================================="
    echo "✅ 参数验证测试通过！"
    echo "=========================================="
else
    echo "=========================================="
    echo "❌ 参数验证测试失败，请检查测试报告"
    echo "=========================================="
    exit 1
fi

# 运行所有测试
echo "正在运行所有测试..."
mvn test

if [ $? -eq 0 ]; then
    echo "=========================================="
    echo "✅ 所有测试通过！"
    echo "=========================================="
else
    echo "=========================================="
    echo "❌ 部分测试失败，请检查测试报告"
    echo "=========================================="
    exit 1
fi

# 生成测试报告
echo "正在生成测试报告..."
mvn surefire-report:report

echo "测试报告已生成，请查看 target/site/surefire-report.html"

echo "=========================================="
echo "测试运行完成"
echo "=========================================="

# 显示测试结果摘要
echo "测试结果摘要："
echo "- 参数验证功能：✅ 正常工作"
echo "- Commons-logging冲突：✅ 已解决"
echo "- 测试配置问题：✅ 已修复"
echo "- 全局异常处理：✅ 已配置" 
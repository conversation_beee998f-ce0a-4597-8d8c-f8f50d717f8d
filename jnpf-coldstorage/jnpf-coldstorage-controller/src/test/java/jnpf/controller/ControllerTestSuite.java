package jnpf.controller;

import org.junit.platform.suite.api.SelectClasses;
import org.junit.platform.suite.api.Suite;

/**
 * Controller测试套件
 * 用于运行所有Controller测试类
 *
 * @版本： V5.2.0
 * @版权： 杭州多协信息技术有限公司（https://www.duoxieyun.com）
 * @作者： 多协开发组
 * @日期： 2025-01-27
 */
@Suite
@SelectClasses({
    ColdStorageCustomerControllerTest.class,
    DeviceControllerTest.class,
    ColdStorageControllerTest.class,
    MiniAppAuthControllerTest.class
})
public class ControllerTestSuite {
    // 测试套件配置
} 
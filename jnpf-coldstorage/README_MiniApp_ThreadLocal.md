# 微信小程序ThreadLocal用户信息管理机制

## 概述

本系统为微信小程序认证提供了基于ThreadLocal的用户信息管理机制，允许在整个请求处理过程中方便地获取当前用户信息，无需层层传递参数。

## 架构组件

### 1. MiniAppUserProvider
- **位置**: `jnpf-coldstorage-biz/src/main/java/jnpf/util/MiniAppUserProvider.java`
- **功能**: 管理ThreadLocal中的用户信息、Token和客户端IP
- **核心方法**:
  - `setCurrentUser(customer)` - 设置当前用户
  - `getCurrentUser()` - 获取当前用户完整信息
  - `getCurrentUserId()` - 获取当前用户ID
  - `isLoggedIn()` - 检查是否已登录
  - `clearAll()` - 清理所有ThreadLocal缓存

### 2. MiniAppAuthInterceptor
- **位置**: `jnpf-coldstorage-biz/src/main/java/jnpf/interceptor/MiniAppAuthInterceptor.java`
- **功能**: 拦截微信小程序请求，验证Token并设置用户信息到ThreadLocal
- **处理流程**:
  1. 获取Mini-App-Token请求头
  2. 验证Token有效性
  3. 获取用户信息并设置到ThreadLocal
  4. 请求完成后清理ThreadLocal

### 3. MiniAppWebConfig
- **位置**: `jnpf-coldstorage-biz/src/main/java/jnpf/config/MiniAppWebConfig.java`
- **功能**: 注册拦截器，配置拦截路径
- **拦截路径**: `/api/coldstorage/**`
- **排除路径**: 登录、注册等无需认证的接口

### 4. MiniAppAuthServiceImpl
- **位置**: `jnpf-coldstorage-biz/src/main/java/jnpf/service/impl/MiniAppAuthServiceImpl.java`
- **功能**: 登录认证服务，在登录成功后设置用户信息到ThreadLocal

## 使用示例

### 基本用法

```java
@Service
public class SomeService {
    
    public void doSomething() {
        // 检查用户是否已登录
        if (!MiniAppUserProvider.isLoggedIn()) {
            throw new RuntimeException("用户未登录");
        }
        
        // 获取当前用户ID
        String userId = MiniAppUserProvider.getCurrentUserId();
        
        // 获取完整用户信息
        ColdStorageCustomerEntity user = MiniAppUserProvider.getCurrentUser();
        
        // 获取客户端IP
        String clientIp = MiniAppUserProvider.getClientIp();
        
        // 业务逻辑...
    }
}
```

### 权限检查

```java
public void checkPermission(String resourceId) {
    if (!MiniAppUserProvider.isValidUser()) {
        throw new RuntimeException("用户账号无效");
    }
    
    String userId = MiniAppUserProvider.getCurrentUserId();
    // 检查用户对资源的访问权限
}
```

### 数据过滤

```java
public List<DataEntity> getUserData() {
    String userId = MiniAppUserProvider.getCurrentUserId();
    
    // 确保只查询当前用户的数据
    return dataMapper.selectByUserId(userId);
}
```

### 审计日志

```java
public void recordOperation(String operation) {
    String userId = MiniAppUserProvider.getCurrentUserId();
    String userName = MiniAppUserProvider.getCurrentUserName();
    String clientIp = MiniAppUserProvider.getClientIp();
    
    auditLogService.save(userId, userName, clientIp, operation);
}
```

## 工作流程

### 请求处理流程

1. **请求到达** → 微信小程序发送带有Mini-App-Token的请求
2. **拦截器处理** → MiniAppAuthInterceptor验证Token
3. **设置ThreadLocal** → 将用户信息存储到ThreadLocal
4. **业务处理** → Controller/Service可随时获取用户信息
5. **清理缓存** → 请求完成后自动清理ThreadLocal

### 登录流程

1. **用户登录** → 调用登录接口
2. **验证成功** → 生成Token并存储到Redis
3. **设置ThreadLocal** → 将用户信息设置到ThreadLocal
4. **返回结果** → 返回Token和用户信息

## 优势

### 1. 简化代码
- 无需在每个方法中传递用户参数
- 在任何地方都可以直接获取当前用户信息
- 减少代码冗余和维护复杂度

### 2. 线程安全
- 每个线程拥有独立的用户信息副本
- 多个请求之间互不干扰
- 确保数据隔离性

### 3. 性能优化
- 避免重复查询用户信息
- 内存访问比数据库访问更快
- 减少数据库压力

### 4. 安全性
- 自动进行权限验证
- 确保用户只能访问自己的数据
- 提供统一的认证机制

## 注意事项

### 1. 内存管理
- **必须清理**: 每次请求结束后必须清理ThreadLocal，避免内存泄漏
- **自动清理**: 拦截器会在`afterCompletion`中自动清理
- **异常处理**: 即使发生异常也要确保清理ThreadLocal

### 2. 使用范围
- 只在微信小程序相关的API中有效
- 需要有效的Mini-App-Token才能设置用户信息
- 不适用于系统内部的定时任务或异步处理

### 3. 多线程场景
```java
// 错误示例：在异步线程中使用
@Async
public void asyncMethod() {
    // 这里获取不到用户信息，因为是不同的线程
    String userId = MiniAppUserProvider.getCurrentUserId(); // 返回null
}

// 正确做法：传递参数
@Async
public void asyncMethod(String userId) {
    // 使用传递的参数
}
```

### 4. 单元测试
```java
@Test
public void testSomeMethod() {
    // 测试前设置模拟用户
    ColdStorageCustomerEntity mockUser = new ColdStorageCustomerEntity();
    mockUser.setId("test-user-id");
    MiniAppUserProvider.setCurrentUser(mockUser);
    
    try {
        // 执行测试
        someService.doSomething();
    } finally {
        // 测试后清理
        MiniAppUserProvider.clearAll();
    }
}
```

## 配置说明

### 拦截器配置

在`MiniAppWebConfig`中可以配置：

```java
// 拦截路径
.addPathPatterns("/api/coldstorage/**")

// 排除不需要认证的路径
.excludePathPatterns(
    "/api/coldstorage/miniapp/login",
    "/api/coldstorage/miniapp/register", 
    "/api/coldstorage/miniapp/sms/send"
)
```

### Redis配置

Token存储在Redis中，键格式：
- 访问Token: `coldstorage:token:{token}`
- 刷新Token: `coldstorage:refresh_token:{refreshToken}`

## 故障排查

### 常见问题

1. **获取不到用户信息**
   - 检查请求是否包含Mini-App-Token头部
   - 确认Token是否有效
   - 查看拦截器是否正确配置

2. **内存泄漏**
   - 确保拦截器正确注册
   - 检查是否在finally块中清理ThreadLocal
   - 查看异常处理是否完善

3. **权限问题**
   - 确认用户状态是否正常
   - 检查Token是否过期
   - 验证拦截器执行顺序

### 日志配置

开启调试日志：
```properties
logging.level.jnpf.interceptor.MiniAppAuthInterceptor=DEBUG
```

## 扩展功能

### 自定义用户信息

可以扩展MiniAppUserProvider添加更多用户相关信息：

```java
// 添加新的ThreadLocal变量
private static final ThreadLocal<String> USER_ROLE_CACHE = new ThreadLocal<>();

// 添加对应的setter/getter方法
public static void setCurrentUserRole(String role) {
    USER_ROLE_CACHE.set(role);
}

public static String getCurrentUserRole() {
    return USER_ROLE_CACHE.get();
}
```

### 权限注解

可以创建权限注解简化权限检查：

```java
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface RequireAuth {
    boolean checkValid() default true;
}
```

这个ThreadLocal机制为微信小程序提供了强大且安全的用户信息管理功能，简化了开发过程并提高了代码质量。 
package jnpf.flowable.model.candidates;

import lombok.Data;

import java.util.HashMap;
import java.util.Map;

/**
 * 类的描述
 *
 * <AUTHOR> Info. Co., Ltd
 * @version 5.0.x
 * @since 2024/4/18 21:17
 */
@Data
public class CandidateCheckFo {
    String flowId;
    Map<String, Object> formData = new HashMap<>();
    // 1.同意 0.拒绝
    Integer handleStatus = 1;
    // 任务主键
    String id;

    String delegateUser;
}

package jnpf.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import jnpf.base.ActionResult;
import jnpf.base.vo.ListVO;
import jnpf.base.vo.PageListVO;
import jnpf.base.vo.PaginationVO;
import jnpf.model.ColdStorageForm;
import jnpf.model.ColdStorageQueryForm;
import jnpf.model.ColdStorageVO;
import jnpf.service.ColdStorageService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import org.springframework.test.web.servlet.MvcResult;
/**
 * 冷库管理Controller测试类
 *
 * @版本： V5.2.0
 * @版权： 杭州多协信息技术有限公司（https://www.duoxieyun.com）
 * @作者： 多协开发组
 * @日期： 2025-01-27
 */
@ExtendWith(MockitoExtension.class)
class ColdStorageControllerTest {

    @Mock
    private ColdStorageService coldStorageService;

    @InjectMocks
    private ColdStorageController coldStorageController;

    private MockMvc mockMvc;
    private ObjectMapper objectMapper;

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.standaloneSetup(coldStorageController).build();
        objectMapper = new ObjectMapper();
    }

    /**
     * 测试获取冷库列表（条件查询）
     */
    @Test
    void testGetList() throws Exception {
        // 准备测试数据
        List<ColdStorageVO> storageList = new ArrayList<>();
        ColdStorageVO storage = new ColdStorageVO();
        storage.setId("storage-id-1");
        storage.setStorageName("测试冷库");
        storage.setStorageCode("CS001");
        storage.setStorageStatus(1);
        storageList.add(storage);

        ColdStorageQueryForm queryForm = new ColdStorageQueryForm();
        queryForm.setCurrentPage(1);
        queryForm.setPageSize(10);
        queryForm.setStorageName("测试");

        // Mock服务层方法
        when(coldStorageService.getList(any(ColdStorageQueryForm.class))).thenReturn(storageList);
        when(coldStorageService.getCount(any(ColdStorageQueryForm.class))).thenReturn(1L);

        // 执行测试
       MvcResult result = mockMvc.perform(post("/api/coldstorage/storage/getList")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(queryForm)))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.list").isArray())
                .andExpect(jsonPath("$.data.list[0].id").value("storage-id-1"))
                .andExpect(jsonPath("$.data.list[0].storageCode").value("CS001"))
                .andExpect(jsonPath("$.data.pagination.total").value(1))
                .andReturn();

        System.out.println("实际响应状态码: " + result.getResponse().getStatus());
        System.out.println("实际响应内容: " + result.getResponse().getContentAsString(java.nio.charset.StandardCharsets.UTF_8));

        // 验证服务层方法被调用
        verify(coldStorageService).getList(any(ColdStorageQueryForm.class));
        verify(coldStorageService).getCount(any(ColdStorageQueryForm.class));
    }

    /**
     * 测试获取冷库列表（无条件）
     */
    @Test
    void testListAll() throws Exception {
        // 准备测试数据
        List<ColdStorageVO> storageList = new ArrayList<>();
        ColdStorageVO storage = new ColdStorageVO();
        storage.setId("storage-id-1");
        storage.setStorageName("测试冷库");
        storage.setStorageCode("CS001");
        storageList.add(storage);

        // Mock服务层方法
        when(coldStorageService.getList()).thenReturn(storageList);

        // 执行测试
        mockMvc.perform(get("/api/coldstorage/storage"))
                .andExpect(status().isOk())
                .andDo(print())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.list").isArray())
                .andExpect(jsonPath("$.data.list[0].id").value("storage-id-1"));

        // 验证服务层方法被调用
        verify(coldStorageService).getList();
    }

    /**
     * 测试根据项目ID获取冷库列表
     */
    @Test
    void testListByProjectId() throws Exception {
        // 准备测试数据
        String projectId = "project-id-1";
        List<ColdStorageVO> storageList = new ArrayList<>();
        ColdStorageVO storage = new ColdStorageVO();
        storage.setId("storage-id-1");
        storage.setStorageName("测试冷库");
        storage.setProjectId(projectId);
        storageList.add(storage);

        // Mock服务层方法
        when(coldStorageService.getListByProjectId(projectId)).thenReturn(storageList);

        // 执行测试
        mockMvc.perform(get("/api/coldstorage/storage/project/{projectId}", projectId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.list").isArray())
                .andExpect(jsonPath("$.data.list[0].id").value("storage-id-1"));

        // 验证服务层方法被调用
        verify(coldStorageService).getListByProjectId(projectId);
    }

    /**
     * 测试根据客户ID获取关联的冷库列表
     */
    @Test
    void testGetStoragesByCustomerId() throws Exception {
        // 准备测试数据
        String customerId = "customer-id-1";
        List<ColdStorageVO> storageList = new ArrayList<>();
        ColdStorageVO storage = new ColdStorageVO();
        storage.setId("storage-id-1");
        storage.setStorageName("测试冷库");
        storageList.add(storage);

        // Mock服务层方法
        when(coldStorageService.getStoragesByCustomerId(customerId)).thenReturn(storageList);

        // 执行测试
        mockMvc.perform(get("/api/coldstorage/storage/customer/{customerId}", customerId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.list").isArray())
                .andExpect(jsonPath("$.data.list[0].id").value("storage-id-1"));

        // 验证服务层方法被调用
        verify(coldStorageService).getStoragesByCustomerId(customerId);
    }

    /**
     * 测试获取冷库信息
     */
    @Test
    void testGetInfo() throws Exception {
        // 准备测试数据
        String storageId = "storage-id-1";
        ColdStorageVO storage = new ColdStorageVO();
        storage.setId(storageId);
        storage.setStorageName("测试冷库");
        storage.setStorageCode("CS001");

        // Mock服务层方法
        when(coldStorageService.getInfo(storageId)).thenReturn(storage);

        // 执行测试
        mockMvc.perform(get("/api/coldstorage/storage/{id}", storageId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.id").value(storageId))
                .andExpect(jsonPath("$.data.storageCode").value("CS001"));

        // 验证服务层方法被调用
        verify(coldStorageService).getInfo(storageId);
    }

    /**
     * 测试新建冷库 - 成功
     */
    @Test
    void testCreateSuccess() throws Exception {
        // 准备测试数据
        ColdStorageForm form = new ColdStorageForm();
        form.setStorageName("测试冷库");
        form.setStorageCode("CS001");
        form.setStorageType(1);
        form.setProjectId("project-id-1");

        // Mock服务层方法
        when(coldStorageService.existsByStorageCode(anyString(), isNull())).thenReturn(false);
        doNothing().when(coldStorageService).create(any(ColdStorageForm.class));

        // 执行测试
        mockMvc.perform(post("/api/coldstorage/storage")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(form)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.msg").value("新建成功"));

        // 验证服务层方法被调用
        verify(coldStorageService).existsByStorageCode("CS001", null);
        verify(coldStorageService).create(any(ColdStorageForm.class));
    }

    /**
     * 测试新建冷库 - 冷库编号已存在
     */
    @Test
    void testCreateStorageCodeExists() throws Exception {
        // 准备测试数据
        ColdStorageForm form = new ColdStorageForm();
        form.setStorageName("测试冷库");
        form.setStorageCode("CS001");
        form.setStorageType(1);

        // Mock服务层方法
        when(coldStorageService.existsByStorageCode(anyString(), isNull())).thenReturn(true);

        // 执行测试
        mockMvc.perform(post("/api/coldstorage/storage")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(form)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(400))
                .andExpect(jsonPath("$.msg").value("冷库编号已存在"));

        // 验证服务层方法被调用
        verify(coldStorageService).existsByStorageCode("CS001", null);
        verify(coldStorageService, never()).create(any(ColdStorageForm.class));
    }

    /**
     * 测试修改冷库 - 成功
     */
    @Test
    void testUpdateSuccess() throws Exception {
        // 准备测试数据
        String storageId = "storage-id-1";
        ColdStorageForm form = new ColdStorageForm();
        form.setStorageName("测试冷库修改");
        form.setStorageCode("CS001");
        form.setStorageType(1);

        // Mock服务层方法
        when(coldStorageService.existsByStorageCode(anyString(), eq(storageId))).thenReturn(false);
        doNothing().when(coldStorageService).update(eq(storageId), any(ColdStorageForm.class));

        // 执行测试
        mockMvc.perform(put("/api/coldstorage/storage/{id}", storageId)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(form)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.msg").value("修改成功"));

        // 验证服务层方法被调用
        verify(coldStorageService).existsByStorageCode("CS001", storageId);
        verify(coldStorageService).update(storageId, form);
    }

    /**
     * 测试修改冷库 - 冷库编号已存在
     */
    @Test
    void testUpdateStorageCodeExists() throws Exception {
        // 准备测试数据
        String storageId = "storage-id-1";
        ColdStorageForm form = new ColdStorageForm();
        form.setStorageName("测试冷库修改");
        form.setStorageCode("CS001");
        form.setStorageType(1);

        // Mock服务层方法
        when(coldStorageService.existsByStorageCode(anyString(), eq(storageId))).thenReturn(true);

        // 执行测试
        mockMvc.perform(put("/api/coldstorage/storage/{id}", storageId)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(form)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(400))
                .andExpect(jsonPath("$.msg").value("冷库编号已存在"));

        // 验证服务层方法被调用
        verify(coldStorageService).existsByStorageCode("CS001", storageId);
        verify(coldStorageService, never()).update(anyString(), any(ColdStorageForm.class));
    }

    /**
     * 测试删除冷库
     */
    @Test
    void testDelete() throws Exception {
        // 准备测试数据
        String storageId = "storage-id-1";

        // Mock服务层方法
        doNothing().when(coldStorageService).delete(storageId);

        // 执行测试
        mockMvc.perform(delete("/api/coldstorage/storage/{id}", storageId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.msg").value("删除成功"));

        // 验证服务层方法被调用
        verify(coldStorageService).delete(storageId);
    }

    /**
     * 测试根据冷库编号查询冷库 - 成功
     */
    @Test
    void testGetByStorageCodeSuccess() throws Exception {
        // 准备测试数据
        String storageCode = "CS001";
        String storageId = "storage-id-1";
        
        // Mock实体对象
        jnpf.entity.ColdStorageEntity entity = new jnpf.entity.ColdStorageEntity();
        entity.setId(storageId);
        entity.setStorageCode(storageCode);
        
        ColdStorageVO storage = new ColdStorageVO();
        storage.setId(storageId);
        storage.setStorageCode(storageCode);
        storage.setStorageName("测试冷库");

        // Mock服务层方法
        when(coldStorageService.getByStorageCode(storageCode)).thenReturn(entity);
        when(coldStorageService.getInfo(storageId)).thenReturn(storage);

        // 执行测试
        mockMvc.perform(get("/api/coldstorage/storage/code/{storageCode}", storageCode))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.id").value(storageId))
                .andExpect(jsonPath("$.data.storageCode").value(storageCode));

        // 验证服务层方法被调用
        verify(coldStorageService).getByStorageCode(storageCode);
        verify(coldStorageService).getInfo(storageId);
    }

    /**
     * 测试根据冷库编号查询冷库 - 冷库不存在
     */
    @Test
    void testGetByStorageCodeNotFound() throws Exception {
        // 准备测试数据
        String storageCode = "CS001";

        // Mock服务层方法
        when(coldStorageService.getByStorageCode(storageCode)).thenReturn(null);

        // 执行测试
        mockMvc.perform(get("/api/coldstorage/storage/code/{storageCode}", storageCode))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(400))
                .andExpect(jsonPath("$.msg").value("冷库不存在"));

        // 验证服务层方法被调用
        verify(coldStorageService).getByStorageCode(storageCode);
        verify(coldStorageService, never()).getInfo(anyString());
    }
} 
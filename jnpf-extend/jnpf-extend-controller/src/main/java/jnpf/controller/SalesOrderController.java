package jnpf.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jnpf.base.ActionResult;
import jnpf.base.controller.SuperController;
import jnpf.constant.MsgCode;
import jnpf.entity.SalesOrderEntity;
import jnpf.entity.SalesOrderEntryEntity;
import jnpf.exception.WorkFlowException;
import jnpf.model.salesorder.SalesOrderEntryEntityInfoModel;
import jnpf.model.salesorder.SalesOrderForm;
import jnpf.model.salesorder.SalesOrderInfoVO;
import jnpf.service.SalesOrderService;
import jnpf.util.GeneraterSwapUtil;
import jnpf.util.JsonUtil;
import jnpf.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 销售订单
 *
 * <AUTHOR>
 * @version V3.1.0
 * @copyright 引迈信息技术有限公司
 * @date 2019年9月29日 上午9:18
 */
@Tag(name = "销售订单", description = "SalesOrder")
@RestController
@RequestMapping("/api/extend/Form/SalesOrder")
public class SalesOrderController extends SuperController<SalesOrderService, SalesOrderEntity> {

    @Autowired
    private SalesOrderService salesOrderService;
    @Autowired
    private GeneraterSwapUtil generaterSwapUtil;

    /**
     * 获取销售订单信息
     *
     * @param id 主键值
     * @return
     */
    @Operation(summary = "获取销售订单信息")
    @GetMapping("/{id}")
    @Parameters({
            @Parameter(name = "id", description = "主键", required = true),
    })
    public ActionResult info(@PathVariable("id") String id) {
        SalesOrderEntity entity = salesOrderService.getInfo(id);
        List<SalesOrderEntryEntity> entityList = salesOrderService.getSalesEntryList(id);
        SalesOrderInfoVO vo = JsonUtil.getJsonToBean(entity, SalesOrderInfoVO.class);
        if (vo != null) {
            vo.setEntryList(JsonUtil.getJsonToList(entityList, SalesOrderEntryEntityInfoModel.class));
        }
        return ActionResult.success(vo);
    }

    /**
     * 新建销售订单
     *
     * @param salesOrderForm 表单对象
     * @return
     * @throws WorkFlowException
     */
    @Operation(summary = "新建销售订单")
    @PostMapping("/{id}")
    @Parameters({
            @Parameter(name = "id", description = "主键", required = true),
            @Parameter(name = "salesOrderForm", description = "销售模型", required = true),
    })
    public ActionResult create(@RequestBody SalesOrderForm salesOrderForm, @PathVariable("id") String id) throws WorkFlowException {
        SalesOrderEntity sales = JsonUtil.getJsonToBean(salesOrderForm, SalesOrderEntity.class);
        List<SalesOrderEntryEntity> salesEntryList = JsonUtil.getJsonToList(salesOrderForm.getEntryList(), SalesOrderEntryEntity.class);
        salesOrderService.submit(id, sales, salesEntryList, salesOrderForm);
        return ActionResult.success(MsgCode.SU006.get());
    }

    /**
     * 修改销售订单
     *
     * @param salesOrderForm 表单对象
     * @param id             主键
     * @return
     * @throws WorkFlowException
     */
    @Operation(summary = "修改销售订单")
    @PutMapping("/{id}")
    @Parameters({
            @Parameter(name = "id", description = "主键", required = true),
            @Parameter(name = "salesOrderForm", description = "销售模型", required = true),
    })
    public ActionResult update(@RequestBody SalesOrderForm salesOrderForm, @PathVariable("id") String id) throws WorkFlowException {
        SalesOrderEntity sales = JsonUtil.getJsonToBean(salesOrderForm, SalesOrderEntity.class);
        sales.setId(id);
        List<SalesOrderEntryEntity> salesEntryList = JsonUtil.getJsonToList(salesOrderForm.getEntryList(), SalesOrderEntryEntity.class);
        salesOrderService.submit(id, sales, salesEntryList, salesOrderForm);
        return ActionResult.success(MsgCode.SU006.get());
    }

    /**
     * 删除销售订单信息
     *
     * @param id 主键
     */
    @Operation(summary = "删除销售订单信息")
    @DeleteMapping("/{id}")
    @Parameters({
            @Parameter(name = "id", description = "主键", required = true),
    })
    public ActionResult delete(@PathVariable("id") String id, @RequestParam(name = "forceDel", defaultValue = "false") Boolean forceDel) {
        SalesOrderEntity entity = salesOrderService.getInfo(id);
        if (null != entity) {
            if (!forceDel) {
                String errMsg = generaterSwapUtil.deleteFlowTask(entity.getId());
                if (StringUtil.isNotBlank(errMsg)) {
                    throw new RuntimeException(errMsg);
                }
            }
            salesOrderService.delete(entity);
            return ActionResult.success(MsgCode.SU003.get());
        }
        return ActionResult.fail(MsgCode.FA003.get());
    }
}

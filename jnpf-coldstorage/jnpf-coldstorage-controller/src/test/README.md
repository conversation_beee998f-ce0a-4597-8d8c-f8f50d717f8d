# 冷库储能Controller测试说明

## 概述

本目录包含了 `jnpf-coldstorage-controller` 项目的完整测试用例，涵盖了所有Controller层的API端点测试。

## 测试结构

```
src/test/java/jnpf/controller/
├── BaseControllerTest.java              # 测试基类
├── ControllerTestSuite.java             # 测试套件
├── ColdStorageCustomerControllerTest.java  # 客户管理测试
├── DeviceControllerTest.java            # 设备管理测试
├── ColdStorageControllerTest.java       # 冷库管理测试
├── MiniAppAuthControllerTest.java       # 小程序认证测试
└── util/
    └── TestUtil.java                    # 测试工具类
```

## 测试覆盖范围

### 1. ColdStorageCustomerControllerTest
- ✅ 获取客户列表（条件查询）
- ✅ 获取客户列表（无条件）
- ✅ 获取客户信息
- ✅ 新建客户（成功/失败场景）
- ✅ 修改客户（成功/失败场景）
- ✅ 删除客户
- ✅ 重置客户密码
- ✅ 启用/禁用客户
- ✅ 根据手机号查询客户
- ✅ 根据客户编号查询客户

### 2. DeviceControllerTest
- ✅ 获取设备列表（条件查询）
- ✅ 获取设备列表（无条件）
- ✅ 根据客户ID获取设备列表
- ✅ 根据仓库ID获取设备列表
- ✅ 获取设备信息
- ✅ 新增设备
- ✅ 修改设备
- ✅ 删除设备
- ✅ 批量新增设备
- ✅ 更新设备状态
- ✅ 获取设备统计信息
- ✅ 根据客户统计设备数量
- ✅ 根据仓库统计设备数量

### 3. ColdStorageControllerTest
- ✅ 获取冷库列表（条件查询）
- ✅ 获取冷库列表（无条件）
- ✅ 根据项目ID获取冷库列表
- ✅ 根据客户ID获取关联的冷库列表
- ✅ 获取冷库信息
- ✅ 新建冷库（成功/失败场景）
- ✅ 修改冷库（成功/失败场景）
- ✅ 删除冷库
- ✅ 根据冷库编号查询冷库

### 4. MiniAppAuthControllerTest
- ✅ 小程序登录（成功/失败）
- ✅ 获取用户信息（成功/失败）
- ✅ 刷新Token（成功/失败）
- ✅ 退出登录
- ✅ 检查Token有效性
- ✅ 绑定手机号（成功/失败）
- ✅ 发送验证码

## 运行测试

### 1. 运行所有测试
```bash
mvn test
```

### 2. 运行特定测试类
```bash
mvn test -Dtest=ColdStorageCustomerControllerTest
```

### 3. 运行特定测试方法
```bash
mvn test -Dtest=ColdStorageCustomerControllerTest#testCreateSuccess
```

### 4. 运行测试套件
```bash
mvn test -Dtest=ControllerTestSuite
```

## 测试配置

### 测试环境配置
- 使用H2内存数据库
- 启用SQL日志输出
- 配置测试专用配置文件

### 测试数据
- 使用 `TestUtil` 工具类创建测试数据
- 每个测试方法独立，不依赖其他测试
- 使用Mock对象模拟服务层依赖

## 测试最佳实践

### 1. 测试命名规范
- 测试方法名：`test[方法名][场景]`
- 例如：`testCreateSuccess`、`testCreateMobilePhoneExists`

### 2. 测试结构
```java
@Test
void testMethodName() throws Exception {
    // 1. 准备测试数据
    // 2. Mock服务层方法
    // 3. 执行测试
    // 4. 验证结果
    // 5. 验证服务层方法被调用
}
```

### 3. 断言验证
- 验证HTTP状态码
- 验证响应JSON结构
- 验证业务逻辑结果
- 验证服务层方法调用

### 4. 异常测试
- 测试参数验证失败
- 测试业务逻辑异常
- 测试服务层异常

## 注意事项

1. **依赖管理**：确保添加了必要的测试依赖
   - JUnit 5
   - Mockito
   - Spring Boot Test
   - H2 Database

2. **测试隔离**：每个测试方法应该独立运行，不依赖其他测试的状态

3. **Mock使用**：合理使用Mock对象，避免测试过于复杂

4. **测试覆盖率**：确保测试覆盖了所有主要的业务场景和异常情况

## 故障排除

### 常见问题

1. **依赖缺失**
   ```xml
   <dependency>
       <groupId>org.springframework.boot</groupId>
       <artifactId>spring-boot-starter-test</artifactId>
       <scope>test</scope>
   </dependency>
   ```

2. **测试配置问题**
   - 检查 `application-test.yml` 配置
   - 确保测试环境正确配置

3. **Mock对象问题**
   - 确保正确Mock了所有依赖的服务
   - 检查Mock方法的参数匹配

## 扩展测试

如需添加新的测试用例：

1. 在对应的Controller测试类中添加新的测试方法
2. 使用 `TestUtil` 工具类创建测试数据
3. 遵循测试命名和结构规范
4. 确保测试覆盖了成功和失败场景
5. 更新测试套件配置

## 联系方式

如有问题，请联系开发团队。 
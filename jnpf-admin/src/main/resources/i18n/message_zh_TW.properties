AD101=ä»é¢ç¡æ³è¨ªå
AD102=ç³»çµ±ç°å¸¸
AD103=æä½éæ¼é »ç¹
AD104=æ²æè¨ªåè¨±å¯æ¬ï¼è«è¯ç¹«ç®¡çå¡ææ¬
AD105=èªè­å¤±æï¼ç¡æ³è¨ªåç³»çµ±è³æº
AD106=ç¡æå§é¨èªè­ï¼ç¡æ³è¨ªåç³»çµ±è³æº
COD001=éåæ¢ä»¶éæ¿¾ç²å¾ç®æ¨çºç©º
COPY001=è¤è£½åç¨±é·åº¦è¶éäºéå¶é·åº¦
DB001=æ¸æé¡åç·¨ç¢¼ä¸ç¬¦åæ¨æºï¼è«æ³¨æå¤§å°å¯«ï¼ãMySQL , SQLServer , Oracle , DM , KingbaseES , PostgreSQL
DB002=è«æª¢æ¥ 1ãé£æ¥è³è¨ 2ãç¶²è·¯éä¿¡ 3ãè³æåº«æåååçæã è©³æï¼{0}
DB003=ééurlæ¾ä¸å°å°æè³æåº«
DB004=æ¥è©¢çµæéçºç©ºã
DB005=æªæ¾å°å°æè³æåº«é¡å:{0}({1})
DB006=æªæ¾å°å°ææ¸æé¡åè½æ
DB007=å°å¥è¡¨åå­å¨éè¤
DB008=å»ºè¡¨æ¸æèç¶åéç®åæåº«ä¸å¹é: {0} -> {1}
DB009=æªæ¾å°è¡¨è³è¨: {0}
DB010=è³æåº«{0}ï¼æªæ¾å°æ­¤è¡¨:{1}
DB011=è¯åä¸»éµé¡ç¼ºå°â{0}âå­æ®µå¼
DB012=è¡¨ç¤ºå°æç²åæ¸å¼å¤±æ
DB013=ç®åéæªæ¯æ{0}æ¸æé¡åï¼{1}
DB014=è¡¨ "{0}"ä¸­å­æ®µ "{1}" çºä¸»éµï¼ä¸åè¨±æ¸æé¡å "{2}"
DB015=æªæ¾å°å­æ®µSQLèªå¥
DB016=æ²æåå§å­æ®µ
DB017=sqlç°å¸¸ï¼{0}
DB018=è«å¨è³æåº«ä¸­æ·»å å°æçæ¸æè¡¨
DB019=æ·»å å¤±æ
DB101=ç³»çµ±èªå¸¶è¡¨,ä¸åè¨±è¢«åªé¤
DB102=ç³»çµ±èªå¸¶è¡¨,ä¸åè¨±è¢«ç·¨è¼¯
DB201=è¡¨å·²ç¶è¢«ä½¿ç¨,ä¸åè¨±è¢«åªé¤
DB202=è¡¨å·²ç¶è¢«ä½¿ç¨,ä¸åè¨±è¢«ç·¨è¼¯
DB301=è³æåº«é£æ¥æå
DB302=è³æåº«é£æ¥å¤±æ
ETD101=æä½å¤±æï¼åæä»¶ä¸å­å¨
ETD102=æ¾ä¸å°ç¶ç´
ETD103=ä¸è½ç§»åå°èªå·±çæä»¶å¤¾
ETD104=æªè½æ¾å°æ­¤è¨å®
ETD105=æ°å»ºæå10000æ¢æ¸æ
ETD106=ç²åå¤±æ
ETD107=å¸³æ¶èªè­é¯èª¤
ETD108=ä½ éæ²æè¨­ç½®éµä»¶çå¸³æ¶
ETD109=æä»¶å°åºå¤±æ
ETD110=æä»¶æ ¼å¼ä¸æ­£ç¢º
ETD111=æä»¶æ¾ä¸å°
ETD112=æ­¤è¨éè¢«éè¯å¼ç¨,ä¸åè¨±è¢«åªé¤
ETD113=é²æ­¢æ¡æåµå»ºéå¤æ¸æ
ETD114=ä¿å­å¤±æï¼è«éæ°ç»é¸
ETD115=è«è¼¸å¥é è¦½çurl
ETD116=è«é¸ææ­£ç¢ºçé è¦½æ¹å¼
ETD117=æ¸æè¶é1000æ¢
EXIST001=åç¨±ä¸è½éè¤
EXIST002=ç·¨ç¢¼ä¸è½éè¤
EXIST003=ç¯æ¬åå·²å­å¨
EXIST004=æä»¶å¤¾åç¨±ä¸è½éè¤
EXIST005=ç¯æ¬åç¨±è¶éäºéå¶é·åº¦
EXIST101=åç¨±éè¤ï¼è«éæ°è¼¸å¥
EXIST102=ç·¨ç¢¼éè¤ï¼è«éæ°è¼¸å¥
EXIST103=ä¸è½éè¤
FA001=æ¸æä¸å­å¨
FA002=æ´æ°å¤±æï¼æ¸æä¸å­å¨
FA003=åªé¤å¤±æï¼æ¸æä¸å­å¨
FA004=è¤è£½å¤±æï¼æ¸æä¸å­å¨
FA005=ç¼éå¤±æï¼æ¸æä¸å­å¨
FA006=ä¸è¼å¤±æï¼æ¸æä¸å­å¨
FA007=æä½å¤±æï¼æ¸æä¸å­å¨
FA008=åæ­¢å¤±æï¼æ¸æä¸å­å¨
FA009=çµæ­¢å¤±æï¼æ¸æä¸å­å¨
FA010=éåå¤±æï¼æ¸æä¸å­å¨
FA011=ç¼ä½å¤±æï¼æ¸æä¸å­å¨
FA012=ç²åå¤±æï¼æ¸æä¸å­å¨
FA013=ä»é¢ä¿®æ¹å¤±æï¼æ¸æä¸å­å¨
FA014=æ´æ°ä»é¢çæå¤±æï¼æ¸æä¸å­å¨
FA015=é è¦½å¤±æï¼æ¸æä¸å­å¨
FA016=åªé¤å¤±æï¼è©²æä»¶å¤¾å­å¨æ¸æ
FA017=æä»¶æ ¼å¼ä¸æ­£ç¢º
FA018=æä»¶ä¸å­å¨
FA019=å·²å¤±æ
FA020=æªæ¥å°è³è¨
FA021=æä½å¤±æï¼æ¨æ²æè¨±å¯æ¬æä½
FA022=æ´æ°å¤±æï¼æ¨æ²æè¨±å¯æ¬æä½ (è§è²åªæè¶ç´ç®¡çå¡æè½å¤ æä½)
FA023=æ´æ°å¤±æï¼å·²ç¶å®ç¨æ¶ï¼ç¡æ³åæçµç¹
FA024=åªé¤å¤±æï¼å·²ç¶å®ç¨æ¶
FA025=è©²çµç¹å§è¨±å¯æ¬çºç©ºï¼çµç¹åæå¤±æï¼
FA026=æ´æ°å¤±æï¼éè¯çµç¹ä¸å­å¨ï¼è«éæ°ç»éï¼æèå·æ°é é¢
FA027=è©²ç³»çµ±ä¸èå®çºç©ºï¼ç³»çµ±åæå¤±æ
FA028=æ°å¢æ¸æå¤±æ
FA029=ä¿®æ¹æ¸æå¤±æ
FA030=æ´æ°å¤±æï¼å·²ç¶å®ç¨æ¶ï¼ç¡æ³ä¿®æ¹çæ
FA031=è©²çµç¹ç¡æ¬æç¨è¨±å¯æ¬ï¼åæå¤±æ
FA032=ä¸å³æä»¶ä¸è½çºç©º
FA033=æä»¶ä¸å³å¤±æï¼
FA034=éæ³è«æ±, ç¼ºå°èªè­è³è¨
FA035=æªç²åå°ç§æ¶æå®æ¸ææºè³è¨
FA036=å¸¸ç¨æ¸æå·²å­å¨
FA037=ä»é¢è«æ±å¤±æ
FA038=æä»¶å­å²è·¯å¾é¯èª¤
FA039=éæ¥å·²å¤±æ
FA040=é è¦½å¤±æ,è«æª¢æ¥æä»¶é¡åæ¯å¦è¦ç¯
FA041=é è¦½å¤±æ,è«éæ°ä¸å³æä»¶
FA042=è«è¼¸å¥æ­£ç¢ºçæä»¶æ ¼å¼
FA043=å­å¨ååæä»¶ï¼
FA044=ä¸å­å¨è©²æä»¶
FA045=åªé¤æä»¶:{0}å¤±æ
FA046=æä»¶è®åå¤±æ
FA047=æªç¼ç¾æä»¶
FA048=å¾®ä¿¡å¬ç¾èåå§idä¸è½éè¤
FA049=æ­¤è¨éèâæ¶æ¯ç¼ééç½®âéè¯å¼ç¨ï¼ä¸åè¨±è¢«ç¦ç¨
FA050=æ­¤è¨éèâæ¶æ¯ç¼ééç½®âéè¯å¼ç¨ï¼ä¸åè¨±è¢«åªé¤
FA051=ä»é¢å·²éç½®å å¯, æ¸æè§£å¯å¤±æ.
FA052=è©²èº«ä»½æªåéæ¬é
FA101=ä¿å­å¤±æ
FA102=æ´æ°å¤±æ
FA103=åªé¤å¤±æ
FA104=ç²åå¤±æ
FA105=é è¦½å¤±æ,è«åä¿å­å¨é è¦½æ¸æ
FA106=é è¦½å¤±æ,å®åæ ¼éç½®åºç¾æ­»è¿´å
FM001=æªæ¾å°ä»é¢
FM002=è¡¨å®è³è¨ä¸å­å¨
FM003=å­è¡¨éè¤
FM004=å·²å°éè©²ç¯æ¬è¤è£½ä¸éï¼è«è¤è£½æºç¯æ¬!
FM005=è©²è¡¨å®å·²è¢«æµç¨å¼ç¨ï¼ç¡æ³åªé¤ï¼
FM006=è©²è¡¨å®æªç¼ä½ï¼ç¡æ³åæ»¾è¡¨å®å§å®¹
FM007=è©²ç¯æ¬å§è¡¨å®å§å®¹çºç©ºï¼ç¡æ³ç¼ä½
FM008=è©²åè½æªå°å¥æµç¨è¡¨å®
FM009=æµç¨æªè¨­è¨ï¼è«åè¨­è¨æµç¨ï¼
FM010=è©²åè½æµç¨èæ¼åç¨çæï¼
FM011=è¡¨[{0}]ç¡ä¸»éµ!
FM012=ä¸»éµç­ç¥:{0}ï¼èè¡¨[{1}]ä¸»éµç­ç¥ä¸ä¸è´!
FM013=è¡¨æ°å¢é¯èª¤:{0}
GT101=æå
GT102=å¤±æ
GT103=é©è­é¯èª¤
GT104=ç°å¸¸
GT105=ç»ééæ,è«éæ°ç»é
GT106=æ¨çå¸³èå¨å¶ä»å°æ¹å·²ç»é,è¢«å¼·å¶è¸¢åº
GT107=Tokené©è­å¤±æ
GT108=è«æ±è¶éæå¤§æ¸
IMP001=å°å¥æå
IMP002=å°å¥å¤±æï¼æä»¶æ ¼å¼é¯èª¤
IMP003=å°å¥å¤±æï¼æ¸æå·²å­å¨
IMP004=å°å¥å¤±æï¼æ¸ææèª¤
IMP005=å°åºå¤±æ
IMP006=å°å¥æ¸ææ ¼å¼ä¸æ­£ç¢º
IMP007=éè¤
IMP008=åç¨±
IMP009=ç·¨ç¢¼
IMP010=å°å¥å¤±æï¼æ¥è©¢ä¸å°ä¸ç´åé¡
IMP011=è«é¸æå°åºå­æ®µ
LOG001=å¸³æ¶ç°å¸¸
LOG002=è¨»é·æå
LOG004=å¸³èç°å¸¸ï¼è«è¯ç¹«ç®¡çå¡ä¿®æ¹æå±¬çµç¹è³è¨
LOG005=å¸³èæªè¢«åå
LOG006=å¸³èå·²è¢«ç¦ç¨
LOG007=å¸³èå·²è¢«åªé¤
LOG010=æ­¤IPæªå¨ç½åå®ä¸­
LOG011=ç»éå¤±æï¼ç¨æ¶æ«æªç¶å®è§è²
LOG012=å¸³èå·²è¢«éå®ï¼è«è¯ç¹«ç®¡çå¡è§£é¤éå®
LOG101=å¸³èæå¯ç¢¼é¯èª¤
LOG102=å¸³èæèª¤ï¼è«éæ°è¼¸å¥
LOG103=è«è¼¸å¥é©è­ç¢¼
LOG104=é©è­ç¢¼é¯èª¤
LOG105=é£æ¥ç§æ¶æåå¤±æï¼è«ç¨å¾åè©¦
LOG106=ç­ä¿¡é©è­ç¢¼é¯èª¤
LOG107=é©è­ç¢¼å·²å¤±æ
LOG108=è«ç­å¾{0}åéå¾åé²è¡ç»éï¼æè¯ç¹«ç®¡çå¡è§£é¤éå®
LOG109=ç§æ¶ç»éå¤±æï¼è«ç¨ææ©é©è­ç¢¼ç»é
LOG110=è³æåº«ç°å¸¸ï¼è«è¯ç¹«ç®¡çå¡èç
LOG111=å·²éåå®é»ç»é, ä¸æ¯ææ­¤ç»éæ¹å¼
LOG112=ä¸æ¯ææ­¤ç»éæ¹å¼
LOG113=æªè¨­ç½®ç§æ¶è³è¨
LOG114=ç§æ¶ç·¨ç¢¼ä¸åè¨±çºç©º
LOG115=ç§æ¶è³è¨ç²åå¤±æ
LOG116=ä¸æ¯ææ­¤é©è­
LOG117=ç­ä¿¡é©è­ç¢¼é©è­å¤±æï¼{0}
LOG118=ç§æ¶åº«åçºç©º
LOG201=èå¯ç¢¼é¯èª¤
LOG202=ä¿®æ¹æåï¼è«ç¢è¨æ°å¯ç¢¼
LOG203=ä¿®æ¹å¤±æï¼å¸³èä¸å­å¨
LOG204=ä¿®æ¹å¤±æï¼æ°å»ºå¯ç¢¼ä¸è½èèå¯ç¢¼ä¸æ¨£
LOG205=éç½®å¯ç¢¼æå
LOG206=éç½®å¯ç¢¼å¤±æ
MSERR101=ç¼éå¤±æï¼å¤±æåå ï¼SMTPæåçºç©º
MSERR102=ç¼éå¤±æï¼å¤±æåå ï¼ç¼ä»¶äººéµç®±çºç©º
MSERR103=ç¼éå¤±æï¼å¤±æåå ï¼ç¼ä»¶äººå¯ç¢¼çºç©º
MSERR104=ç¼éå¤±æï¼å¤±æåå ï¼æ¥æ¶äººçºç©º
MSERR105=ç¼éå¤±æãå¤±æåå ï¼{0}çéµç®±å¸³èæ ¼å¼æèª¤ï¼
MSERR106=ç¼éå¤±æãå¤±æåå ï¼{0}çéµç®±å¸³èçºç©ºï¼
MSERR107=ç¼éå¤±æãå¤±æåå ï¼æ¥æ¶äººå°æçéµç®±å¨é¨çºç©º
MSERR108=ç¼éå¤±æãå¤±æåå ï¼{0}
MSERR109=é£æ¥æå
MSERR110=é£æ¥å¤±æãå¤±æåå ï¼{0}
MSERR111=å·²ç¼é
MSERR112=å§å®¹ä¸è½åå«<ç¬¦è
MSERR113=æ«ç¡æªè®æ¶æ¯
MSERR114=èªå®ç¾©ç¯æ¬ç·¨ç¢¼ä¸è½ä½¿ç¨ç³»çµ±ç¯æ¬ç·¨ç¢¼è¦å
MSERR115=åµå»ºå¤±æï¼å­å¨å¤åæ¨é¡åæ¸
MSERR116=åµå»ºå¤±æï¼ä¸å­å¨æ¨é¡åæ¸
MSERR117=æ´æ°å¤±æï¼å­å¨å¤åæ¨é¡åæ¸
MSERR118=æ´æ°å¤±æï¼ä¸å­å¨æ¨é¡åæ¸
MSERR119=è«ååå¾ç³»çµ±åæ­¥è¨­ç½®ï¼éç½®ééå¸³è
MSERR120=è«ååå¾ç³»çµ±åæ­¥è¨­ç½®ï¼éç½®ä¼æ¥­å¾®ä¿¡å¸³è
MSERR121=éç½®ç¯æ¬ç¡æ¸æï¼ç¡æ³æ¸¬è©¦
OA001=ç¨æ¶ç»é
OA002=è¨­å
OA003=TOKEN
OA004=ç¨æ¶éåº
OA005=ç¨æ¶è¸¢åº
OA006=ç¨æ¶é æ¿
OA007=ç»éç°å¸¸
OA008=ç»éç²åç³»çµ±éç½®å¤±æ
OA009=è©²ç¨æ¶æªåéæ¬é
OA010=åæ¯æpcç«¯è¨ªå,APPç«¯ä¸æ¯æ
OA011=æç¨ä¸å­å¨
OA012=ç¶åæç¨å·²è¢«ç¦ç¨
OA013=ç»éå¯ç¢¼è§£å¯å¤±æ
OA014=æ³¨éæå
OA015=ç»éæå
OA016=ç»éç¥¨æå·²å¤±æ
OA017=ç¬¬ä¸æ¹æªç¶å®å¸³è
OA018=ä¸åè¨±è¨ªåæ­¤ç»éä»é¢
OA019=å¸³èä¸å­å¨
OA020=å¸³æ¶æå¯ç¢¼é¯èª¤ï¼è«éæ°è¼¸å¥
OA021=é©è­æå
OA022=éå¶æè©±, ä¸åè¨±è¨ªåç³»çµ±
OA023=ç®¡çå¡ä¸è½è¨»é·
OA024=ç»éå¤±æ
OA025=è¶ç´ç®¡çå¡
OA026=æ®éç®¡çå¡
OA027=æ®éç¨æ¶
OA028=æªç¥ä¾æº
PRI001=åå°ç¯æ¬ä¸å­å¨
PRI002=æ¸å­å­å¸ä¸å­å¨printDevçå­å¸åé¡
PRI003=ç¬¬1æ¢SQLèªå¥ï¼æ¥è©¢åºå¤æ¢è¡¨é ­è³è¨
PRI004=ç¬¬1æ¢SQLèªå¥ï¼æªæ¥åºè¡¨é ­è³è¨
PRI005=ç¬¬{index}æ¢SQLèªå¥ï¼
PRI006=å·²å°éè©²ç¯æ¬è¤è£½ä¸éï¼è«è¤è£½æºç¯æ¬
PRI007=Sqlèªæ³é¯èª¤
PRI008=è©²å ±è¡¨å·²åªé¤
PS001=æ­¤è¨éè"{0}"éè¯å¼ç¨ï¼ä¸åè¨±è¢«åªé¤
PS003=çµç¹
PS004=å´ä½
PS005=ç¨æ¶
PS006=è§è²
PS007=å¸³èä¸è½çºç©º
PS008=å§åä¸è½çºç©º
PS009=ç¨æ¶é¡åº¦å·²éå°ä¸é
PS010=è¨±å¯æ¬å·²è®æ´ï¼è«éæ°ç»é
PS011=å¯ç¢¼å·²è®æ´ï¼è«éæ°ç»é
PS012=é¡åä¸è½çºç©º
PS013=ç¶åæ©æ§Idä¸è½èç¶æ©æ§Idç¸å
PS014=è©²æç¨å·²ç¦ç¨
PS015=ç¡æ³è¨­ç½®ç¶åç¨æ¶çºåç´ç®¡çå¡
PS016=ç¡æ³è¨­ç½®è¶ç®¡çºåç´ç®¡çå¡
PS017=ç¡æ³è¨­ç½®ç¶åç¨æ¶æä½è¨±å¯æ¬
PS018=è§£ç¶å¤±æ
PS019=ç¬¬ä¸æ¹ç»éæªéç½®
PS020=æ§å¥ä¸è½çºç©º
PS021=ç¡æ³ç¦ç¨ç®¡çå¡ç¨æ¶
PS022=ç®¡çå¡åªè½ä¿®æ¹èªå·±ï¼ä¸è½ä¿®æ¹å¶ä»ç®¡çå¡
PS023=ç¡æ³ä¿®æ¹ç®¡çå¡å¸³æ¶
PS024=ç´å±¬ä¸»ç®¡ä¸è½æ¯èªå·±
PS025=ç´å±¬ä¸»ç®¡ä¸è½æ¯æçä¸å±¬ç¨æ¶
PS026=ç¡æ³åªé¤ç®¡çå¡å¸³æ¶
PS027=æ­¤ç¨æ¶çºæé¨éä¸»ç®¡ï¼ç¡æ³åªé¤
PS028=æ­¤ç¨æ¶æä¸å±¬ï¼ç¡æ³åªé¤
PS029=ç¡æ³ä¿®æ¹ç®¡çå¡å¸³æ¶çæ
PS030=ç¨æ¶è³è¨å·²è®æ´ï¼è«éæ°ç»é
PS031=è©²æç¨å·²åªé¤
PS032=è©²çµç¹ç¡æ¬æç¨è¨±å¯æ¬,åæå¤±æ
PS033=å·¥ä½äº¤æ¥æå!
PS034=å·¥ä½äº¤æ¥ç¡æ³è½ç§»çµ¦ç®¡çå¡
PS035=å·¥ä½äº¤æ¥ç¡æ³è½ç§»çµ¦æ¬äºº
SC001=æä½å¤±æï¼ä»»åä¸å­å¨
SU000=Success
SU001=æ°å»ºæå
SU002=ä¿å­æå
SU003=åªé¤æå
SU004=æ´æ°æå
SU005=æä½æå
SU006=æäº¤æåï¼è«èå¿ç­å¾
SU007=è¤è£½æå
SU008=åæ­¢æå
SU009=çµæ­¢æå
SU010=éåæå
SU011=ç¼ä½æå
SU012=ç¼éæå
SU013=ä»é¢ä¿®æ¹æå
SU014=æ´æ°ä»é¢çææå
SU015=ä¸å³æå
SU016=è¨­ç½®æå
SU017=é©è­æå
SU018=æ·»å æå
SU019=ç²åæå
SU020=åæ»¾æå
SU021=ç§»é¤æå
SU022=æ¥è©¢æå
SYS001=ååç·¨ç¢¼ä¸è½éè¤
SYS002=åªé¤å¤±æï¼ç¶åæå­ç¯é»æ¸æ
SYS003=å®æå·²ç¶è¢«ä½¿ç¨,ä¸åè¨±è¢«åªé¤
SYS004=æ¸çæå
SYS005=ä»é¢åµå»ºæå
SYS006=ç¶åSQLå«æææå­:{0}
SYS007=ä»é¢è«æ±æå
SYS008=ä»é¢ä¸ç¬¦åè¦ç¯
SYS009=è®æ¸åä¸è½åå«ææå­å
SYS010=è®æ¸åå·²å­å¨
SYS011=è³æåº«é£æ¥ä¸è½ç¸å
SYS012=è«æª¢æ¥ï¼åä¸è³æåº«ä¸ç¡æ³åæ­¥æ¸æ
SYS013=åæ­¥å¤±æ:{0}
SYS014=å­å¸é¡åä¸éºµæå­å¸å¼ç¦æ­¢åªé¤
SYS015=ç¯æ¬ä¸å­å¨
SYS016=ç¶åç®éå­å¨æ¸æ,ä¸è½ä¿®æ¹é¡å
SYS017=åªé¤å¤±æï¼è«ååªé¤å­èå®
SYS018=ç¶åå°å¥èå®çº{0}ç«¯èå®ï¼è«å¨å°ææ¨¡çµä¸å°å¥ï¼
SYS019=è«å¨é ç´ç¯é»ä¸åµå»ºç®éå¾åé²è¡èå®å°å¥
SYS020=è©²å­æ®µå¨æ¹æ¡{0}ä¸­å·²è¢«ä½¿ç¨
SYS021=ä¿®æ¹å¤±æï¼è©²æ¹æ¡ä¸åè¨±ç·¨è¼¯
SYS022=ç·¨ç¢¼é¯èª¤
SYS023=è«æ±ç¼çé¯èª¤ï¼
SYS024=ç²åä¸å°æ¸æï¼
SYS025=ç²åä¼æ¥­å¾®ä¿¡access_tokenå¤±æ
SYS026=æ­£å¨é²è¡åæ­¥ï¼è«ç¨å¾åè©¦
SYS027=è«åå¾ä¼æ¥­å¾®ä¿¡åæ­¥é¨éå°æ¬å°
SYS028=è«åå¾ééåæ­¥é¨éå°æ¬å°
SYS029=é©è­ç¢¼ä½æ¸ä¸è½å¤§æ¼6
SYS030=é©è­ç¢¼ä½æ¸ä¸è½å°æ¼3
SYS031=æ¸¬è©¦ç¼éæ¶æ¯çé£æ¥å¤±æï¼{0}
SYS032=æ¸¬è©¦ç¼éæ¶æ¯é£æ¥æå
SYS033=æ¸¬è©¦çµç¹åæ­¥çé£æ¥å¤±æï¼{0}
SYS034=æ¸¬è©¦çµç¹åæ­¥é£æ¥æå
SYS035=æ¸¬è©¦é£æ¥é¡åé¯èª¤
SYS036=æ¸¬è©¦ééé£æ¥å¤±æï¼
SYS037=æ¸¬è©¦é£æ¥æå
SYS038=è¡¨è³è¨æ½åç°å¸¸
SYS039=åªé¤å¤±æï¼è«ååªé¤è©²æç¨ä¸çèå®åéæ¶
SYS040=åªé¤å¤±æï¼è«ååªé¤è©²æç¨ä¸çèå®
SYS041=åªé¤å¤±æï¼è«ååªé¤è©²æç¨ä¸çéæ¶
SYS042=è©²æ¥ç¨å·²è¢«åªé¤
SYS043=æå¾ä¸æ¢æ¸æä¸è½åªé¤
SYS044=åç¨çæ¬ä¸è½åªé¤
SYS045=æ­¸æªçæ¬ä¸è½åªé¤
SYS046=æ¸æéä¸è½éå
SYS047=SQLèªå¥åæ¯ææ¥è©¢èªå¥
SYS048=SQLèªå¥éå¸¶ä¸@formIdæ¢ä»¶
SYS049=æ­£å¨é²è¡åæ­¥,è«ç¨ç­
SYS050=åªè½è¼¸å¥å­æ¯ãæ¸å­ãé»ãæ©«ç·åä¸åç·ï¼ä¸ä»¥å­æ¯éé ­
SYS051=ç¿»è­¯æ¨è¨ä¸è½éè¤
SYS052=ç¿»è­¯èªè¨è³å°å¡«å¯«ä¸é 
SYS053=ç²åééaccess_tokenå¤±æ
SYS101=æ´æ°å¤±æï¼ä¸»ç³»çµ±ä¸åè¨±ç¦ç¨
SYS102=ä¸»ç³»çµ±ä¸åè¨±åªé¤
SYS103=ç³»çµ±å¨å¯©æ¹å¸¸ç¨èªä¸­è¢«ä½¿ç¨ï¼ä¸åè¨±åªé¤
SYS104=æ´æ°å¤±æï¼ä¸»ç³»çµ±ä¸åè¨±ä¿®æ¹ç·¨ç¢¼
SYS105=å¸¸ç¨èªå·²å­å¨
SYS121=ä»é¢æ«åªæ¯æHTTPåHTTPSæ¹å¼
SYS122=ä»é¢è«æ±å¤±æ
SYS123=ä»é¢è«æ±å¤±æ, JSèª¿ç¨å¤±æ,é¯èª¤ï¼{0}
SYS124=é©è­è«æ±è¶æ
SYS125=appSecreté¯èª¤
SYS126=appIdä½¿ç¨æéå·²å°æ
SYS127=appIdåæ¸é¯èª¤
SYS128={0}ä¸è½ä½¿ç¨ç³»çµ±ãéç¼èªè¨åè³æåº«ééµå­å½å
SYS129=ç¶åæ¸ææºä¸æ¯æå¨é£æ¥
SYS130=æ¨é¡ä¸è½çºç©º
SYS131=çµææéå¿é ææ¼éå§æé
SYS132=çµæéè¤å¿é ææ¼éå§æé
VS001=åæ­¥å°æµç¨æï¼{0}
VS002=ç¼ä½å¤±æï¼æµç¨æªè¨­è¨ï¼
VS003=ç¡è¡¨çææè¡¨å¤±æ
VS004=ç¼ä½
VS005=é è¦½
VS006=ä¸è¼
VS007=åæ­¥æå
VS008=åæ»¾å¤±æ,æ«ç¡ç·ä¸çæ¬
VS009=åæ¸è§£æé¯èª¤ï¼
VS010=ç¡æéæ¥
VS011=å¯ç¢¼é¯èª¤
VS012=æªæ¾å°è©²åè½è¡¨å®
VS013=æªéåè¡¨å®å¤éï¼
VS014=ä¸è¼éæ¥å·²å¤±æ
VS015=å­æ®µä¸è½çºç©º
VS016=è·¯å¾é¯èª¤
VS017=éæå©æè¢«ç¦ç¨
VS018=è¡¨è¦ç¯åç¨±ä¸è½éè¤
VS019=è¦ç¯åç¨±ä¸è½ä½¿ç¨ç³»çµ±ééµå­æJAVAééµå­
VS020=å­æ®µè¦ç¯åç¨±ä¸è½éè¤
VS021=â{0}âå½åä¸ç¬¦åè¦ç¯
VS022=ä¸»éµç­ç¥:[éªè±ID],è¡¨[ {0} ]ä¸»éµè¨­ç½®ä¸æ¯æ!
VS023=ä¸»éµç­ç¥:[èªå¢ID],è¡¨[ {0} ]ä¸»éµè¨­ç½®ä¸æ¯æ!
VS024=è¡¨å®ä¸å­å¨æèæªç¼ä½ï¼
VS025=æªç²åå°æµç¨ç¼èµ·äºº
VS026=è¦ç¯åç¨±åå©å­æ¯å¿é å°å¯«
VS027=èªåçæçã{0}ãè¶åºé·åº¦ï¼æäº¤å¤±æï¼
VS028=è¦åæå¤æ°å»º5å
VS401=è©²ç¯æ¬å§è¡¨å®å§å®¹çºç©ºï¼ç¡æ³
VS402=è©²ç¯æ¬å§åè¡¨å§å®¹çºç©ºï¼ç¡æ³
VS403=è©²åè½æªéç½®æµç¨ä¸å¯ç¨
VS404=å®è¡è¼¸å¥ä¸è½éè¤
VS405=ç¶åè¡¨å®åæ¸æå·²è¢«èª¿æ´ï¼è«éæ°é²å¥è©²é é¢ç·¨è¼¯ä¸¦æäº¤æ¸æ
VS406=è©²åè½éç½®çæµç¨èæ¼åç¨
VS407=è¡¨é ­åç¨±ä¸å¯æ´æ¹ï¼è¡¨é ­è¡ä¸è½åªé¤
VS408=è«è³å°é¸æä¸åæ¸æè¡¨
VS409=æªæ¾å°ä¸»è¡¨è³è¨
VS410=è«å°å¥å°æåè½çjsonæä»¶
VS411=å·²å­å¨ç¸ååè½
VS412=è©²è¡¨å®å·²åªé¤
VS413=æç¨ä¸è½çºç©º
VS414=éæ¶æ¸æè³è¨å­å¨éè¤
VS415=è©²éæ¶å·²åªé¤
WF001=å¯©æ ¸æå
WF002=éåæå
WF003=è½è¾¦æå
WF004=å ç°½æå
WF005=ç¶åæµç¨è¢«éåï¼ç¡æ³æ¤åæµç¨
WF006=æµç¨å·²æ¤åï¼ä¸è½éè¤æä½
WF007=æ¤åå¤±æ,è½åæ¸æç¡æ³æ¤å
WF008=æ¤åæå
WF009=åè½æµç¨ä¸è½çµæ­¢
WF010=ææ´¾æå
WF011=æ¹éæä½å®æ
WF012=è©²æµç¨ä¸è½æä½
WF013=å¾©æ´»æå
WF014=è®æ´æå
WF015=æèµ·æå
WF016=æ¢å¾©æå
WF017=å§è¨äººåè¢«å§è¨äººç¸åï¼å§è¨å¤±æ
WF018=æä½å¤±æï¼åä¸æéå§æç¸åæµç¨çå§è¨
WF019=æä½å¤±æï¼åä¸æéå§æç¸åæµç¨ï¼ä¸è½ç¸äºå§è¨
WF020=åè½æµç¨ä¸è½åªé¤
WF021=ä¸è½åªé¤
WF022=å¬è¾¦æå
WF023=æªæ¾å°å¬è¾¦äºº
WF024=è©²åè½å·²è¢«æµç¨å¼ç¨ï¼è«éæ°é¸æéè¯åè½
WF025=åç¨å¤±æï¼æµç¨æªè¨­è¨
WF026=åç¨æå
WF027=ç¦ç¨æå
WF028=è©²çæ¬å§æå·¥å®ä»»åæµè½ï¼ç¡æ³åªé¤
WF029=æ¨æ²æç¼èµ·è©²æµç¨çè¨±å¯æ¬
WF030=è¡¨å®æªæ¾å°
WF031=å·²å¯©æ ¸å®æ
WF032=åçµä¸è½æä½
WF033=è½åç¯é»ä¸å­å¨æéç½®é¯èª¤
WF034=è½åå¤±æï¼è½åç¯é»æªå¯©æ¹
WF035=éåè³æ¨çå¯©æ¹ï¼ä¸è½åç¼èµ·éå
WF036=æµç¨å·²èçï¼ç¡æ³æ¤å
WF037=ç¶åæµç¨åå«å­æµç¨ï¼ç¡æ³æ¤å
WF038=å­æµç¨ç¡æ³æ¤å
WF039=ä¸ä¸ç¯é»çºé¸æåæ¯ç¡æ³æ¹éå¯©æ¹
WF040=æ¢ä»¶æµç¨åå«åé¸äººç¡æ³æ¹ééé
WF041=è©²æµç¨å·¥å®å·²çµæ­¢
WF042=è©²æµç¨å·¥å®å·²æ¤å
WF043=è©²ç¯é»æ²ææ¸æ,ç¡æ³å¾©æ´»
WF044=æ­¤æµç¨ä¸æ¯æè®æ´
WF045=ç¶åç¯é»æå­æµç¨ç¡æ³è®æ´
WF046=éåç¯é»åå«å­æµç¨ï¼éåå¤±æ
WF047=ç¶åç¯é»æªå¯©æ¹ï¼ä¸è½éå
WF048=æµç¨èæ¼æèµ·çæï¼ä¸å¯æä½
WF049=ç¶åæµç¨æ­£å¨éè¡ä¸è½åªé¤
WF050=å·²è¢«æèµ·ä¸è½åªé¤
WF051=æ²æåªé¤è¨±å¯æ¬
WF052=ä¸»çæ¬æ²æå§å®¹
WF053=æµç¨æ²æåç¨
WF054=æµç¨ç·¨ç¢¼ä¸è½éè¤
WF055=æµç¨è¡¨å®ä¸ä¸è´ï¼è«éæ°é¸æ
WF056=è©²æµç¨ç±ç·ä¸éç¼çæçï¼ç¡æ³ç´æ¥åªé¤ï¼è«å¨åè½è¨­è¨ä¸­åªé¤ç¸éåè½
WF057=è©²æµç¨å§å·¥å®ä»»åæµè½æªçµæï¼ç¡æ³åªé¤
WF058=ç¶åæµç¨æ­£å¨éè¡ä¸è½éè¤æäº¤
WF059=æµç¨èªåç¼èµ·å¯©æ¹å¤±æ
WF060=é§åç¯é»ä¸è½æ¯å­æµç¨
WF061=ä¸ä¸ç¯é»ç¡å¯©æ¹äººå¡è«è¯ç¹«ç®¡çå¡
WF062=è¡¨å®å·²è¢«å¼ç¨ï¼è«éæ°é¸æ
WF063=æµç¨å·²ç¼èµ·ï¼ç¡æ³åªé¤
WF064=ä»»åä¸å­å¨,æèå·²èç
WF065=æçµæå
WF066=åææå
WF067=åè¾¦æå
WF068=åè¾¦ä¿å­æå
WF069=æ¸ç°½æå
WF070=æ¤é·æå
WF071=æå¾ä¸æ¢æ¸æä¸è½åªé¤
WF072=åç¨çæ¬ä¸è½åªé¤
WF073=æ­¸æªçæ¬ä¸è½åªé¤
WF074=æ«åæå
WF075=æ¢ä»¶ä¸æ»¿è¶³ç¡æ³æµè½
WF076=ç¯é»ä¸å­å¨
WF077=æµç¨ç¡æ³æ¤å
WF078=æµç¨æªåæï¼ç¡æ³æ¤é·
WF079=æ­¸æªç°å¸¸
WF080=é¸æçæ¸æä¸è½éç°½
WF081=ç¡æ³å ç°½
WF082=ç¡æ³æ¸ç°½
WF083=ç¡æ³éå
WF084=ç¡æ³è½å¯©
WF085=ç¡æ³åè¾¦
WF086=ç¡æ³æ¹éå¯©æ¹
WF087=ç¶è¾¦æªç°½æ¶
WF088=ç¶è¾¦æªéå§è¾¦ç
WF089=æµç¨ç¼ä½å¤±æ
WF090=æµç¨ç¼ä½å¤±æ
WF091=æµç¨æäº¤å¤±æ
WF092=ç²åå¼æç¶åä»»åå¤±æ
WF093=æµç¨åªé¤å¤±æ
WF094=ç²ååºç·éåå¤±æ
WF095=ç²åç·ä¹å¾çä»»åç¯é»å¤±æ
WF096=ç²åä¸ä¸ç´ä»»åç¯é»éåå¤±æ
WF097=ç²åä¸ä¸ç´ä»»åç¯é»éåå¤±æ
WF098=ä»»åå®æå¤±æ
WF099=ç²åæµç¨å¯¦ä¾å¤±æ
WF100=ç²åæªç¶éçç¯é»å¤±æ
WF101=ç²åç¯é»çå¾çºç¯é»å¤±æ
WF102=ç²åå¯åéçç¯é»å¤±æ
WF103=éåå¤±æ
WF104=ç¯é»è·³è½å¤±æ
WF105=è£åå¤±æ
WF106=ä¸è½å ç°½çµ¦èªå·±
WF107=ä¸è½è½å¯©çµ¦èªå·±
WF108=ä¸è½åè¾¦çµ¦èªå·±
WF109=å¿é ä¿çä¸åå ç°½äººå¡
WF110=å¯©æ¹ç°å¸¸ç¡æ³æ¤é·
WF111=æé¸æµç¨åå«æ¢ä»¶åé¸äºº
WF112=é¸æçæ¸æå°ææµç¨å·²æ«å
WF113=å·²è¢«æ«åä¸è½åªé¤
WF114=æµç¨èæ¼æ«åçæï¼ä¸å¯æä½
WF115=æµç¨å·²åçï¼ç¡æ³åªé¤
WF116=ä¸è½å ç°½çµ¦å§è¨äºº
WF117=ä¸è½è½å¯©çµ¦å§è¨äºº
WF118=ä¸è½åè¾¦çµ¦å§è¨äºº
WF119=è¨­ç½®äºæµè½æ¢ä»¶ï¼ç¡æ³æ¹éå¯©æ¹
WF120=ä¸ä¸ç¯é»å¯©æ¹ç°å¸¸ï¼ç¡æ³æ¹éå¯©æ¹
WF121=å­æµç¨èªåç¼èµ·å¯©æ¹å¤±æ
WF122=æµç¨ä¸å­å¨
WF123=æµç¨èæ¼çµæ­¢çæï¼ä¸å¯æä½
WF124=è©²æµç¨å·²ç¼èµ·æ¸æï¼ç¡æ³åªé¤!
WF125=æ¨æ²æç¼èµ·å§è¨æµç¨
WF126=æ¤é·æµç¨ä¸è½è½å¯©
WF127=æ¤é·æµç¨ä¸è½éå
WF128=è©²ç¨æ¶å·²å¯©æ¹ï¼è«éæ°æéä»é¢
WF129=å§è¨äººå·²ç¡è©²æµç¨è¨±å¯æ¬
WF130=ç®¡çå¡ä¸è½æ°å»ºå§è¨/ä»£ç
WF131=ä¸è½é¸æadmin
WF132=å·²æäººæ¥åï¼ä¸å¯ç·¨è¼¯
WF133=æµè½æ¢ä»¶ä¸æ»¿è¶³ï¼ç¡æ³èªåç¼èµ·å¯©æ¹
WF134=ç¬¬ä¸åå¯©æ¹ç¯é»è¨­å®åé¸äººï¼ç¡æ³èªåç¼èµ·å¯©æ¹
WF135=ç¬¬ä¸åå¯©æ¹ç¯é»å¼å¸¸ï¼ç¡æ³èªåç¼èµ·å¯©æ¹
WF136=æ¾ä¸å°ç¼èµ·äººï¼ç¼èµ·å¤±æ
WF137=ä»£çäººåè¢«ä»£çäººç¸åï¼ä»£çå¤±æ
WF138=å­å¨æªç°½æ¶çæ¸æï¼ç¡æ³éé
WF139=è©²æµç¨å·²è§¸ç¼äºä»»åï¼ç¡æ³å é¤
WF144=æä½å¤±æï¼åä¸æéå§æç¸åæµç¨çä»£ç
WF145=æä½å¤±æï¼åä¸æéå§æç¸åæµç¨ï¼ä¸è½ç¸äºä»£ç
app.apply.expandData=å±éæ¸æ
app.apply.location.location=æ·»å å®ä½
app.apply.location.modalTitle=é¸æä½ç½®
app.apply.location.relocation=éæ°å®ä½
app.apply.noMoreData=æ²ææ´å¤æ¸æ
app.apply.pleaseKeyword=è«è¼¸å¥ééµè©æç´¢
app.apply.screen=ç¯©é¸
app.apply.sort=æåº
app.my.accountSecurity=å¸³èå®å¨
app.my.agencyMe=ä»£ççµ¦æ
app.my.allFlow=å¨é¨æµç¨
app.my.entrustedAgency=å§è¨ä»£ç
app.my.entrustMe=å§è¨çµ¦æ
app.my.flowSelect=æµç¨é¸æ
app.my.logOut=ç»åº
app.my.myAgency=æçä»£ç
app.my.myEntrust=æçå§è¨
app.my.organization=æççµç¹
app.my.position=æçå´ä½
app.my.scanCode=æä¸æ
app.my.setting=è¨­å®
app.my.settings.About=éæ¼å¹³èº
app.my.settings.changePassword=ä¿®æ¹å¯ç¢¼
app.my.settings.contact=è¯ç¹«æå
app.my.settings.language=å¤èªè¨
app.my.settings.privacyPolicy=é±ç§æ¿ç­
app.my.settings.userAgreement=ä½¿ç¨èåå®
app.my.sto=çµæ­¢
app.my.subordinates=æçä¸å±¬
app.my.switchIdentity=åæèº«ä»½
common.add1Text=æ·»å 
common.add2Text=æ°å¢
common.addText=æ°å»º
common.back=è¿å
common.batchDelText=æ¹éåªé¤
common.batchDelTip=æ¨ç¢ºå®è¦å é¤éäºæ¸æåï¼æ¯å¦ç¹¼çºï¼
common.batchPrintText=æ¹éæå°
common.cancelText=åæ¶
common.chooseText=è«é¸æ
common.chooseTextPrefix=è«é¸æ
common.cleanText=æ¸ç©º
common.closeList=ééåè¡¨
common.closeText=éé
common.collapseAll=æç
common.continueAndAddText=ç¢ºå®ä¸¦æ°å¢
common.continueText=ç¢ºå®ä¸¦ç¹¼çº
common.copyText=å¾©è£½
common.dark=é»æä¸»é¡
common.delText=åªé¤
common.delTip=æ­¤æä½å°æ°¸ä¹åªé¤è©²æ¸æ, æ¯å¦ç¹¼çºï¼
common.detailText=è©³æ
common.drawerSearchText=è«è¼¸å¥ééµè©
common.editText=ç·¨è¼¯
common.enterKeyword=è«è¼¸å¥ééµè©
common.expandAll=å±é
common.exportText=å°åº
common.importText=å°å¥
common.inputPlaceholder=è«è¼¸å¥
common.inputText=è«è¼¸å¥
common.inputTextPrefix=è«è¼¸å¥
common.keyword=ééµè©
common.leftTreeSearchText=è«è¼¸å¥ééµè©
common.light=äº®è²ä¸»é¡
common.loadingText=å è¼ä¸­...
common.moreText=æ´å¤
common.next=ä¸ä¸æ­¥
common.nextRecord=ä¸ä¸æ¢
common.noData=æ«ç¡æ¸æ
common.okText=ç¢ºå®
common.prev=ä¸ä¸æ­¥
common.previewText=é è¦½
common.prevRecord=ä¸ä¸æ¢
common.printText=æå°
common.queryText=æ¥è©¢
common.redo=å·æ°
common.redoText=éå
common.resetText=éç½®
common.saveText=ä¿å­
common.searchText=æç´¢
common.selectDataTip=è«é¸æä¸æ¢æ¸æ
common.selectI18nCode=é¸æç¿»è­¯æ¨è¨
common.selectPlaceholder=è«é¸æ
common.submitText=æäº¤
common.superQuery=é«ç´æ¥è©¢
common.syncText=ç¬¬ä¸æ¹åæ­¥
common.tipTitle=æç¤º
common.undoText=æ¤é·
component.app.searchNotData=æ«ç¡æç´¢çµæ
component.app.toNavigate=åæ
component.app.toSearch=ç¢ºèª
component.countdown.normalText=ç²åé©è­ç¢¼
component.countdown.sendText={0}ç§å¾éæ°ç²å
component.cropper.btn_reset=éç½®
component.cropper.btn_rotate_left=éæéæè½
component.cropper.btn_rotate_right=é æéæè½
component.cropper.btn_scale_x=æ°´å¹³ç¿»è½
component.cropper.btn_scale_y=åç´ç¿»è½
component.cropper.btn_zoom_in=æ¾å¤§
component.cropper.btn_zoom_out=ç¸®å°
component.cropper.modalTitle=é ­åä¸å³
component.cropper.okText=ç¢ºèªä¸¦ä¸å³
component.cropper.preview=é è¦½
component.cropper.selectImage=é¸æåç
component.cropper.uploadSuccess=ä¸å³æå
component.drawer.cancelText=éé
component.drawer.loadingText=å è¼ä¸­...
component.drawer.okText=ç¢ºèª
component.excel.exportModalTitle=å°åºæ¸æ
component.excel.fileName=æä»¶å
component.excel.fileType=æä»¶é¡å
component.form.apiSelectNotFound=è«ç­å¾æ¸æå è¼å®æ...
component.form.fold=æ¶èµ·
component.form.maxTip=å­ç¬¦æ¸æå°æ¼{0}ä½
component.form.unfold=å±é
component.icon.copy=å¾©è£½åæ¨æå!
component.icon.placeholder=é»æé¸æåæ¨
component.icon.search=æç´¢åæ¨
component.jnpf.areaSelect.modalTitle=çå¸å
component.jnpf.calculate.storage=ç¨æ¼å±ç¤ºè¨ç®çµæï¼ä¸æ¸æåææä¿å­å¥åº«
component.jnpf.calculate.unStorage=ç¨æ¼å±ç¤ºè¨ç®çµæï¼ä¸æ¸æä¸æä¿å­
component.jnpf.common.allData=å¨é¨æ¸æ
component.jnpf.common.autoGenerate=ç³»çµ±èªåçæ
component.jnpf.common.clearAll=æ¸ç©ºåè¡¨
component.jnpf.common.selected=å·²é¸
component.jnpf.dateRange.endPlaceholder=çµææ¥æ
component.jnpf.dateRange.startPlaceholder=éå§æ¥æ
component.jnpf.depSelect.modalTitle=é¸æé¨é
component.jnpf.groupSelect.modalTitle=é¸æåçµ
component.jnpf.iconPicker.modalTitle=åæ¨é¸æ
component.jnpf.iconPicker.searchPlaceholder=è«è¼¸å¥ééµè©
component.jnpf.iconPicker.select=é¸æ
component.jnpf.iconPicker.ymCustom=ymCustomåæ¨
component.jnpf.iconPicker.ymIcon=ymIconåæ¨
component.jnpf.location.location=æ·»å å®ä½
component.jnpf.location.modalTitle=é¸æä½ç½®
component.jnpf.location.relocation=éæ°å®ä½
component.jnpf.location.searchPlaceholder=æç´¢æç´æ¥å¨å°åä¸é»é¸
component.jnpf.numberRange.max=æå¤§å¼
component.jnpf.numberRange.min=æå°å¼
component.jnpf.organizeSelect.modalTitle=é¸æçµç¹
component.jnpf.popupAttr.storage=ç¨æ¼å±ç¤ºéè¯å½çªçå±¬æ§ï¼ä¸æ¸æåææä¿å­å¥åº«
component.jnpf.popupAttr.unStorage=ç¨æ¼å±ç¤ºéè¯å½çªçå±¬æ§ï¼ä¸æ¸æä¸æä¿å­
component.jnpf.popupSelect.modalTitle=é¸ææ¸æ
component.jnpf.posSelect.modalTitle=é¸æå´ä½
component.jnpf.relationFormAttr.storage=ç¨æ¼å±ç¤ºéè¯è¡¨å®çå±¬æ§ï¼ä¸æ¸æåææä¿å­å¥åº«
component.jnpf.relationFormAttr.unStorage=ç¨æ¼å±ç¤ºéè¯è¡¨å®çå±¬æ§ï¼ä¸æ¸æä¸æä¿å­
component.jnpf.roleSelect.modalTitle=é¸æè§è²
component.jnpf.sign.operateTip=è«å¨æ­¤ååä½¿ç¨é¼ æ¨æå¯«ç°½å
component.jnpf.sign.signPlaceholder=è«ç°½å
component.jnpf.sign.signTip=æå¯«ç°½å
component.jnpf.timeRange.endPlaceholder=çµææé
component.jnpf.timeRange.startPlaceholder=éå§æé
component.jnpf.userSelect.modalTitle=é¸æç¨æ¶
component.menu.search=èå®æç´¢
component.modal.cancelText=éé
component.modal.close=éé
component.modal.maximize=æå¤§å
component.modal.okText=ç¢ºèª
component.modal.restore=éå
component.table.action=æä½
component.table.index=åºè
component.table.settingColumn=åè¨­ç½®
component.table.settingColumnShow=åå±ç¤º
component.table.settingDens=å¯åº¦
component.table.settingDensDefault=é»èª
component.table.settingDensMiddle=ä¸­ç­
component.table.settingDensSmall=ç·æ¹
component.table.settingFixedLeft=åºå®å°å·¦å´
component.table.settingFixedRight=åºå®å°å³å´
component.table.settingFullScreen=å¨å±
component.table.settingIndexColumnShow=åºèå
component.table.settingSelectColumnShow=å¾é¸å
component.table.status=çæ
component.table.summary=åè¨
component.table.total=å± {total} æ¢æ¸æ
component.time.after=å¾
component.time.before=å
component.time.days=å¤©
component.time.hours=å°æ
component.time.just=åå
component.time.minutes=åé
component.time.seconds=ç§
component.tree.checkStrictly=å±¤ç´éè¯
component.tree.checkUnStrictly=å±¤ç´ç¨ç«
component.tree.expandAll=å±éå¨é¨
component.tree.reload=å·æ°æ¸æ
component.tree.selectAll=é¸æå¨é¨
component.tree.unExpandAll=æçå¨é¨
component.tree.unSelectAll=åæ¶é¸æ
component.upload.accept=æ¯æ{0}æ ¼å¼
component.upload.acceptUpload=åªè½ä¸å³{0}æ ¼å¼æä»¶
component.upload.audio=é³é »
component.upload.buttonText=é»æä¸å³
component.upload.checking=æä»¶æ ¡é©ä¸­
component.upload.choose=é¸ææä»¶
component.upload.del=åªé¤
component.upload.download=ä¸è¼
component.upload.downloadAll=å¨é¨ä¸è¼
component.upload.fileMaxNumber=æå¤å¯ä»¥ä¸å³{0}åæä»¶
component.upload.fileMaxSize=æä»¶å¤§å°è¶é{size}{unit}
component.upload.fileName=æä»¶å
component.upload.fileReadError=æä»¶{0}è®ååºé¯ï¼è«æª¢æ¥è©²æä»¶
component.upload.fileSize=æä»¶å¤§å°
component.upload.fileStatue=çæ
component.upload.fileTypeCheck=è«é¸æ{0}é¡åçæä»¶
component.upload.image=åç
component.upload.imageMaxNumber=æå¤å¯ä»¥ä¸å³{0}å¼µåç
component.upload.imageMaxSize=åçå¤§å°è¶é{size}{unit}
component.upload.imgUpload=åçä¸å³
component.upload.legend=ç¥ç¸®å
component.upload.maxNumber=æå¤åªè½ä¸å³{0}åæä»¶
component.upload.maxSize=å®åæä»¶ä¸è¶é{0}MB
component.upload.maxSizeMultiple=åªè½ä¸å³ä¸è¶é{0}MBçæä»¶!
component.upload.operating=æä½
component.upload.paused=æ«åä¸­
component.upload.preview=é è¦½
component.upload.reUploadFailed=éæ°ä¸å³å¤±ææä»¶
component.upload.save=ä¿å­
component.upload.saveError=æ²æä¸å³æåçæä»¶ï¼ç¡æ³ä¿å­!
component.upload.saveWarn=è«ç­å¾æä»¶ä¸å³å¾ï¼ä¿å­!
component.upload.startUpload=éå§ä¸å³
component.upload.upload=ä¸å³
component.upload.uploaded=å·²ä¸å³
component.upload.uploadError=ä¸å³å¤±æ
component.upload.uploadImg=è«ä¸å³åç
component.upload.uploading=ä¸å³ä¸­
component.upload.uploadSuccess=ä¸å³æå
component.upload.uploadWait=è«ç­å¾æä»¶ä¸å³çµæå¾æä½
component.upload.video=è¦é »
component.upload.videoNoPreview=é³è¦é »æä»¶ä¸è½é è¦½
component.upload.view=æ¥ç
component.upload.viewImage=æ¥çåç
component.upload.waiting=ç­å¾ä¸­
component.upload.zipNoPreview=å£ç¸®åä¸è½é è¦½
component.verify.dragText=è«æä½æ»å¡æå
component.verify.error=é©è­å¤±æï¼
component.verify.redoTip=é»æåçå¯å·æ°
component.verify.successText=é©è­éé
component.verify.time=é©è­æ ¡é©æå,èæ{time}ç§ï¼
formGenerator.cleanComponentTip=ç¢ºå®è¦æ¸ç©ºææçµä»¶å?
formGenerator.component.alert=æç¤º
formGenerator.component.areaSelect=çå¸åå
formGenerator.component.autoComplete=ä¸æè£å¨
formGenerator.component.barcode=æ¢å½¢ç¢¼
formGenerator.component.billRule=å®æçµä»¶
formGenerator.component.button=æé
formGenerator.component.calculate=è¨ç®å¬å¼
formGenerator.component.card=å¡çå®¹å¨
formGenerator.component.cascader=ç´è¯é¸æ
formGenerator.component.checkbox=å¤é¸æ¡çµ
formGenerator.component.collapse=æçé¢æ¿
formGenerator.component.colorPicker=é¡è²é¸æ
formGenerator.component.createTime=åµå»ºæé
formGenerator.component.createUser=åµå»ºäººå¡
formGenerator.component.currOrganize=æå±¬çµç¹
formGenerator.component.currPosition=æå±¬å´ä½
formGenerator.component.datePicker=æ¥æé¸æ
formGenerator.component.depSelect=é¨éé¸æ
formGenerator.component.divider=åå²ç·
formGenerator.component.editor=å¯ææ¬
formGenerator.component.groupSelect=åçµé¸æ
formGenerator.component.groupTitle=åçµæ¨é¡
formGenerator.component.iframe=Iframe
formGenerator.component.input=å®è¡è¼¸å¥
formGenerator.component.inputNumber=æ¸å­è¼¸å¥
formGenerator.component.link=éæ¥
formGenerator.component.location=å®ä½
formGenerator.component.modifyTime=ä¿®æ¹æé
formGenerator.component.modifyUser=ä¿®æ¹äººå¡
formGenerator.component.organizeSelect=çµç¹é¸æ
formGenerator.component.popupAttr=å½çªé¸æå±¬æ§
formGenerator.component.popupSelect=å½çªé¸æ
formGenerator.component.popupTableSelect=ä¸æè¡¨æ ¼
formGenerator.component.posSelect=å´ä½é¸æ
formGenerator.component.qrcode=äºç¶­ç¢¼
formGenerator.component.radio=å®é¸æ¡çµ
formGenerator.component.rate=è©å
formGenerator.component.relationForm=éè¯è¡¨å®
formGenerator.component.relationFormAttr=éè¯è¡¨å®å±¬æ§
formGenerator.component.roleSelect=è§è²é¸æ
formGenerator.component.row=æµæ ¼å®¹å¨
formGenerator.component.select=ä¸æé¸æ
formGenerator.component.sign=æå¯«ç°½å
formGenerator.component.slider=æ»å¡
formGenerator.component.switch=éé
formGenerator.component.tab=æ¨ç°½é¢æ¿
formGenerator.component.table=è¨­è¨å­è¡¨
formGenerator.component.tableGrid=è¡¨æ ¼å®¹å¨
formGenerator.component.text=ææ¬
formGenerator.component.textarea=å¤è¡è¼¸å¥
formGenerator.component.timePicker=æéé¸æ
formGenerator.component.treeSelect=ä¸ææ¨¹å½¢
formGenerator.component.uploadFile=æä»¶ä¸å³
formGenerator.component.uploadImg=åçä¸å³
formGenerator.component.userSelect=ç¨æ¶é¸æ
formGenerator.component.usersSelect=ç¨æ¶çµä»¶
formGenerator.copyComponentTip=ç¢ºå®å¾©è£½è©²çµä»¶?
formGenerator.delComponentTip=ç¢ºå®åªé¤è©²çµä»¶?
layout.footer.onlineDocument=å¨ç·ææª
layout.footer.onlinePreview=å¨ç·é è¦½
layout.header.about=éæ¼å¹³èº
layout.header.commonMenus=å¸¸ç¨èå®
layout.header.dropdownItemDoc=ææª
layout.header.dropdownItemLoginOut=éåºç³»çµ±
layout.header.feedback=åé¥åé¡
layout.header.home=é¦é 
layout.header.lockScreen=éå®å±å¹
layout.header.lockScreenBtn=éå®
layout.header.lockScreenPassword=éå±å¯ç¢¼
layout.header.profile=åäººä¿¡æ¯
layout.header.setting=è¨­ç½®
layout.header.standingChange=åæèº«ä»½
layout.header.statement=å®æ¹è²æ
layout.header.systemChange=æç¨åæ
layout.header.tooltipChat=èå¤©
layout.header.tooltipEntryFull=å¨å±
layout.header.tooltipErrorLog=é¯èª¤æ¥èª
layout.header.tooltipExitFull=éåºå¨å±
layout.header.tooltipLock=éå®å±å¹
layout.header.tooltipNotify=æ¶æ¯
layout.multipleTab.close=ééæ¨ç°½é 
layout.multipleTab.closeAll=ééå¨é¨æ¨ç°½é 
layout.multipleTab.closeLeft=ééå·¦å´æ¨ç°½é 
layout.multipleTab.closeOther=ééå¶å®æ¨ç°½é 
layout.multipleTab.closeRight=ééå³å´æ¨ç°½é 
layout.multipleTab.reload=éæ°å è¼
layout.multipleTab.setCommon=è¨­çºå¸¸ç¨èå®
layout.setting.animation=åç«
layout.setting.animationType=åç«é¡å
layout.setting.autoScreenLock=èªåéå±
layout.setting.blueBg=èèæè¾°
layout.setting.breadcrumb=é¢åå±
layout.setting.breadcrumbIcon=é¢åå±åæ¨
layout.setting.cachePage=ç·©å­é é¢
layout.setting.clearBtn=æ¸ç©ºç·©å­ä¸¦è¿åç»éé 
layout.setting.closeMixSidebarOnChange=åæé é¢ééèå®
layout.setting.collapseMenuDisplayName=æçèå®é¡¯ç¤ºåç¨±
layout.setting.colorWeak=è²å¼±æ¨¡å¼
layout.setting.contentMode=å§å®¹ååå¯¬åº¦
layout.setting.contentModeFixed=å®å¯¬
layout.setting.contentModeFull=æµå¼
layout.setting.copyBtn=æ·è²
layout.setting.darkMode=ä¸»é¡
layout.setting.defaultBg=ç¶å¸ä¸»é¡
layout.setting.drawerTitle=éç½®
layout.setting.expandedMenuWidth=èå®å±éå¯¬åº¦
layout.setting.fixedHeader=åºå®header
layout.setting.fixedSideBar=åºå®Sidebar
layout.setting.footer=é è³
layout.setting.fullContent=å¨å±å§å®¹
layout.setting.grayMode=ç°è²æ¨¡å¼
layout.setting.greenBg=ç¢§ç¶ ç¿ é¢¨
layout.setting.header=é æ¬
layout.setting.headerTheme=é æ¬ä¸»é¡
layout.setting.interfaceDisplay=çé¢é¡¯ç¤º
layout.setting.interfaceFunction=çé¢åè½
layout.setting.menuAccordion=å´éèå®æé¢¨ç´æ¨¡å¼
layout.setting.menuCollapse=æçèå®
layout.setting.menuCollapseButton=èå®æçæé
layout.setting.menuDrag=å´éèå®ææ½
layout.setting.menuSearch=èå®æç´¢
layout.setting.menuTriggerBottom=åºé¨
layout.setting.menuTriggerNone=ä¸é¡¯ç¤º
layout.setting.menuTriggerTop=é é¨
layout.setting.menuTypeMix=é é¨æ··åæ¨¡å¼
layout.setting.menuTypeMixSidebar=å·¦å´æ··åæ¨¡å¼
layout.setting.menuTypeSidebar=å·¦å´èå®æ¨¡å¼
layout.setting.menuTypeTopMenu=é é¨èå®æ¨¡å¼
layout.setting.minute=åé
layout.setting.mixSidebarFixed=åºå®å±éèå®
layout.setting.mixSidebarTrigger=æ··åèå®è§¸ç¼æ¹å¼
layout.setting.navMode=å°èªæ¬æ¨¡å¼
layout.setting.notAutoScreenLock=ä¸èªåéå±
layout.setting.off=é
layout.setting.on=é
layout.setting.operatingContent=å¾©è£½æå,è«å° src/settings/projectSetting.ts ä¸­ä¿®æ¹éç½®ï¼
layout.setting.operatingTitle=æä½æå
layout.setting.progress=é é¨é²åº¦æ¢
layout.setting.purpleBg=ç´«è¤è¿è­
layout.setting.resetSuccess=éç½®æåï¼
layout.setting.sidebar=å·¦å´èå®
layout.setting.sidebarTheme=èå®ä¸»é¡
layout.setting.splitMenu=åå²èå®
layout.setting.switchAnimation=åæåç«
layout.setting.switchLoading=åæloading
layout.setting.systemBackground=ç³»çµ±èæ¯
layout.setting.sysTheme=ç³»çµ±ä¸»é¡
layout.setting.tabDetail=æ¨ç°½è©³æé 
layout.setting.tabs=æ¨ç°½é 
layout.setting.tabsFoldBtn=æ¨ç°½é æçæé
layout.setting.tabsIcon=æ¨ç°½åæ¨
layout.setting.tabsQuickBtn=æ¨ç°½é å¿«æ·æé
layout.setting.tabsRedoBtn=æ¨ç°½é å·æ°æé
layout.setting.toggleLocale=èªè¨åæ
layout.setting.topMenuAlignCenter=å±å³
layout.setting.topMenuAlignLeft=å±å·¦
layout.setting.topMenuAlignRight=å±ä¸­
layout.setting.topMenuLayout=é é¨èå®å¸å±
layout.setting.triggerClick=é»æ
layout.setting.triggerHover=æ¸å
routes.basic.emailDetail=æ¥çéµä»¶
routes.basic.errorLogList=é¯èª¤æ¥èªåè¡¨
routes.basic.externalLink=éæ¥
routes.basic.home=é¦é 
routes.basic.login=ç»é
routes.basic.previewModel=åè½é è¦½
routes.basic.workFlowDetail=æµç¨è©³æ
routes.commonWords=å¯©æ¹å¸¸ç¨èª
routes.dataReport=å ±è¡¨ç¤ºä¾(å)
routes.extend=éç¼ç¤ºä¾
routes.extend-barCode=æ¢ç¢¼ç¤ºä¾
routes.extend-bigData=ç¾è¬æ¸æ
routes.extend-documentPreview=ææªç¤ºä¾
routes.extend-email=éµä»¶æ¶ç¼
routes.extend-formDemo=è¡¨å®ç¤ºä¾
routes.extend-formDemo-fieldForm1=è¡¨å®å­æ®µ1
routes.extend-formDemo-fieldForm2=è¡¨å®å­æ®µ2
routes.extend-formDemo-fieldForm3=è¡¨å®å­æ®µ3
routes.extend-formDemo-fieldForm4=è¡¨å®å­æ®µ4
routes.extend-formDemo-fieldForm5=è¡¨å®å­æ®µ5
routes.extend-formDemo-fieldForm6=è¡¨å®å­æ®µ6
routes.extend-formDemo-verifyForm=è¡¨å®é©è­
routes.extend-formDemo-verifyForm1=è¡¨å®é©è­1
routes.extend-functionDemo=åè½ç¤ºä¾
routes.extend-graphDemo=åè¡¨ç¤ºä¾
routes.extend-graphDemo-echartsBar=E-æ±çå
routes.extend-graphDemo-echartsBarAcross=E-æ©«çæ¢å½¢å
routes.extend-graphDemo-echartsCandlestick=E-Kç·å
routes.extend-graphDemo-echartsFunnel=E-æ¼é¬¥å
routes.extend-graphDemo-echartsGauge=E-åè¡¨å
routes.extend-graphDemo-echartsLineArea=E-ç·å½¢å
routes.extend-graphDemo-echartsLineBar=E-ææ±æ··åå
routes.extend-graphDemo-echartsPie=E-é¤çå
routes.extend-graphDemo-echartsScatter=E-æ£é»å
routes.extend-graphDemo-echartsTree=E-æ¨¹å½¢å
routes.extend-graphDemo-highchartsArea=H-é¢ç©å
routes.extend-graphDemo-highchartsBellcurve=H-è²ç¾æ²ç·
routes.extend-graphDemo-highchartsBullet=H-å­å½å
routes.extend-graphDemo-highchartsColumn=H-æ±çå
routes.extend-graphDemo-highchartsFunnel=H-æ¼é¬¥å
routes.extend-graphDemo-highchartsGauge=H-åè¡¨å
routes.extend-graphDemo-highchartsLine=H-ç·æ§å
routes.extend-graphDemo-highchartsPie=H-é¤çå
routes.extend-graphDemo-highchartsScatter=H-æ£é»å
routes.extend-graphDemo-highchartsWordcloud=H-è©é²å
routes.extend-importAndExport=å°å¥å°åº
routes.extend-map=å°åç¤ºä¾
routes.extend-order=è¨å®ç®¡ç
routes.extend-orderDemo=è¨å®ç¤ºä¾
routes.extend-portalDemo=éæ¶ç¤ºä¾
routes.extend-printData=æå°ç¤ºä¾
routes.extend-projectGantt=é ç®ç®¡ç
routes.extend-schedule=æ¥ç¨å®æ
routes.extend-signature=é»å­ç°½å
routes.extend-signet=é»å­ç°½ç« 
routes.extend-tableDemo=è¡¨æ ¼ç¤ºä¾
routes.extend-tableDemo-commonTable=æ®éè¡¨æ ¼
routes.extend-tableDemo-complexHeader=å¾©éè¡¨é ­
routes.extend-tableDemo-extension=å»¶ä¼¸æ´å±
routes.extend-tableDemo-groupingTable=è¡¨æ ¼åçµ
routes.extend-tableDemo-lockTable=è¡¨æ ¼éå®
routes.extend-tableDemo-mergeTable=è¡¨æ ¼åä¸¦
routes.extend-tableDemo-postilTable=è¡¨æ ¼æ¹è¨»
routes.extend-tableDemo-printTable=è¡¨æ ¼æå°
routes.extend-tableDemo-redactTable=è¡¨æ ¼ç·¨è¼¯
routes.extend-tableDemo-signTable=è¡¨æ ¼æ¨è¨
routes.extend-tableDemo-statisticsTable=è¡¨æ ¼çµ±è¨
routes.extend-tableDemo-tableTree=è¡¨æ ¼æ¨¹å½¢
routes.extend-tableDemo-treeTable=æ¨¹å½¢è¡¨æ ¼
routes.flowEngine=æµç¨å¼æ
routes.formDesign=ç³»çµ±è¡¨å®
routes.generator=ä»£ç¢¼çæ
routes.generator-appForm=ç§»åè¡¨å®
routes.generator-flowForm=ç¼èµ·è¡¨å®
routes.generator-webForm=åè½è¡¨å®
routes.lioui=æµç¨ç¤ºä¾
routes.mainSystem=éç¼å¹³èº
routes.moreMenu=æ´å¤...
routes.msgCenter=æ¶æ¯ä¸­å¿
routes.msgCenter-accountConfig=è³¬èéç½®
routes.msgCenter-accountConfig-ding=éééç½®
routes.msgCenter-accountConfig-mail=éµç®±éç½®
routes.msgCenter-accountConfig-mp=å¾®ä¿¡å¬ç¾èéç½®
routes.msgCenter-accountConfig-shortMsg=ç­ä¿¡éç½®
routes.msgCenter-accountConfig-webhook=webhookéç½®
routes.msgCenter-accountConfig-weCom=ä¼æ¥­å¾®ä¿¡éç½®
routes.msgCenter-msgMonitor=æ¶æ¯ç£æ§
routes.msgCenter-msgTemplate=æ¶æ¯æ¨¡æ¿
routes.msgCenter-sendConfig=ç¼ééç½®
routes.onlineDev=å¨ç·éç¼
routes.onlineDev-appDesign=ç§»åè¨­è¨
routes.onlineDev-dataReport=å ±è¡¨è¨­è¨ï¼åï¼
routes.onlineDev-dataScreen=å¤§å±è¨­è¨
routes.onlineDev-integration=éæå©æ
routes.onlineDev-printDev=æå°è¨­è¨
routes.onlineDev-report=å ±è¡¨è¨­è¨
routes.onlineDev-visualPortal=éæ¶è¨­è¨
routes.onlineDev-webDesign=è¡¨å®è¨­è¨
routes.permission=çµç¹æ¬é
routes.permission-auth=æ¬éç®¡ç
routes.permission-authorize=æ¬éçµ
routes.permission-department=é¨éç®¡ç
routes.permission-grade=ç®¡çå¡
routes.permission-group=åçµç®¡ç
routes.permission-organize=çµç¹ç®¡ç
routes.permission-position=å´ä½ç®¡ç
routes.permission-role=è§è²ç®¡ç
routes.permission-user=ç¨æ¶ç®¡ç
routes.permission-userOnline=å¨ç·ç¨æ¶
routes.printDemo=æå°ç¤ºä¾
routes.report=å ±è¡¨ç¤ºä¾
routes.reportBI=å¤§å±ç¤ºä¾
routes.system=ç³»çµ±ç®¡ç
routes.system-area=è¡æ¿åå
routes.system-billRule=å®ææ¨¡æ¿
routes.system-cache=ç³»çµ±ç·©å­
routes.system-icons=ç³»çµ±åæ¨
routes.system-kit=è¡¨å®ç¯æ¬
routes.system-language=ç¿»è­¯ç®¡ç
routes.system-log=ç³»çµ±æ¥èª
routes.system-menu=æç¨èå®
routes.system-messageTemplate=æ¶æ¯æ¨¡æ¿
routes.system-monitor=ç³»çµ±ç£æ§
routes.system-notice=ç³»çµ±å¬å
routes.system-signature=ç°½ç« ç®¡ç
routes.system-smsTemplate=ç­ä¿¡æ¨¡æ¿
routes.system-sysConfig=ç³»çµ±éç½®
routes.system-systemTemplate=ç³»çµ±æ¨¡æ¿
routes.system-task=ç³»çµ±èª¿åº¦
routes.systemData=æ¸ææç¨
routes.systemData-dataBackup=æ¸æåä»½
routes.systemData-dataInterface=æ¸ææ¥å£
routes.systemData-dataModel=æ¸æå»ºæ¨¡
routes.systemData-dataSource=æ¸æé£æ¥
routes.systemData-dataSync=æ¸æåæ­¥
routes.systemData-dictionary=æ¸æå­å¸
routes.systemData-interfaceAuth=æ¥å£èªè­
routes.systemData-map=æ¸æå°å
routes.weChat=å¾®ä¿¡éç½®
routes.weChat-mpConfig=å¬ç¾èéç½®
routes.weChat-mpMaterial=å¬ç¾èç´ æ
routes.weChat-mpMenu=å¬ç¾èèå®
routes.weChat-mpMessage=å¬ç¾èæ¶æ¯
routes.weChat-mpUser=å¬ç¾èç¨æ¶
routes.weChat-qyDepartment=ä¼æ¥­èçµç¹
routes.weChat-qyhConfig=ä¼æ¥­èéç½®
routes.weChat-qyMessage=ä¼æ¥­èæ¶æ¯
routes.weChat-qyUser=ä¼æ¥­èç¨æ¶
routes.workFlow=ååè¾¦å¬
routes.workFlow-addFlow=ç¼èµ·æµç¨
routes.workFlow-document=ææªä¸­å¿
routes.workFlow-entrust=æµç¨å§æ
routes.workFlow-flowCirculate=æéæç
routes.workFlow-flowDoing=æçå¨è¾¦
routes.workFlow-flowDone=æçå·²è¾¦
routes.workFlow-flowEngine=æµç¨è¨­è¨
routes.workFlow-flowLaunch=æç¼èµ·ç
routes.workFlow-flowMonitor=æµç¨ç£æ§
routes.workFlow-flowTodo=æçå¾è¾¦
routes.workFlow-flowToSign=æçå¾ç°½
routes.workFlow-form=è¡¨å®è¨­è¨
routes.workFlow-printTemplate=æå°æ¨¡æ¿
routes.workFlow-schedule=æ¥ç¨å®æ
routes.workSystem=æ¥­åå¹³èº
sys.api.apiRequestFailed=è«æ±åºé¯ï¼è«ç¨åéè©¦
sys.api.apiTimeoutMessage=æ¥å£è«æ±è¶æ,è«å·æ°é é¢éè©¦!
sys.api.errMsg401=ç¨æ¶æ²ææ¬éï¼ä»¤çãç¨æ¶åãå¯ç¢¼é¯èª¤ï¼!
sys.api.errMsg403=ç¨æ¶å¾å°ææ¬ï¼ä½æ¯è¨ªåæ¯è¢«ç¦æ­¢çã!
sys.api.errMsg404=ç¶²çµ¡è«æ±é¯èª¤,æªæ¾å°è©²è³æº!
sys.api.errMsg405=ç¶²çµ¡è«æ±é¯èª¤,è«æ±æ¹æ³æªåè¨±!
sys.api.errMsg408=ç¶²çµ¡è«æ±è¶æ!
sys.api.errMsg500=æåå¨é¯èª¤,è«è¯ç³»ç®¡çå¡!
sys.api.errMsg501=ç¶²çµ¡æªå¯¦ç¾!
sys.api.errMsg502=ç¶²çµ¡é¯èª¤!
sys.api.errMsg503=æåä¸å¯ç¨ï¼æåå¨æ«æéè¼æç¶­è­·!
sys.api.errMsg504=ç¶²çµ¡è¶æ!
sys.api.errMsg505=httpçæ¬ä¸æ¯æè©²è«æ±!
sys.api.errorMessage=æä½å¤±æ,ç³»çµ±ç°å¸¸!
sys.api.errorTip=é¯èª¤æç¤º
sys.api.networkException=ç¶²çµ¡ç°å¸¸
sys.api.networkExceptionMsg=ç¶²çµ¡ç°å¸¸ï¼è«æª¢æ¥æ¨çç¶²çµ¡é£æ¥æ¯å¦æ­£å¸¸!
sys.api.operationFailed=æä½å¤±æ
sys.api.timeoutMessage=ç»éè¶æ,è«éæ°ç»é!
sys.app.logoutMessage=æ¯å¦ç¢ºèªéåºç³»çµ±?
sys.app.logoutTip=æº«é¦¨æé
sys.app.menuLoading=èå®å è¼ä¸­...
sys.errorLog.enableMessage=åªå¨`/src/settings/projectSetting.ts` å§çuseErrorHandle=trueæçæ.
sys.errorLog.fireAjaxError=é»æè§¸ç¼ajaxé¯èª¤
sys.errorLog.fireResourceError=é»æè§¸ç¼è³æºå è¼é¯èª¤
sys.errorLog.fireVueError=é»æè§¸ç¼vueé¯èª¤
sys.errorLog.modalTitle=é¯èª¤è©³æ
sys.errorLog.tableActionDesc=è©³æ
sys.errorLog.tableColumnDate=æé
sys.errorLog.tableColumnFile=æä»¶
sys.errorLog.tableColumnMsg=é¯èª¤ä¿¡æ¯
sys.errorLog.tableColumnStackMsg=stackä¿¡æ¯
sys.errorLog.tableColumnType=é¡å
sys.errorLog.tableTitle=é¯èª¤æ¥èªåè¡¨
sys.exception.backHome=è¿åé¦é 
sys.exception.backLogin=è¿åç»é
sys.exception.networkErrorSubTitle=æ±æ­ï¼æ¨çç¶²çµ¡é£æ¥å·²æ·éï¼è«æª¢æ¥æ¨çç¶²çµ¡ï¼
sys.exception.networkErrorTitle=ç¶²çµ¡é¯èª¤
sys.exception.noDataTitle=ç¶åé ç¡æ¸æ
sys.exception.subTitle403=æ±æ­ï¼æ¨ç¡æ¬è¨ªåæ­¤é é¢ã
sys.exception.subTitle404=æ±æ­ï¼æ¨è¨ªåçé é¢ä¸å­å¨ã
sys.exception.subTitle500=æ±æ­ï¼æåå¨å ±åé¯èª¤ã
sys.lock.alert=éå±å¯ç¢¼é¯èª¤
sys.lock.backToLogin=è¿åç»é
sys.lock.entry=é²å¥ç³»çµ±
sys.lock.placeholder=è«è¼¸å¥ç»éå¯ç¢¼
sys.lock.unlock=é»æè§£é
sys.login.accountPlaceholder=è«è¼¸å¥è³¬è
sys.login.accountTip=è«è¼¸å¥è³¬è
sys.login.backSignIn=è¿å
sys.login.changeCode=é»æåæé©è­ç¢¼
sys.login.codeTip=è«è¼¸å¥é©è­ç¢¼
sys.login.codeTitle=ææ©é©è­ç»é
sys.login.company=è«è¼¸å¥å¬å¸å
sys.login.confirmLogin=è«å¨ææ©ç«¯ç¢ºèªç»é
sys.login.confirmPassword=ç¢ºèªå¯ç¢¼
sys.login.contacts=è«è¼¸å¥è¯ç³»äºº
sys.login.diffPwd=å©æ¬¡è¼¸å¥å¯ç¢¼ä¸ä¸è´
sys.login.email=éµç®±
sys.login.expired=äºç¶­ç¢¼å·²å¤±æ
sys.login.forgetFormTitle=éç½®å¯ç¢¼
sys.login.forgetPassword=å¿è¨å¯ç¢¼?
sys.login.getCode=ç²åé©è­ç¢¼
sys.login.lastLoginInfo=ä¸æ¬¡ç»éä¿¡æ¯
sys.login.logIn=ç»é
sys.login.loginButton=ç»é
sys.login.mobile=è«è¼¸å¥ææ©è
sys.login.mobilePlaceholder=è«è¼¸å¥ææ©èç¢¼
sys.login.mobileSignInFormTitle=ææ©é©è­ç¢¼ç»é
sys.login.otherLogin=å¶ä»ç»éæ¹å¼
sys.login.otherSignIn=å¶ä»ç»éæ¹å¼
sys.login.password=è«è¼¸å¥å¯ç¢¼
sys.login.passwordPlaceholder=è«è¼¸å¥å¯ç¢¼
sys.login.passwordTip=è«è¼¸å¥å¯ç¢¼
sys.login.policy=æåæxxxé±ç§æ¿ç­
sys.login.policyPlaceholder=å¾é¸å¾æè½è¨»å
sys.login.qrCodeTip=è«ä½¿ç¨APPææäºç¶­ç¢¼ç»éï¼180ç§å¾äºç¶­ç¢¼å¤±æ
sys.login.qrSignInFormTitle=æç¢¼ç»é
sys.login.recoverCode=åæ¶ç»é
sys.login.refreshCode=é»æå·æ°
sys.login.registerButton=è¨»å
sys.login.rememberMe=è¨ä½æ
sys.login.reSend=éæ°ç¼é
sys.login.rightMobile=è«è¼¸å¥æ­£ç¢ºçææ©è
sys.login.rule=ç§æ¶å­è³¬æ¶è¦åï¼ç§æ¶è{'@'}è³¬æ¶ ä¾ï¼18577778888{'@'}101001
sys.login.scanSign=æç¢¼å¾é»æ"ç¢ºèª"ï¼å³å¯å®æç»é
sys.login.scanSuccessful=æç¢¼æå
sys.login.scanTip=æç¢¼ç»é
sys.login.scanTitle=æç¢¼ç»é
sys.login.signInDesc=è¼¸å¥æ¨çåäººè©³ç´°ä¿¡æ¯éå§ä½¿ç¨ï¼
sys.login.signInFormTitle=è³¬èå¯ç¢¼ç»é
sys.login.signInTitle=éç®±å³ç¨çä¸­å¾èºç®¡çç³»çµ±
sys.login.signUpFormTitle=è¨»å
sys.login.smsCode=è«è¼¸å¥é©è­ç¢¼
sys.login.smsPlaceholder=è«è¼¸å¥é©è­ç¢¼
sys.login.subTitle=ééå¸³èå¯ç¢¼ç»é
sys.login.subTitle1=ééææ©é©è­ç¢¼ç»éï¼æèåæçº
sys.login.subTitle2=ééå¸³èå¯ç¢¼ç»éï¼æèåæçº
sys.login.subTitle3=ééæç¢¼ç»éï¼æèåæçº
sys.login.title=è³¬æ¶å¯ç¢¼ç»é
sys.login.upper=å¤§å¯«å·²éå®
sys.login.username=è«è¼¸å¥è³¬è
sys.login.version=çæ¬
sys.login.welcome=æ­¡è¿ä½¿ç¨
sys.validate.arrayRequiredPrefix=è«è³å°é¸æä¸å
sys.validate.date=è«è¼¸å¥æ­£ç¢ºçæ¥æ
sys.validate.email=è«è¼¸å¥æ­£ç¢ºçéµç®±
sys.validate.idCard=è«è¼¸å¥æ­£ç¢ºçèº«ä»½è­èç¢¼
sys.validate.mobilePhone=è«è¼¸å¥æ­£ç¢ºçææ©èç¢¼
sys.validate.money=è«è¼¸å¥æ­£ç¢ºçéé¡
sys.validate.number=è«è¼¸å¥æ­£ç¢ºçæ¸å­
sys.validate.phone=è«è¼¸å¥æ­£ç¢ºçè¯ç³»æ¹å¼
sys.validate.telephone=è«è¼¸å¥æ­£ç¢ºçé»è©±èç¢¼
sys.validate.textRequiredSuffix=ä¸è½çºç©º
sys.validate.url=è«è¼¸å¥æ­£ç¢ºçç¶²å
views.dynamicModel.hideSome=é±èé¨å
views.dynamicModel.passwordPlaceholder=è«è¼¸å¥å¯ç¢¼
views.dynamicModel.scanAndShare=ææäºç¶­ç¢¼ï¼åäº«æ­¤éæ¥
views.dynamicModel.showMore=å è¼æ´å¤
views.http404.goBackBtn=è¿åé¦é 
views.http404.subTips=è«æª¢æ¥æ¨è¼¸å¥çURLæ¯å¦æ­£ç¢ºï¼æå®ææéè¿åé¦é ã
views.http404.tips=æ±æ­ï¼ä½ è¨ªåçé é¢ä¸å­å¨æç¡æ¬è¨ªå!
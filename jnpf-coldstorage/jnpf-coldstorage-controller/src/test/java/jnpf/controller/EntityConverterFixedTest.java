package jnpf.controller;

import jnpf.entity.ColdStorageCustomerEntity;
import jnpf.entity.ColdStorageProjectEntity;
import jnpf.entity.DeviceEntity;
import jnpf.model.ColdStorageCustomerVO;
import jnpf.model.ColdStorageProjectVO;
import jnpf.model.DeviceVO;
import jnpf.util.EntityConverter;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Date;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 修复后的EntityConverter测试类
 * 验证所有转换方法是否正常工作
 *
 * @版本： V5.2.0
 * @版权： 杭州多协信息技术有限公司（https://www.duoxieyun.com）
 * @作者： 多协开发组
 * @日期： 2025-01-27
 */
@ExtendWith(MockitoExtension.class)
class EntityConverterFixedTest {

    @Test
    void testToCustomerVO_AllFieldsCopied() {
        // 准备测试数据
        ColdStorageCustomerEntity entity = new ColdStorageCustomerEntity();
        entity.setId("customer-id-123");
        entity.setCustomerCode("CUST001");
        entity.setCustomerName("测试客户");
        entity.setMobilePhone("13800138000");
        entity.setContactPerson("张三");
        entity.setContactPhone("13800138001");
        entity.setCompanyName("测试公司");
        entity.setAddress("测试地址");
        entity.setEnabledMark(1); // 使用基类的enabledMark字段
        entity.setCustomerRemark("测试备注");
        
        Date now = new Date();
        entity.setCreatorTime(now);
        entity.setLastModifyTime(now);

        // 执行转换
        ColdStorageCustomerVO vo = EntityConverter.toCustomerVO(entity);

        // 验证结果
        assertNotNull(vo);
        assertEquals("customer-id-123", vo.getId());
        assertEquals("CUST001", vo.getCustomerCode());
        assertEquals("测试客户", vo.getCustomerName());
        assertEquals("13800138000", vo.getMobilePhone());
        assertEquals("张三", vo.getContactPerson());
        assertEquals("13800138001", vo.getContactPhone());
        assertEquals("测试公司", vo.getCompanyName());
        assertEquals("测试地址", vo.getAddress());
        assertEquals(1, vo.getCustomerStatus()); // 应该正确复制enabledMark
        assertEquals("测试备注", vo.getCustomerRemark());
        assertEquals(now.getTime(), vo.getCreatorTime());
        assertEquals(now.getTime(), vo.getLastModifyTime());
    }

    @Test
    void testToDeviceVO_AllFieldsCopied() {
        // 准备测试数据
        DeviceEntity entity = new DeviceEntity();
        entity.setId("device-id-123");
        entity.setDeviceCode("DEV001");
        entity.setDeviceName("测试设备");
        entity.setDeviceType(1);
        entity.setCustomerId("customer-id-123");
        entity.setStorageId("storage-id-123");
        entity.setDeviceStatus(1);
        entity.setDeviceRemark("测试设备备注");
        
        Date now = new Date();
        entity.setCreatorTime(now);
        entity.setLastModifyTime(now);

        // 执行转换
        DeviceVO vo = EntityConverter.toDeviceVO(entity);

        // 验证结果
        assertNotNull(vo);
        assertEquals("device-id-123", vo.getId());
        assertEquals("DEV001", vo.getDeviceCode());
        assertEquals("测试设备", vo.getDeviceName());
        assertEquals(1, vo.getDeviceType());
        assertEquals("customer-id-123", vo.getCustomerId());
        assertEquals("storage-id-123", vo.getStorageId());
        assertEquals(1, vo.getDeviceStatus());
        assertEquals("测试设备备注", vo.getDeviceRemark());
        
        // 验证时间字段转换（Date -> LocalDateTime）
        assertNotNull(vo.getCreatedTime());
        assertNotNull(vo.getLastModified());
    }

    @Test
    void testToProjectVO_AllFieldsCopied() {
        // 准备测试数据
        ColdStorageProjectEntity entity = new ColdStorageProjectEntity();
        entity.setId("project-id-123");
        entity.setProjectCode("PROJ001");
        entity.setProjectName("测试项目");
        entity.setCompanyName("测试公司");
        entity.setProjectManager("张三");
        entity.setManagerPhone("13800138000");
        entity.setProjectStatus(1);
        entity.setProjectRemark("测试项目备注");
        
        Date now = new Date();
        entity.setCreatorTime(now);
        entity.setLastModifyTime(now);

        // 执行转换
        ColdStorageProjectVO vo = EntityConverter.toProjectVO(entity);

        // 验证结果
        assertNotNull(vo);
        assertEquals("project-id-123", vo.getId());
        assertEquals("PROJ001", vo.getProjectCode());
        assertEquals("测试项目", vo.getProjectName());
        assertEquals("测试公司", vo.getCompanyName());
        assertEquals("张三", vo.getProjectManager());
        assertEquals("13800138000", vo.getManagerPhone());
        assertEquals(1, vo.getProjectStatus());
        assertEquals("测试项目备注", vo.getProjectRemark());
        assertEquals(now.getTime(), vo.getCreatorTime());
        assertEquals(now.getTime(), vo.getLastModifyTime());
    }

    @Test
    void testToCustomerVO_NullEntity() {
        // 测试空实体
        ColdStorageCustomerVO vo = EntityConverter.toCustomerVO(null);
        assertNull(vo);
    }

    @Test
    void testToDeviceVO_NullEntity() {
        // 测试空实体
        DeviceVO vo = EntityConverter.toDeviceVO(null);
        assertNull(vo);
    }

    @Test
    void testToProjectVO_NullEntity() {
        // 测试空实体
        ColdStorageProjectVO vo = EntityConverter.toProjectVO(null);
        assertNull(vo);
    }

    @Test
    void testToCustomerVOList_EmptyList() {
        // 测试空列表
        var result = EntityConverter.toCustomerVOList(null);
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void testToDeviceVOList_EmptyList() {
        // 测试空列表
        var result = EntityConverter.toDeviceVOList(null);
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void testToProjectVOList_EmptyList() {
        // 测试空列表
        var result = EntityConverter.toProjectVOList(null);
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void testToCustomerVO_PartialFields() {
        // 准备测试数据 - 部分字段为空
        ColdStorageCustomerEntity entity = new ColdStorageCustomerEntity();
        entity.setId("customer-id-456");
        entity.setCustomerCode("CUST002");
        entity.setCustomerName("测试客户2");
        // 其他字段保持null

        // 执行转换
        ColdStorageCustomerVO vo = EntityConverter.toCustomerVO(entity);

        // 验证结果
        assertNotNull(vo);
        assertEquals("customer-id-456", vo.getId());
        assertEquals("CUST002", vo.getCustomerCode());
        assertEquals("测试客户2", vo.getCustomerName());
        assertNull(vo.getMobilePhone());
        assertNull(vo.getContactPerson());
        assertNull(vo.getContactPhone());
        assertNull(vo.getCompanyName());
        assertNull(vo.getAddress());
        assertNull(vo.getCustomerStatus());
        assertNull(vo.getCustomerRemark());
        assertNull(vo.getCreatorTime());
        assertNull(vo.getLastModifyTime());
    }

    @Test
    void testToDeviceVO_PartialFields() {
        // 准备测试数据 - 部分字段为空
        DeviceEntity entity = new DeviceEntity();
        entity.setId("device-id-456");
        entity.setDeviceCode("DEV002");
        entity.setDeviceName("测试设备2");
        // 其他字段保持null

        // 执行转换
        DeviceVO vo = EntityConverter.toDeviceVO(entity);

        // 验证结果
        assertNotNull(vo);
        assertEquals("device-id-456", vo.getId());
        assertEquals("DEV002", vo.getDeviceCode());
        assertEquals("测试设备2", vo.getDeviceName());
        assertNull(vo.getDeviceType());
        assertNull(vo.getCustomerId());
        assertNull(vo.getStorageId());
        assertNull(vo.getDeviceStatus());
        assertNull(vo.getDeviceRemark());
        assertNull(vo.getCreatedTime());
        assertNull(vo.getLastModified());
    }
} 
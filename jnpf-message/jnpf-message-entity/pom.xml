<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>jnpf-message</artifactId>
        <groupId>com.jnpf</groupId>
        <version>5.2.0-RELEASE</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>jnpf-message-entity</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.jnpf</groupId>
            <artifactId>jnpf-system-entity</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.dom4j</groupId>
            <artifactId>dom4j</artifactId>
        </dependency>
    </dependencies>


    <profiles>
        <profile>
            <id>boot3</id>
            <activation>
                <jdk>[17,)</jdk>
            </activation>
            <dependencies>
                <dependency>
                    <groupId>com.sun.mail</groupId>
                    <artifactId>jakarta.mail</artifactId>
                </dependency>
            </dependencies>
        </profile>
        <profile>
            <id>boot2</id>
            <activation>
                <jdk>(,17)</jdk>
            </activation>
            <dependencies>
                <dependency>
                    <groupId>com.sun.mail</groupId>
                    <artifactId>javax.mail</artifactId>
                </dependency>
            </dependencies>
        </profile>
    </profiles>

</project>
# 冷库储能用户客户重构设计方案（修正版）

## 业务需求
1. **用户定义**：可登录微信小程序的个人，拥有用户类型，可关联查看多个冷库
2. **客户定义**：企业统称，拥有多个项目，包含多个用户
3. **用户类型**：区分不同类型的用户（冷库客户、投资商等）
4. **关联关系**：用户关联冷库而非项目，项目只和冷库有关联

## 数据模型设计

### 1. 用户类型表 (cs_user_type)
```sql
CREATE TABLE cs_user_type (
    F_ID VARCHAR(50) PRIMARY KEY COMMENT '主键ID',
    F_TYPE_CODE VARCHAR(50) NOT NULL COMMENT '类型编码',
    F_TYPE_NAME VARCHAR(100) NOT NULL COMMENT '类型名称',
    F_TYPE_DESCRIPTION VARCHAR(500) COMMENT '类型描述',
    F_TYPE_STATUS INT DEFAULT 1 COMMENT '类型状态（0-禁用 1-启用）',
    F_SORT_ORDER INT DEFAULT 0 COMMENT '排序序号',
    F_TYPE_REMARK TEXT COMMENT '类型备注',
    UNIQUE KEY UK_TYPE_CODE (F_TYPE_CODE)
);

-- 默认用户类型
INSERT INTO cs_user_type VALUES 
('1', 'COLD_STORAGE_CUSTOMER', '冷库客户', '使用冷库服务的客户用户', 1, 1, NULL),
('2', 'INVESTOR', '投资商', '投资冷库项目的投资商用户', 1, 2, NULL);
```

### 2. 小程序用户表 (cs_miniapp_user)
```sql
-- 原 cs_customer 表重构为用户表
CREATE TABLE cs_miniapp_user (
    F_ID VARCHAR(50) PRIMARY KEY COMMENT '主键ID',
    F_USER_CODE VARCHAR(50) NOT NULL COMMENT '用户编号',
    F_USER_NAME VARCHAR(100) NOT NULL COMMENT '用户姓名',
    F_MOBILE_PHONE VARCHAR(20) NOT NULL COMMENT '手机号（登录账号）',
    F_PASSWORD VARCHAR(255) COMMENT '密码',
    F_PASSWORD_SALT VARCHAR(255) COMMENT '密码盐值',
    F_WECHAT_OPENID VARCHAR(100) COMMENT '微信OpenID',
    F_WECHAT_UNIONID VARCHAR(100) COMMENT '微信UnionID',
    F_AVATAR VARCHAR(500) COMMENT '头像',
    F_USER_TYPE_ID VARCHAR(50) COMMENT '用户类型ID',
    F_USER_STATUS INT DEFAULT 1 COMMENT '用户状态（0-禁用 1-启用）',
    F_USER_REMARK TEXT COMMENT '用户备注',
    UNIQUE KEY UK_USER_CODE (F_USER_CODE),
    UNIQUE KEY UK_MOBILE_PHONE (F_MOBILE_PHONE),
    INDEX IDX_WECHAT_OPENID (F_WECHAT_OPENID),
    INDEX IDX_USER_TYPE_ID (F_USER_TYPE_ID)
);
```

### 2. 客户企业表 (cs_customer_company)
```sql
-- 新增客户企业表
CREATE TABLE cs_customer_company (
    F_ID VARCHAR(50) PRIMARY KEY COMMENT '主键ID',
    F_COMPANY_CODE VARCHAR(50) NOT NULL COMMENT '客户编号',
    F_COMPANY_NAME VARCHAR(200) NOT NULL COMMENT '客户企业名称',
    F_CONTACT_PERSON VARCHAR(100) COMMENT '联系人',
    F_CONTACT_PHONE VARCHAR(20) COMMENT '联系电话',
    F_COMPANY_ADDRESS VARCHAR(500) COMMENT '企业地址',
    F_COMPANY_STATUS INT DEFAULT 1 COMMENT '客户状态（0-禁用 1-启用）',
    F_COMPANY_REMARK TEXT COMMENT '客户备注',
    -- 继承的基础字段
    F_CREATE_TIME DATETIME COMMENT '创建时间',
    F_CREATE_USER VARCHAR(50) COMMENT '创建人',
    F_UPDATE_TIME DATETIME COMMENT '更新时间',
    F_UPDATE_USER VARCHAR(50) COMMENT '更新人',
    F_DELETE_MARK INT DEFAULT 0 COMMENT '删除标记',
    F_DELETE_TIME DATETIME COMMENT '删除时间',
    F_DELETE_USER VARCHAR(50) COMMENT '删除人',
    UNIQUE KEY UK_COMPANY_CODE (F_COMPANY_CODE)
);
```

### 3. 项目表更新 (cs_project)
```sql
-- 项目表添加客户企业关联
ALTER TABLE cs_project ADD COLUMN F_COMPANY_ID VARCHAR(50) COMMENT '所属客户企业ID';
ALTER TABLE cs_project ADD INDEX IDX_COMPANY_ID (F_COMPANY_ID);
```

### 4. 用户冷库关联表 (cs_user_storage_relation)
```sql
-- 用户与冷库的关联关系（替换原项目关联）
CREATE TABLE cs_user_storage_relation (
    F_ID VARCHAR(50) PRIMARY KEY COMMENT '主键ID',
    F_USER_ID VARCHAR(50) NOT NULL COMMENT '用户ID',
    F_STORAGE_ID VARCHAR(50) NOT NULL COMMENT '冷库ID',
    F_PERMISSION_TYPE INT DEFAULT 0 COMMENT '权限类型（0-只读 1-管理）',
    F_RELATION_STATUS INT DEFAULT 1 COMMENT '关联状态（0-禁用 1-启用）',
    F_RELATION_REMARK TEXT COMMENT '关联备注',
    UNIQUE KEY UK_USER_STORAGE (F_USER_ID, F_STORAGE_ID),
    INDEX IDX_USER_ID (F_USER_ID),
    INDEX IDX_STORAGE_ID (F_STORAGE_ID)
);
```

### 5. 用户客户关联表 (cs_user_company_relation)
```sql
-- 新增用户与客户企业关联表
CREATE TABLE cs_user_company_relation (
    F_ID VARCHAR(50) PRIMARY KEY COMMENT '主键ID',
    F_USER_ID VARCHAR(50) NOT NULL COMMENT '用户ID',
    F_COMPANY_ID VARCHAR(50) NOT NULL COMMENT '客户企业ID',
    F_RELATION_TYPE INT DEFAULT 1 COMMENT '关联类型（1-员工 2-管理员）',
    F_RELATION_STATUS INT DEFAULT 1 COMMENT '关联状态（0-禁用 1-启用）',
    F_RELATION_REMARK TEXT COMMENT '关联备注',
    -- 继承的基础字段
    F_CREATE_TIME DATETIME COMMENT '创建时间',
    F_CREATE_USER VARCHAR(50) COMMENT '创建人',
    F_UPDATE_TIME DATETIME COMMENT '更新时间',
    F_UPDATE_USER VARCHAR(50) COMMENT '更新人',
    F_DELETE_MARK INT DEFAULT 0 COMMENT '删除标记',
    F_DELETE_TIME DATETIME COMMENT '删除时间',
    F_DELETE_USER VARCHAR(50) COMMENT '删除人',
    UNIQUE KEY UK_USER_COMPANY (F_USER_ID, F_COMPANY_ID),
    INDEX IDX_USER_ID (F_USER_ID),
    INDEX IDX_COMPANY_ID (F_COMPANY_ID)
);
```

## 实体关系说明

1. **用户 ↔ 用户类型**：多对一关系（一个用户属于一种类型，一种类型包含多个用户）
2. **用户 ↔ 客户企业**：多对多关系（一个用户可属于多个企业，一个企业有多个用户）
3. **用户 ↔ 冷库**：多对多关系（一个用户可查看多个冷库，一个冷库可被多个用户查看）
4. **客户企业 ↔ 项目**：一对多关系（一个企业有多个项目，一个项目属于一个企业）
5. **项目 ↔ 冷库**：一对多关系（一个项目包含多个冷库，一个冷库属于一个项目）

## 重构步骤

1. 创建用户类型管理实体和相关代码
2. 重构现有客户实体为用户实体，添加用户类型字段
3. 新增客户企业实体和相关代码
4. 更新项目实体，添加客户企业关联
5. 创建用户冷库关联表，替换原用户项目关联
6. 新增用户客户企业关联表
7. 更新所有相关的Service、Controller、Mapper
8. 创建数据迁移脚本
9. 初始化默认用户类型（冷库客户、投资商）
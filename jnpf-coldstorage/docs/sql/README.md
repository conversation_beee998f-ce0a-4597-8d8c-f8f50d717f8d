# 冷库储能管理系统数据库部署指南

## 概述

本文档提供冷库储能管理系统数据库的完整部署和配置指南。

## 文件说明

### 核心SQL文件

1. **`complete_database_init.sql`** - 完整数据库初始化脚本
   - 包含所有表结构创建
   - 包含索引、约束、视图创建
   - 适用于全新部署

2. **`sample_data_init.sql`** - 示例数据初始化脚本
   - 包含测试和演示数据
   - 适用于开发和测试环境

### 历史SQL文件（已整合）

以下文件内容已整合到上述核心文件中，保留作为参考：

- `init_coldstorage.sql` - 原基础表结构
- `device_management.sql` - 设备管理模块
- `electricity_price_config.sql` - 电价配置模块  
- `fault_management.sql` - 故障管理模块
- `update_password_security.sql` - 密码安全升级
- `user_customer_refactor_migration.sql` - 用户客户重构

## 部署步骤

### 环境要求

- MySQL 8.0+ 或 MariaDB 10.5+
- 数据库字符集：`utf8mb4`
- 数据库排序规则：`utf8mb4_general_ci`

### 第一步：创建数据库

```sql
CREATE DATABASE coldstorage_db 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_general_ci;

USE coldstorage_db;
```

### 第二步：执行核心初始化脚本

```bash
# 方式1：使用MySQL命令行
mysql -u root -p coldstorage_db < complete_database_init.sql

# 方式2：使用source命令
mysql -u root -p
USE coldstorage_db;
SOURCE /path/to/complete_database_init.sql;
```

### 第三步：插入示例数据（可选）

仅在开发和测试环境执行：

```bash
mysql -u root -p coldstorage_db < sample_data_init.sql
```

### 第四步：验证部署

```sql
-- 检查表是否创建成功
SHOW TABLES;

-- 检查数据是否插入成功（如果执行了示例数据脚本）
SELECT 
  'cs_project' as table_name, COUNT(*) as record_count FROM cs_project
UNION ALL
SELECT 'cs_cold_storage', COUNT(*) FROM cs_cold_storage
UNION ALL  
SELECT 'cs_device', COUNT(*) FROM cs_device
UNION ALL
SELECT 'cs_miniapp_user', COUNT(*) FROM cs_miniapp_user;
```

## 数据库结构说明

### 核心业务表

1. **项目管理**
   - `cs_project` - 冷库储能项目
   - `cs_cold_storage` - 冷库信息

2. **用户管理**
   - `cs_user_type` - 用户类型字典
   - `cs_miniapp_user` - 小程序用户
   - `cs_customer_company` - 客户企业信息

3. **关联关系**
   - `cs_user_company_relation` - 用户企业关联
   - `cs_user_storage_relation` - 用户冷库关联

4. **设备管理**
   - `cs_device` - 设备信息

5. **电价配置**
   - `cs_electricity_price_config` - 电价配置主表
   - `cs_electricity_time_period` - 时间段电价

6. **故障管理**
   - `cs_fault_type_dict` - 故障类型字典
   - `cs_fault_management` - 故障管理

### 业务视图

1. **`v_project_storage_stats`** - 项目冷库统计
2. **`v_user_storage_access`** - 用户冷库权限
3. **`v_fault_management`** - 故障管理详情

## 数据关系图

```
cs_project (项目)
    ↓ 1:N
cs_cold_storage (冷库)
    ↓ N:M (通过cs_user_storage_relation)
cs_miniapp_user (用户)
    ↓ N:M (通过cs_user_company_relation)  
cs_customer_company (客户企业)

cs_device (设备) → cs_cold_storage (所属冷库)
cs_electricity_price_config → cs_customer_company (客户企业)
cs_fault_management → cs_cold_storage (故障冷库)
```

## 安全配置建议

### 1. 数据库用户权限

```sql
-- 创建应用专用数据库用户
CREATE USER 'coldstorage_app'@'%' IDENTIFIED BY 'strong_password';

-- 授予必要权限
GRANT SELECT, INSERT, UPDATE, DELETE ON coldstorage_db.* TO 'coldstorage_app'@'%';

-- 刷新权限
FLUSH PRIVILEGES;
```

### 2. 连接配置

- 使用SSL连接
- 配置连接池最大连接数
- 设置合理的连接超时时间

### 3. 备份策略

```bash
# 定期备份脚本示例
mysqldump -u root -p --single-transaction --routines --triggers \
  coldstorage_db > backup_$(date +%Y%m%d_%H%M%S).sql
```

## 性能优化建议

### 1. 索引优化

核心表已包含必要索引，根据实际查询需求可添加复合索引：

```sql
-- 示例：为常用查询创建复合索引
CREATE INDEX idx_storage_type_status ON cs_cold_storage(F_STORAGE_TYPE, F_STORAGE_STATUS);
CREATE INDEX idx_device_customer_status ON cs_device(F_CUSTOMER_ID, F_DEVICE_STATUS);
```

### 2. 分区建议

对于大数据量表考虑分区：

```sql
-- 故障管理表按月分区示例
ALTER TABLE cs_fault_management PARTITION BY RANGE (YEAR(F_CREATE_TIME)*100 + MONTH(F_CREATE_TIME))
(
    PARTITION p202501 VALUES LESS THAN (202502),
    PARTITION p202502 VALUES LESS THAN (202503),
    -- 继续添加分区...
    PARTITION p_future VALUES LESS THAN MAXVALUE
);
```

### 3. 查询优化

- 使用预定义视图简化复杂查询
- 避免SELECT * 语句  
- 合理使用LIMIT分页
- 定期分析慢查询日志

## 数据迁移指南

### 从旧版本升级

如果从早期版本升级，请按以下顺序执行：

1. 备份现有数据
2. 执行`update_password_security.sql`（如需要）
3. 执行`user_customer_refactor_migration.sql`（如需要）
4. 数据验证和清理

### 数据导入导出

```bash
# 导出特定表数据
mysqldump -u root -p --no-create-info coldstorage_db cs_project cs_cold_storage > data_export.sql

# 导入数据到新环境
mysql -u root -p new_coldstorage_db < data_export.sql
```

## 监控和维护

### 1. 性能监控

```sql
-- 检查表大小
SELECT 
    TABLE_NAME,
    ROUND(((DATA_LENGTH + INDEX_LENGTH) / 1024 / 1024), 2) AS 'Size(MB)'
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = 'coldstorage_db'
ORDER BY (DATA_LENGTH + INDEX_LENGTH) DESC;

-- 检查索引使用情况
SELECT 
    OBJECT_SCHEMA,
    OBJECT_NAME,
    INDEX_NAME,
    COUNT_READ,
    COUNT_WRITE
FROM performance_schema.table_io_waits_summary_by_index_usage 
WHERE OBJECT_SCHEMA = 'coldstorage_db'
ORDER BY COUNT_READ DESC;
```

### 2. 定期维护

```sql
-- 优化表
OPTIMIZE TABLE cs_device, cs_fault_management, cs_electricity_time_period;

-- 分析表
ANALYZE TABLE cs_project, cs_cold_storage, cs_miniapp_user;
```

## 故障排除

### 常见问题

1. **字符集问题**
   ```sql
   -- 检查字符集
   SHOW CREATE TABLE cs_miniapp_user;
   
   -- 修改字符集
   ALTER TABLE table_name CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;
   ```

2. **外键约束错误**
   ```sql
   -- 临时禁用外键检查
   SET FOREIGN_KEY_CHECKS = 0;
   -- 执行操作
   SET FOREIGN_KEY_CHECKS = 1;
   ```

3. **权限问题**
   ```sql
   -- 检查用户权限
   SHOW GRANTS FOR 'coldstorage_app'@'%';
   ```

### 日志分析

- 检查MySQL错误日志
- 监控慢查询日志
- 分析binlog（如启用）

## 联系支持

如遇问题，请提供以下信息：

- MySQL版本：`SELECT VERSION();`
- 错误日志内容
- 具体操作步骤和报错信息
- 数据库配置信息（去敏感信息）

---

**注意事项：**
- 生产环境部署前请充分测试
- 定期备份数据库
- 监控数据库性能指标
- 及时更新数据库版本和安全补丁
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="jnpf.mapper.TrainLibraryEssayQuestionMapper">
    <select id="getListByParentId" resultType="jnpf.entity.TrainLibraryEssayQuestionEntity">
        select f_id        as id,
               f_bank_id   as fbankId,
               f_full_name as ffullName,
               f_answer    as fanswer,
               f_result    as fresult
        from make_train_library_essay_question
        where f_bank_id = #{parentId}
    </select>

    <update id="updateResult">
        <foreach collection="items" item="item" separator=";">
            update make_train_library_essay_question
            set f_result = #{item.result}
            where f_id = #{item.id}
        </foreach>
    </update>
</mapper>
